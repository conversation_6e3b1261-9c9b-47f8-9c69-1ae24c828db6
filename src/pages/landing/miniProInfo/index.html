<!DOCTYPE html>
<html lang="zh">

<head>
  <title>参与活动抢好礼</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
  <meta http-equiv="expires" content="-1">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- <link rel="stylesheet" href="//cjwxpp.dianpusoft.cn/resources/wxjs/gallery/ibd/ibd.min.css?8098"> -->
  <style>
    a,
    abbr,
    acronym,
    address,
    applet,
    article,
    aside,
    audio,
    b,
    big,
    blockquote,
    body,
    button,
    canvas,
    caption,
    center,
    cite,
    code,
    dd,
    del,
    details,
    dfn,
    div,
    dl,
    dt,
    em,
    embed,
    fieldset,
    figcaption,
    figure,
    footer,
    form,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    header,
    hgroup,
    html,
    i,
    iframe,
    img,
    ins,
    kbd,
    label,
    legend,
    li,
    mark,
    menu,
    nav,
    object,
    ol,
    output,
    p,
    pre,
    q,
    ruby,
    s,
    samp,
    section,
    small,
    span,
    strike,
    strong,
    sub,
    summary,
    sup,
    table,
    tbody,
    td,
    tfoot,
    th,
    thead,
    time,
    tr,
    tt,
    u,
    ul,
    var,
    video {
      margin: 0;
      padding: 0;
      border: 0;
      vertical-align: baseline;
      background: 0 0;
      outline: 0;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    hgroup,
    menu,
    nav,
    section {
      display: block
    }

    ol,
    ul {
      list-style: none
    }

    button {
      background: 0 0
    }

    blockquote,
    q {
      quotes: none
    }

    blockquote:after,
    blockquote:before,
    q:after,
    q:before {
      content: '';
      content: none
    }

    strong {
      font-weight: 700
    }

    table {
      border-collapse: collapse;
      border-spacing: 0
    }

    img {
      border: 0;
      max-width: 100%
    }

    html {
      line-height: initial
    }

    body {
      font-size: .32rem;
      font-family: "Helvetica Neue", Helvetica, sans-serif
    }

    a {
      text-decoration: none
    }
  </style>
  <style>
    #wrap {
      background: #ec453f url(//img10.360buyimg.com/imgzone/jfs/t1/142529/8/21182/613530/619f4d59E77f94d7c/14b5e99359f2b319.png) no-repeat;
      background-size: 100%;
      width: 100vw;
      max-width: 750px;
      height: 13.34rem;
      min-height: 100vh;
      margin: 0 auto;
      padding-top: 1.9rem;
      box-sizing: border-box;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      position: relative;
    }

    /*#wrap button {*/
    /*    width:4rem;*/
    /*    height:0.9rem;*/
    /*    background: url("//img10.360buyimg.com/imgzone/jfs/t1/151671/33/17246/13946/601cc065Eaf43de4b/7b3778e7d0150716.png") center / 100% 100%;*/
    /*    font-size: 0;*/
    /*    position: absolute;*/
    /*    top: 9.7rem;*/
    /*    left: 50%;*/
    /*    transform: translateX(-50%);*/
    /*}*/
  </style>
  <script type="text/javascript" src="//cjwxpp.dianpusoft.cn/resources/wxjs/htmlFontSize.min.js?8098"></script>
</head>

<body>
  <div id="wrap">

  </div>

  <!-- <script src="//wq.360buyimg.com/js/common/dest/m_common_merge.min.js" type="text/javascript"></script> -->
</body>

</html>
