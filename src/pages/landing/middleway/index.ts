import { createApp } from 'vue';
import { initOnlyLogin } from '@/utils';
import { initRem } from '@/utils/platforms/client';
import index from './index.vue';
import '@/style';
import { InitRequest } from '@/types/InitRequest';

initRem();
const app = createApp(index);
const config: InitRequest = {
  disableShare: true,
  activityType: '99999',
  activityId: '99999',
  templateCode: '99999',
};

initOnlyLogin(config).then(async ({ userInfo }) => {
  app.provide('userInfo', userInfo);
  app.mount('#app');
});
