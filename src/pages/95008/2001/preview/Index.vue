<template>
  <div class="bg" :style="furnishStyles.pageBg.value"  v-if="isLoadingFinish">
    <div class="header-kv select-hover"  >
      <img
        :src="furnish.pageBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/51180/8/24389/92003/66a884dbFe589c4c6/924d5e8d3c95ec6e.png'"
        alt=""
        draggable="false"
        class="kv-img" />
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
         <div>
           <div class="header-btn" :style="furnishStyles.headerBtn.value"  v-for="(btn, index) in btnList" :key="index" @click="btn.event">
             {{ btn.name }}
           </div>
         </div>
      </div>
      <div class="content">
        <div class="card-list">
          <div v-for="(item, index) in cardList" :key="index" class="card-item">
<!--            <div class="hasNum">-->
<!--              <span>0</span>-->
<!--            </div>-->
          </div>
        </div>
        <div class="draw-btn" @click="toast">点击抽拼图</div>
      </div>
      <img
        :src="furnish.prizeBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/67335/19/27918/185038/66aafc3eF56e8d142/a2a32d8e5d5b924c.png'"
        alt=""
        draggable="false"
        class="kv-img" />
      <img
        src="//img10.360buyimg.com/imgzone/jfs/t1/18635/8/21661/7033/66aaf8f0F4a54e8d1/4348aaa1908cc05f.png"
        alt=""
        @click="toast"
        draggable="false"
        style="padding-bottom: 1rem;"
        class="kv-img" />
    </div>
    <VanPopup teleport="body" v-model:show="showRule" position="center">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="center">
      <MyPrize v-if="showMyPrize"  @close="showMyPrize = false"></MyPrize>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const isStart = ref(true);

const isLoadingFinish = ref(false);

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const cardList = ref<any>([
  {
    cardImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/244405/14/15071/5380/66a8cb7fF80536421/ea0d9f97321dbb1e.png',
  },
  {
    cardImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/244405/14/15071/5380/66a8cb7fF80536421/ea0d9f97321dbb1e.png',
  },
  {
    cardImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/244405/14/15071/5380/66a8cb7fF80536421/ea0d9f97321dbb1e.png',
  },

]);
const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '我的奖品',
    event: () => {
      showMyPrize.value = true;
    },
  },
  {
    name: '分享好友',
    event: () => {
      showToast('活动预览，仅供查看');
    },
  },
];

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);

const createImg = async () => {
  showRule.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};
// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});

// 活动数据监听
registerHandler('activity', (data) => {
  console.log('activity', data);
  document.title = data.activityName;
  if (data.cardList.length) {
    cardList.value = data.cardList.filter((item: any) => item.cardImg);
  }
  ruleTest.value = data.rules;
});

// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});
const toast = () => {
  showToast('活动预览，仅供查看');
};
onMounted(() => {
  if (activityData) {
    const data = activityData;
    document.title = data.activityName;
    ruleTest.value = data.rules;
    if (data.cardList.length) {
      cardList.value = data.cardList.filter((item: any) => item.cardImg);
    }
  }
  if (decoData) {
    console.log('decoData', decoData);

    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

</script>
<style lang="scss" scoped>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: cover;
  min-height: 100vh;
  user-select: none;
  -webkit-user-drag: none;
}
.header-kv {
  position: relative;
  .kv-img {
    width: 100%;
  }
  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0;
    display: flex;
    justify-content: flex-end;
    .create-img {
      .header-btn {
        div {
          margin-top: -0.18rem;
        }
      }
    }
    .header-btn {
      padding: 0 0.2rem;
      height: 0.48rem;
      margin-bottom: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 0.24rem;
    }
  }
  .content {
    position: absolute;
    top: 8rem;
    width:100%;
    .card-list {
      width: 90%;
      margin: 0 auto;
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 2.5rem;
      .card-item {
        width: 2rem;
        height: 2.1rem;
        position: relative;
        .hasNum {
          width: 0.8rem;
          height: 0.8rem;
          background: url("https://img10.360buyimg.com/imgzone/jfs/t1/242145/36/15005/3559/66aaf8efF22352351/c55d46624e3d3f77.png") no-repeat;
          background-size: 100%;
          position: absolute;
          top: 0;
          right: -0.16rem;
          z-index: 1;
          font-size: 0.25rem;
          color: #fff;
          span {
            display: inline-block;
            width: 0.7rem;
            height: 0.7rem;
            line-height: 0.6rem;
            text-align: center;
            font-size: 0.25rem;
            color: #fff;
          }
        }
      }
    }
    .draw-btn {
      width: 4.86rem;
      margin: 0.5rem auto 0;
      height: 1.06rem;
      line-height: 0.8rem;
      background: url("https://img10.360buyimg.com/imgzone/jfs/t1/31539/27/21851/14317/66aaf8efFd1ec59a9/84bec6e88e8e23e8.png") no-repeat;
      background-size: 100%;
      font-size: 0.36rem;
      color: #fff;
      text-align: center;
    }
  }
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
