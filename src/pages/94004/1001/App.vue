<template>
  <div>
    <div class="bg" :style="currentPage" v-if="isLoadingFinish">
      <div class="header-kv">
        <div class="rule-btn" v-click-track="'hdgz'" :style="{top: !isJdChannel ? '1.95rem' : '3.95rem'}" @click="showRule = true"></div>
      </div>
      <div v-if="!isJdChannel" class="moreAct" v-click-track="'jdcyqthd'" @click="jump(furnish.NonsupportShopLink)"></div>
      <div v-else>
        <!--0-立即申领 1-奖品领完 2-填写收货信息 3-已领取-->
        <div v-if="status === 0 && !failed" v-click-track="'ljsl'" class="getPrize" @click="getPrizes"></div>
        <div v-if="status === 1" class="noPrize"></div>
        <div v-if="status === 2" class="successBox">
          <div class="addAddressBtn" v-click-track="'txshxx'" @click="showSaveAddress = true"></div>
        </div>
        <div v-if="status === 3" class="success"></div>
        <div v-if="failed" class="failedBox">
          <div v-if="level === 1 || level === 0" class="toShopBtn" v-click-track="'ljjd'" @click="jump(furnish.failedLinkPink)"></div>
          <div v-if="level === 2" class="toShopBtn" v-click-track="'ljjd'" @click="jump(furnish.failedLinkSilver)"></div>
          <div v-if="level === 3" class="toShopBtn" v-click-track="'ljjd'" @click="jump(furnish.failedLinkGold)"></div>
        </div>
      </div>
    </div>
  </div>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <AddressPopup v-if="showSaveAddress" @close="closeAddressCallBack"></AddressPopup>
  </VanPopup>
</template>
<script setup lang="ts">
import { inject, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import RulePopup from './components/RulePopup.vue';
import AddressPopup from './components/AddressPopup.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast, Toast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const isLoadingFinish = ref(false);

const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};

const jump = (url: any) => {
  window.location.href = url;
};

// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 地址弹窗
const showSaveAddress = ref(false);
// 获取规则
const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    ruleTest.value = data;
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

const showMainPage = ref(furnishStyles.pageBgPink.value);
const showSuccessPage = ref(furnishStyles.successBgPink.value);
const showFailedPage = ref(furnishStyles.failedBgPink.value);
const showNonPage = ref(furnishStyles.nonsupportShopBg.value);
// 是否是京东渠道客户
const isJdChannel = ref(false);
// 会员等级 0 1 粉卡  2 银卡  3 金卡
const level = ref(0);
// 页面状态  0-立即申领 1-奖品领完 2-填写收货信息 3-已领取
const status = ref(1);
// 领取失败
const failed = ref(false);

const getLevelBg = async () => {
  if (isJdChannel.value) {
    if (level.value === 3) {
      showMainPage.value = furnishStyles.pageBgGold.value;
      showSuccessPage.value = furnishStyles.successBgGold.value;
      showFailedPage.value = furnishStyles.failedBgGold.value;
      console.log('金卡');
    }
    if (level.value === 2) {
      showMainPage.value = furnishStyles.pageBgSilver.value;
      showSuccessPage.value = furnishStyles.successBgSilver.value;
      showFailedPage.value = furnishStyles.failedBgSilver.value;
      console.log('银卡');
    }
    if (level.value === 1 || level.value === 0) {
      showMainPage.value = furnishStyles.pageBgPink.value;
      showSuccessPage.value = furnishStyles.successBgPink.value;
      showFailedPage.value = furnishStyles.failedBgPink.value;
      console.log('粉卡');
    }
  } else {
    showNonPage.value = furnishStyles.nonsupportShopBg.value;
  }
};

const currentPage = ref();
const currentPageStyle = async () => {
  if (status.value === 0 || status.value === 1 || status.value === 3) {
    currentPage.value = showMainPage.value;
  } if (status.value === 2) {
    currentPage.value = showSuccessPage.value;
  } if (failed.value) {
    currentPage.value = showFailedPage.value;
  } if (isJdChannel.value === false) {
    currentPage.value = showNonPage.value;
  }
};

const getActivity = async () => {
  try {
    const { data, code } = await httpRequest.post('/94004/activity');
    console.log(data, code, 'ackMessage');
    if (code === 200) {
      isJdChannel.value = data.isJdChannel;
      level.value = data.level;
      status.value = data.status;
      await getLevelBg();
      await currentPageStyle();
    }
    isLoadingFinish.value = true;
  } catch (error) {
    isLoadingFinish.value = true;
    showToast(error.message);
    console.error(error);
  }
};

const closeAddressCallBack = () => {
  showSaveAddress.value = false;
  getActivity();
};

// 领取奖品
const getPrizes = async () => {
  if (!isStart.value && !isEnd.value) {
    showToast('活动未开始');
    return;
  }
  if (isEnd.value) {
    showToast('活动已结束');
    return;
  }
  try {
    const { data, code } = await httpRequest.post('/94004/sendPrize');
    console.log(data, code, 'ackMessage');
    if (code === 200) {
      await getActivity();
    }
  } catch (error) {
    showToast(error.message);
    if (error.message === '会员上月未升级' || error.message === '您未满足升级礼领取条件') {
      failed.value = true;
      await getLevelBg();
      await currentPageStyle();
    }
    console.error(error);
  }
};

// 初始化
const init = async () => {
  if (baseInfo.thresholdLevelsStatus === 3) {
    lzReportClick('join');
    window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    if (!isStart.value && !isEnd.value) {
      showToast('活动未开始');
    }
    if (isEnd.value) {
      showToast('活动已结束');
    }
    await Promise.all([getRule(), getActivity()]);
    // 两秒后关闭
    setTimeout(() => {
      closeToast();
    }, 2000);
  } catch (error) {
    closeToast();
  }
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>

<style>
@font-face {
  font-family: 'FZLTHJW';
  src: url("./font/fzlthjwgb10.TTF") format('TTF');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'FZLTHJW';
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom:0.2rem;
}

.getPrize {
  margin: 1.3rem auto;
  width: 2.6rem;
  height: 0.58rem;
  background-size: 100%;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/243337/31/15211/7225/669a233eFdf3d2b52/dea172897c4da9b5.png);
}

.success {
  margin: 1.3rem auto;
  width: 2.01rem;
  height: 0.58rem;
  background-size: 100%;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/53549/40/25541/6311/669a2336F2afb2a5c/91920f27b3987164.png);
}

.noPrize{
  margin: 1.3rem auto;
  width: 2.01rem;
  height: 0.58rem;
  background-size: 100%;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/244483/8/15406/6114/669a2336F87f73620/81cee04de35d4cf6.png);
}

.successBox {
  margin: 0.1rem auto;
  width: 6.42rem;
  height: 3.26rem;
  background-size: 100%;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/6375/5/34843/9537/669a233eF5dc408f3/74ed3783b925dc14.png);
  padding-top: 1.96rem;
  .addAddressBtn {
    margin: 0 auto;
    width: 3.95rem;
    height: 0.7rem;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/39891/29/21773/8214/669a2335F662e5ff9/e690852615f0c165.png);
  }
}

.failedBox {
  margin: 0.1rem auto;
  width: 6.42rem;
  height: 3.26rem;
  background-size: 100%;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/232336/13/21558/24123/669e07fcF50be8dce/44d9d88bf1ad754d.png);
  padding-top: 1.96rem;
  .toShopBtn {
    margin: 0 auto;
    width: 4.63rem;
    height: 0.58rem;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/55013/12/23801/10535/669a233eF6da88ea1/cc3cf19134c33e9d.png);
  }
}

.moreAct {
  margin: 0 auto;
  width: 3.95rem;
  height: 0.58rem;
  background-size: 100%;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/249744/34/15406/10687/669a233dF612a8927/26f2eb88e1143352.png);
}

.header-kv {
  position: relative;
  height: 9rem;
  .rule-btn {
    position: absolute;
    right: 0;
    width: 0.58rem;
    height: 1.62rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/57511/16/24509/2299/669a2338Fdfc10d2c/6bbb3298f8d39b50.png) no-repeat;
    background-size: 100%;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
.van-popup--center {
  max-width: 100%;
}
</style>
