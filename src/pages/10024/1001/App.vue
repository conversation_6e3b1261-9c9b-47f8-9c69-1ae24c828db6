<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRulePopup" v-click-track="'hdgz'"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true" v-click-track="'wdjp'"><div>我的奖品</div></div>
        </div>
      </div>
    </div>
    <div class="prizeClassAll">
      <div class="addCartTypeDiv">
        <img class="labaImg"  src="//img10.360buyimg.com/imgzone/jfs/t1/118773/38/21802/666/61988d7eEfaba26f0/cffd5c3036db2206.png" alt="" />
        <div class="addCartTypeDetailDiv" :style="furnishStyles.cartPromptColor.value" >加购<span :style="furnishStyles.cartTypeColor.value">{{addInfoData.skuNum}}件</span>宝贝可领取礼品</div>
      </div>
      <div class="prizeDetailDiv" :style="furnishStyles.drawPrizeBg.value" v-if="furnish.isShowRealPrize === 1">
        <div class="prizeContentDiv">
          <div class="leftDiv">
            <img :src="addInfoData.img" alt="" />
            <div class="backDiv">{{prizeType[addInfoData.prizeType]}}</div>
          </div>
          <div class="rightDiv">
            <div class="prizeName">{{addInfoData.prizeName}}</div>
            <div class="prizeAllRest">奖品剩余:{{addInfoData.surplus}}份</div>
<!--            <div class="prizeTodayRest">今日奖品剩余:{{addInfoData.todayNum === '不限' ? addInfoData.todayNum : addInfoData.todayNum + '份'}}</div>-->
          </div>
        </div>
      </div>
      <div v-if="addInfoData.addWay === 2">
        <div class="allAddCartBtnDiv" :style="furnishStyles.addCartBtnBg.value" v-if="addInfoData.status===1" v-threshold-click="() => drawPrize(addInfoData)">领取奖品</div>
        <div class="allAddCartBtnDiv" :style="furnishStyles.addCartBtnBg.value" v-else-if="addInfoData.status===2" v-threshold-click="allCartClick">一键加购所有商品</div>
        <div class="allAddCartBtnDiv" :style="furnishStyles.addCartGrayBtnBg.value" v-else-if="addInfoData.status===3">已领取</div>
      </div>
      <div v-else-if="addInfoData.addWay === 1">
        <div class="allAddCartBtnDiv" :style="furnishStyles.addCartBtnBg.value" v-if="addInfoData.status===1" v-threshold-click="() => drawPrize(addInfoData)">领取奖品</div>
        <div class="allAddCartBtnDiv" :style="furnishStyles.addCartGrayBtnBg.value" v-else-if="addInfoData.status===3">已领取</div>
      </div>
    </div>
    <div class="sku">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in addSkuList" :key="index" @click.stop="gotoSkuPage(item.skuId)">
          <div class="goodImgDiv">
            <img :src="item.skuMainPicture" alt="">
          </div>
          <div class="sku-text">{{item.skuName}}</div>
          <div class="bottomDiv">
            <div class="priceDiv">{{item.jdPrice}}</div>
            <div class="sku-btns" v-if="addInfoData.addWay === 1 && item.addStatus === 1" :style="furnishStyles.stepAddCartBtnBg.value" v-threshold-click="stepCartClick(item)"></div>
            <div class="sku-btns" v-else-if="addInfoData.addWay === 1 && item.addStatus === 2" :style="furnishStyles.stepAddCartGrayBtnBg.value"></div>
          </div>
        </div>
        <div class="more-btn-all" v-if="pageNum < pagesAll">
          <div class="more-btn" @click.stop="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>

    <div class="bottom-shop-share select-hover">
      <div class="to-shop" :style="furnishStyles.btnToShop.value" @click="gotoShopPage(baseInfo.shopId)" />
      <div class="share-friends" :style="furnishStyles.btnShare.value" @click="shareAct" />
    </div>
    <VanPopup teleport="body" v-model:show="rulePopup" position="bottom" :closeOnClickOverlay="false">
      <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="myPrizePopup" position="bottom" :closeOnClickOverlay="false">
      <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="awardPopup">
      <Award :prize="award" @close="awardPopup = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></Award>
    </VanPopup>
<!--     保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom" :closeOnClickOverlay="false">
      <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :userPrizeId="userPrizeId" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup" :closeOnClickOverlay="false">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
    </VanPopup>
    <!-- 领取京元宝权益 -->
    <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom" :closeOnClickOverlay="false">
      <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
    </VanPopup>

  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import Award from './components/AwardPopup.vue';
import SaveAddress from './components/SaveAddress.vue';
import CopyCard from './components/CopyCard.vue';
import SavePhone from './components/SavePhone.vue';
import dayjs from 'dayjs';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { AddInfoData, CardType } from './ts/type';
import { defaultAddInfoData, prizeType } from './ts/default';

import { addSkuToCart, gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const shopName = ref(baseInfo.shopName);
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
  addStatus: number;
};
const addSkuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

const chanceNum = ref(0); // 抽奖次数
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const endTime = ref(0);
const isEnd = ref(false);
const startTime = ref(0);
const isStart = ref(false);
const addInfoData = ref<AddInfoData>(defaultAddInfoData);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error) {
    console.error();
  }
};

const awardPopup = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string, userPrizeId1: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  userPrizeId.value = userPrizeId1;
  awardPopup.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  awardPopup.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  awardPopup.value = false;
  myPrizePopup.value = false;
  savePhonePopup.value = true;
};

const getExposureSkuData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10024/getExposureSku', {
      pageNum: pageNum.value,
      pageSize: 100,
      type: 1,
    });
    closeToast();
    // if (res.data.records && res.data.records.length > 0) {
    //   addSkuList.value.push(...res.data.records);
    // }
    addSkuList.value = res.data.records as any[];
    if (addInfoData.value.skuId && addInfoData.value.skuId.length > 0) {
      addSkuList.value.forEach((item, index) => {
        item.addStatus = 1; // 1 未加购 2 已加购
        addInfoData.value.skuId.forEach((item1, index1) => {
          if (item1 === item.skuId) {
            item.addStatus = 2;
          }
        });
      });
      // console.log(addSkuList.value, 'addSkuList.valueaddSkuList.value');
    } else {
      addSkuList.value.forEach((item, index) => {
        item.addStatus = 1; // 1 未加购 2 已加购
      });
    }

    pagesAll.value = res.data.pages;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getExposureSkuData();
};
// 获取奖品信息
const addInfo = async () => {
  try {
    const { data } = await httpRequest.post('/10024/addInfo');
    addInfoData.value = data;
    console.log(data, 'prizeInfoprizeInfo');
    await getExposureSkuData();
  } catch (error) {
    console.error(error);
  }
};
// 点击立即抽奖按钮
const drawPrize = async (prizeData:any) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10024/sendPrize');
    // const data = {
    //   activityPrizeId: '1820334376899821570',
    //   result: {
    //     activityPrizeId: '1820334376899821570',
    //     prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229812/4/22258/2537/66a9fdffF93ca3a0a/9a1665c2cf72b465.png',
    //     prizeName: 'Crxtest001',
    //     prizeType: 3,
    //     result: {
    //       result: 'o240805134520910000',
    //     },
    //     sortId: 0,
    //     status: 1,
    //     userPrizeId: '1820335256896724993',
    //   },
    //   userPrizeId: '1820335256896724993',
    // };
    closeToast();
    // addSkuList.value = [];
    await addInfo();
    data.prizeType = data.result.prizeType;
    data.prizeName = data.result.prizeName;
    data.showImg = data.result.prizeImg;
    award.value = data;
    awardPopup.value = true;
  } catch (error) {
    closeToast();
    showToast(error.message);
  }
};

// 单件加购
const stepCartClick = async (itemData:any) => {
  const addStartTime = dayjs(addInfoData.value.addStartTime).valueOf();
  const addEndTime = dayjs(addInfoData.value.addEndTime).valueOf();
  // console.log(addStartTime, addEndTime, 'zxxxxxxxxxxxx');
  if (dayjs().valueOf() < addStartTime) {
    showToast('加购时间未开始');
    return;
  }
  if (dayjs().valueOf() > addEndTime) {
    showToast('加购时间已结束');

  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10024/addClickLog', {
      skuIds: [itemData.skuId],
    });
    closeToast();
    addSkuToCart(itemData.skuId);
  } catch (e) {
    showToast(e.message);
  }
};
// 一键加购所有商品
const allCartClick = async () => {
  const addStartTime = dayjs(addInfoData.value.addStartTime).valueOf();
  const addEndTime = dayjs(addInfoData.value.addEndTime).valueOf();
  // console.log(addStartTime, addEndTime, 'zxxxxxxxxxxxx');
  if (dayjs().valueOf() < addStartTime) {
    showToast('加购时间未开始');
    return;
  }
  if (dayjs().valueOf() > addEndTime) {
    showToast('加购时间已结束');
    return;
  }
  const ids = addSkuList.value.map((item) => item.skuId);
  console.log(ids, '一键加购所有商品'); // 输出: [1, 2, 3]
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10024/addClickLog', {
      skuIds: ids,
    });
    closeToast();
    addSkuToCart(ids);
  } catch (e) {
    showToast(e.message);
  }
};
const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
// 初始化
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([addInfo()]);
    closeToast();
  } catch (error) {
    console.error(error);
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .winener-list{
    height: 0.6rem;
    color: #ffffff;
    z-index: 20;
    background: rgba(0,0,0,0.5);
    border-radius: 0.5rem;
    padding: 0.12rem 0.33rem;
    width: 2.9rem;
    font-size: 0.24rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    position: absolute;
    top: 0.7rem;
    left: 0.18rem;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.prizeClassAll{
  //width: 6.9rem;
  //height: 5.63rem;
  //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/15152/19/17080/126146/62aa8a71E60d7f958/cc3690c730999236.png);
  //background-size: 100%;
  //margin-left: calc(50% - 6.9rem / 2);
  //margin-top: 0.4rem;
  //position: relative;
  .addCartTypeDiv{
    width: 100%;
    text-align: center;
    color: rgb(255, 210, 113);
    font-size: 0.24rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 0.3rem;
    .labaImg{
      width:0.35rem;
      height:0.3rem;
      margin-right: 0.16rem;
    }
    .addCartTypeDetailDiv{
      span{
        font-weight: bold;
        color: rgb(242, 39, 12);
      }
    }
  }
  .prizeDetailDiv{
    padding-top: 0.75rem;
    width: 6.9rem;
    height: 3.4rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/203762/8/15856/135660/61988d7eE154a17ae/fb5a84af6834ed29.png);
    background-repeat: no-repeat;
    margin: 0.3rem auto 0px;
    background-size: 100%;
    .prizeContentDiv{
      width: 6.4rem;
      height: 2.4rem;
      padding: 0.2rem;
      display: flex;
      border-radius: 0.1rem;
      background-color: rgb(255, 255, 255);
      box-sizing: border-box;
      margin: 0px auto;
      .leftDiv{
        width: 2rem;
        height: 2rem;
        display: block;
        border: 2px solid #f2270c;
        border-radius: 0.1rem;
        position: relative;
        img{
          height: 100%;
          max-width: none;
          width:-webkit-fill-available;
        }
        .backDiv{
          width: 2rem;
          height: 0.7rem;
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
          background-size: 100%;
          background-repeat: no-repeat;
          position: absolute;
          bottom: -2px;
          left: -2px;
          text-align: center;
          padding-top: 0.32rem;
          font-size: 0.24rem;
          color: #fff;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
        }
      }
      .rightDiv{
        //width: 66%;
        margin-left: 0.43rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .prizeName{
          text-align: left;
          font-size: 0.3rem;
          display: inline-block;
          color: rgb(38, 38, 38);
          font-weight: bold;
        }
        .prizeAllRest{
          font-size: 0.2rem;
          color: rgb(140, 140, 140);
          font-weight: 500;
          text-align: left;
          margin-top: 0.2rem;
        }
        .prizeTodayRest{
          font-size: 0.2rem;
          color: #8c8c8c;
          font-weight: 500;
          text-align: left;
          margin-top: 0.1rem;
        }
      }
    }
  }
  .allAddCartBtnDiv{
    width: 6.9rem;
    height: 0.88rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/198663/31/17694/47507/61988014E18812cc5/4d5608060b6e0631.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin: 0.3rem auto 0.2rem;
    text-align: center;
    line-height: 0.88rem;
    font-size: 0.3rem;
  }
  .allAddCartBtnGrayDiv{
    width: 6.9rem;
    height: 0.88rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/208917/24/9925/46240/6198809bE38ac0386/a282e19dd52659d5.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin: 0.3rem auto 0.2rem;
    text-align: center;
    line-height: 0.88rem;
    font-size: 0.3rem;
  }
}
.sku{
  width: 7.5rem;
  margin: 0 auto 0 auto;
  padding: 0.2rem;
  position: relative;
  .more-btn-all{
    width:100%;
    display:flex;
    justify-content:center;
    margin-top:0.24rem;
    padding-bottom:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    //width: 6.9rem;
    place-content: flex-start space-between;
    max-height: 11rem;
    min-height:4rem;
    position: relative;
    overflow-y: scroll;
    padding: 0 0.12rem 0px;
  }
  .sku-item{
    width: 3.35rem;
    margin-bottom: 0.1rem;
    background: rgb(255, 255, 255);
    overflow: hidden;
    padding: 0.2rem;
    .goodImgDiv{
      width: 100%;
      height:3.25rem;
      img{
        display: block;
        width:100%;
      }
    }
    .sku-text{
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;

      overflow: hidden;
      font-size: 0.27rem;
      color: #262626;
      margin: 0.2rem 0 0.2rem 0;
      box-sizing: border-box;
    }
    .bottomDiv{
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-top: 0.13rem;
      .priceDiv{
        color: #f2270c;
        font-size: 0.3rem;
        font-weight: 400;
      }
      .sku-btns{
        width: 0.88rem;
        height: 0.4rem;
        background-color: #fff;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/215913/37/5095/3592/61987f44E4e79ad0a/4b0b7ebc7d675d10.png);
        background-size: 100%;
        background-repeat: no-repeat;
      }
    }

  }
}
.bottom-shop-share {
  display: flex;
  position: fixed;
  bottom: 0;
  .to-shop {
    flex: 1;
    height: 0.88rem;
    width: 3.75rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .share-friends {
    flex: 1;
    height: 0.88rem;
    width: 3.75rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  //padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem;
  //margin-top: 0.1rem;
  background: #ffffff;
  border-radius: 0.1rem;

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
.wheel {
  width: 100%;
  margin: 0 auto;
  //background-color: #ffffff;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
