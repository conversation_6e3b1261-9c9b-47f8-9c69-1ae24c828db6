<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="rulePopup = true"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true"><div>我的奖品</div></div>
        </div>
      </div>
    </div>
    <div class="prizeClassAll select-hover" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)">
      <div class="addCartTypeDiv">
        <img class="labaImg"  src="//img10.360buyimg.com/imgzone/jfs/t1/118773/38/21802/666/61988d7eEfaba26f0/cffd5c3036db2206.png" alt="" />
        <div class="addCartTypeDetailDiv" :style="furnishStyles.cartPromptColor.value" >加购<span :style="furnishStyles.cartTypeColor.value">{{addNum}}件</span>宝贝可领取礼品</div>
      </div>
      <div class="prizeDetailDiv" :style="furnishStyles.drawPrizeBg.value" v-if="furnish.isShowRealPrize === 1">
        <div class="prizeContentDiv">
          <div class="leftDiv">
            <img :src="prizeList[0].prizeImg" alt="" />
            <div class="backDiv">{{prizeType[prizeList[0].prizeType]}}</div>
          </div>
          <div class="rightDiv">
            <div class="prizeName">{{prizeList[0].prizeName}}</div>
            <div class="prizeAllRest">奖品剩余:{{prizeList[0].sendTotalCount}}份</div>
<!--            <div class="prizeTodayRest">今日奖品剩余:{{prizeList[0].remainCount}}份</div>-->
          </div>
        </div>
      </div>
      <div class="allAddCartBtnDiv" :style="furnishStyles.addCartBtnBg.value" v-if="addWay === 2">一键加购所有商品</div>
    </div>
    <div class="sku" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in addSkuListPreview" :key="index" @click.stop="toast">
          <div class="goodImgDiv">
            <img :src="item.skuMainPicture" alt="">
          </div>
          <div class="sku-text">{{item.skuName}}</div>
          <div class="bottomDiv">
            <div class="priceDiv">{{item.jdPrice}}</div>
            <div class="sku-btns" v-if="addWay===1" :style="furnishStyles.stepAddCartBtnBg.value"></div>
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="addSkuList.length > 18 " @click.stop="toast">点我加载更多</div>
        </div>
      </div>
    </div>
    <div class="bottom-shop-share select-hover" :class="{ 'on-select': selectedId === 4 }" @click="onSelected(4)">
      <div class="to-shop" :style="furnishStyles.btnToShop.value" @click.stop="toast" />
      <div class="share-friends" :style="furnishStyles.btnShare.value" @click.stop="toast" />
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="rulePopup" position="bottom" :closeOnClickOverlay="false">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup" position="bottom" :closeOnClickOverlay="false">
    <MyPrize @close="myPrizePopup = false"></MyPrize>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import { showToast } from 'vant';
import MyPrize from '../components/MyPrize.vue';
import Rule from '../components/Rule.vue';
import { defaultStateList, prizeType } from '../ts/default';
import dayjs from 'dayjs';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');
const isLoadingFinish = ref(false);
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const addWay = ref(2); // 1 逐件加购 2 一键加购
const addNum = ref(1); // 加购数量
const addSkuList = ref<Sku[]>([]);
const addSkuListPreview = ref<Sku[]>([]);

const toast = () => {
  showToast('活动预览，仅供查看');
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
};
const prizeList = ref<Prize[]>(defaultStateList);
const getDate = ref(dayjs().format('HH:mm')); // 领取奖品时间
const createImg = async () => {
  rulePopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  console.log(data, '活动数据监听activity');
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  if (data.prizeList.length && data.prizeList[0].prizeType > 0) {
    prizeList.value = data.prizeList;
  } else {
    prizeList.value = defaultStateList;
  }
  addWay.value = data.addWay;
  addNum.value = data.addNum;

  if (data.addSkuList) {
    addSkuList.value = data.addSkuList;
  }
  addSkuListPreview.value = data.addSkuListPreview ? data.addSkuListPreview : [];
  if (data.shopName) {
    shopName.value = data.shopName;
  }
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    if (activityData.prizeList.length && activityData.prizeList[0].prizeType > 0) {
      prizeList.value = activityData.prizeList;
    }
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    addWay.value = activityData.addWay;
    addNum.value = activityData.addNum;
    if (activityData.addSkuList) {
      addSkuList.value = activityData.addSkuList;
    }
    addSkuListPreview.value = activityData.addSkuListPreview ? activityData.addSkuListPreview : [];
    if (activityData.shopName) {
      shopName.value = activityData.shopName;
    }
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .winener-list{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 6.9rem;
    height: 5.71rem;
    margin: 0.3rem auto 0;
    padding: 0.91rem 0.15rem 0.15rem;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.prizeClassAll{
  //width: 6.9rem;
  //height: 5.63rem;
  //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/15152/19/17080/126146/62aa8a71E60d7f958/cc3690c730999236.png);
  //background-size: 100%;
  //margin-left: calc(50% - 6.9rem / 2);
  //margin-top: 0.4rem;
  //position: relative;
 .addCartTypeDiv{
   width: 100%;
   text-align: center;
   color: rgb(255, 210, 113);
   font-size: 0.24rem;
   display: flex;
   justify-content: center;
   align-items: center;
   margin-top: 0.3rem;
   .labaImg{
     width:0.35rem;
     height:0.3rem;
     margin-right: 0.16rem;
   }
   .addCartTypeDetailDiv{
     span{
       font-weight: bold;
       color: rgb(242, 39, 12);
     }
   }
 }
  .prizeDetailDiv{
    padding-top: 0.75rem;
    width: 6.9rem;
    height: 3.4rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/203762/8/15856/135660/61988d7eE154a17ae/fb5a84af6834ed29.png);
    background-repeat: no-repeat;
    margin: 0.3rem auto 0px;
    background-size: 100%;
    .prizeContentDiv{
      width: 6.4rem;
      height: 2.4rem;
      padding: 0.2rem;
      display: flex;
      border-radius: 0.1rem;
      background-color: rgb(255, 255, 255);
      box-sizing: border-box;
      margin: 0px auto;
      .leftDiv{
        width: 2rem;
        height: 2rem;
        display: block;
        border: 2px solid #f2270c;
        border-radius: 0.1rem;
        position: relative;
        img{
          height: 100%;
          max-width: none;
          width:-webkit-fill-available;
        }
        .backDiv{
          width: 2rem;
          height: 0.7rem;
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
          background-size: 100%;
          background-repeat: no-repeat;
          position: absolute;
          bottom: -2px;
          left: -2px;
          text-align: center;
          padding-top: 0.32rem;
          font-size: 0.24rem;
          color: #fff;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
        }
      }
      .rightDiv{
        //width: 66%;
        margin-left: 0.43rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .prizeName{
          text-align: left;
          font-size: 0.3rem;
          display: inline-block;
          color: rgb(38, 38, 38);
          font-weight: bold;
        }
        .prizeAllRest{
          font-size: 0.2rem;
          color: rgb(140, 140, 140);
          font-weight: 500;
          text-align: left;
          margin-top: 0.2rem;
        }
        .prizeTodayRest{
          font-size: 0.2rem;
          color: #8c8c8c;
          font-weight: 500;
          text-align: left;
          margin-top: 0.1rem;
        }
      }
    }
  }
  .allAddCartBtnDiv{
    width: 6.9rem;
    height: 0.88rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/198663/31/17694/47507/61988014E18812cc5/4d5608060b6e0631.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin: 0.3rem auto 0.2rem;
    text-align: center;
    line-height: 0.88rem;
    font-size: 0.3rem;
  }
  .allAddCartBtnGrayDiv{
    width: 6.9rem;
    height: 0.88rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/208917/24/9925/46240/6198809bE38ac0386/a282e19dd52659d5.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin: 0.3rem auto 0.2rem;
    text-align: center;
    line-height: 0.88rem;
    font-size: 0.3rem;
  }
}
.sku{
  width: 7.5rem;
  margin: 0 auto 0 auto;
  padding: 0.2rem;
  position: relative;
  .more-btn-all{
    width:100%;
    display:flex;
    justify-content:center;
    margin-top:0.24rem;
    padding-bottom:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    //width: 6.9rem;
    place-content: flex-start space-between;
    max-height: 11rem;
    min-height:4rem;
    position: relative;
    overflow-y: scroll;
    padding: 0 0.12rem 0px;
  }
  .sku-item{
    width: 3.35rem;
    margin-bottom: 0.1rem;
    background: rgb(255, 255, 255);
    overflow: hidden;
    padding: 0.2rem;
    .goodImgDiv{
      width: 100%;
      height:3.25rem;
      img{
        display: block;
        width:100%;
      }
    }
    .sku-text{
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;

      overflow: hidden;
      font-size: 0.27rem;
      color: #262626;
      margin: 0.2rem 0 0.2rem 0;
      box-sizing: border-box;
    }
    .bottomDiv{
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-top: 0.13rem;
      .priceDiv{
        color: #f2270c;
        font-size: 0.3rem;
        font-weight: 400;
      }
      .sku-btns{
        width: 0.88rem;
        height: 0.4rem;
        background-color: #fff;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/215913/37/5095/3592/61987f44E4e79ad0a/4b0b7ebc7d675d10.png);
        background-size: 100%;
        background-repeat: no-repeat;
      }
    }

  }
}
.bottom-shop-share {
  display: flex;
  position: fixed;
  bottom: 0;
  .to-shop {
    flex: 1;
    height: 0.88rem;
    width: 3.75rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .share-friends {
    flex: 1;
    height: 0.88rem;
    width: 3.75rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  width: 0;
}
</style>
