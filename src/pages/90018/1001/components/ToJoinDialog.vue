<template>
  <!-- 上传图片弹窗 -->
  <div class="box">
    <div class="btn" @click="openCard"/>
    </div>
</template>

<script lang='ts' setup>
import { defineEmits, inject } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { setBaseInfo } from '../ts/port';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
setBaseInfo(baseInfo);

const emit = defineEmits(['closeDialog']);

const openCard = () => {
  console.log('入会');
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const closeDialog = () => {
  emit('closeDialog', 'join');
};
</script>
<style lang='scss'>
.van-popup--center {
  max-width: unset !important;
}
</style>
<style lang='scss' scoped>
.box {
  align-items: center;
  justify-content: center;
  background-image: url(../assets/img/join-popup.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  z-index: 1;
  width: 5.97rem;
  height: 8.84rem;
  padding: 7.06rem 0 0;
  margin: 0 auto;
  text-align: center;
  img {
    margin: 0 auto;
  }

  .popup {
    width: 6rem;
  }
  .btn {
    font-size: 0.25rem;
    margin: 0 auto;
    //background-color: antiquewhite;
    width: 2rem;
    height: 0.5rem;
  }

  .close-btn {
    width: 0.9rem;
    height: 0.7rem;
    /* cursor: pointer; */
    z-index: 10;
    margin: 1.8rem auto 0;
    //background-color: #fff;
  }
}
</style>
