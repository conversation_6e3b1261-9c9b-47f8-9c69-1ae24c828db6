<template>
  <!-- upload-background -->
  <div class='upload-page'>
    <img class='logo' src='../assets/img/logo-icon.png' alt=''>

    <div v-if="actType === 1">
      <img class='title-image' src='../assets/img/upload-image-title.png' alt=''>
    </div>
    <div v-else>
      <img class='title-image' src='../assets/img/new-upload-title.png' alt=''>
      <div class="subhead">
        请拍摄并上传{{uploadName}}
      </div>
    </div>

    <!-- 上传区域 -->
    <img src='//img10.360buyimg.com/imgzone/jfs/t1/252531/8/29371/17606/67c7ff66F409a6e87/6f4bf88d91d51e23.png'
         v-if='!croppedImage' class='upload-area' @click='triggerFileInput' alt=''>

    <div class='cropper-view' v-else>
      <img :src='croppedImage' @click='triggerFileInput()' alt=''>
    </div>

    <!-- 隐藏的input -->
    <input
      ref='fileInput'
      type='file'
      accept='image/*'
      class='hidden-input'
      @change='handleFileChange'
    />

    <img class='result-btn' :class='{gray:!croppedImage}' src='../assets/img/commit-btn.png' @click='commitSizeAdjustImage()' alt=''>
    <img class="bottom" src="../assets/img/bottom-text.png" alt="">

  </div>

  <Popup class='popup' v-model:show='showImageSizeAdjustPopup' :close-on-click-overlay='false'>
    <!-- 图片容器 -->
    <div class='cropper-container'>
      <img ref='imageElement' :src='previewUrl' alt='' />
    </div>

    <div class='cropper-tip'>请上传5M以内的图片</div>

    <!-- 操作按钮 -->
    <div class='controls-view' v-if='cropper && previewUrl'>
      <img class='icon-btn' src='../assets/img/cancel-image-size-icon.png' @click='cancelSizeAdjust()' alt=''>
      <img class='icon-btn' src='../assets/img/sure-image-size-icon.png' @click='cropImage()' alt=''>
    </div>
  </Popup>
</template>

<script lang='ts' setup>
import { computed, ref } from 'vue';
import { closeToast, Popup, showLoadingToast, showToast } from 'vant';
import { useRouter, useRoute } from 'vue-router';
import Cropper from 'cropperjs';
import 'cropperjs/dist/cropper.css';
import { useStore } from 'vuex';
import { httpRequest } from '@/utils/service';

const router = useRouter();
const route = useRoute();

const store = useStore();

// 通过 getters 获取状态
// B端配置的活动类型 0-不开启 1-上传出生证明 2-上传宝宝照片
const actType = computed(() => store.getters.getActType);
// 上传照片名称
const uploadName = computed(() => store.getters.getUploadName);
// 手机号
const successPhone = computed(() => store.getters.getSuccessPhone);

// 元素引用
const imageElement = ref(null);
const croppedImage = ref('');
const cropper = ref(null);

const previewUrl = ref();
const fileInput = ref(null);
const showImageSizeAdjustPopup = ref(false);

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click();
};

// 初始化Cropper
const initCropper = () => {
  if (imageElement.value) {
    cropper.value = new Cropper(imageElement.value, {
      dragMode: 'move',
      movable: true,
      viewMode: 1,
      autoCropArea: 1,
      aspectRatio: 4 / 3, // 比例锁定
      cropBoxMovable: false, // 固定裁剪框位置
      cropBoxResizable: false, // 禁止调整大小
    });
  }
};

const imageSizeAdjust = () => {
  showImageSizeAdjustPopup.value = true;
  setTimeout(() => {
    initCropper();
  }, 100);
};

// 处理文件选择
const handleFileChange = (event: any) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    showToast('请选择图片');
    return;
  }

  // 读取新文件
  const reader = new FileReader();
  imageSizeAdjust();
  reader.onload = (event) => {
    previewUrl.value = event.target.result;
  };
  reader.readAsDataURL(file);
  event.target.value = '';
};

// 取消裁剪
const cancelSizeAdjust = () => {
  showImageSizeAdjustPopup.value = false;
  cropper.value.destroy();
};

// 执行裁剪
const cropImage = () => {
  if (!cropper.value) return;

  const canvas = cropper.value.getCroppedCanvas({
    imageSmoothingQuality: 'high',
  });

  croppedImage.value = canvas.toDataURL('image/png');
  cancelSizeAdjust();
};

// 示例上传方法
const uploadImage = async (file: any) => {
  const formData = new FormData();
  formData.append('image', file);

  try {
    // 替换为实际的上传API
    // const response = await axios.post('/upload', formData)
    console.log('上传成功');
  } catch (error) {
    console.error('上传失败:', error);
  }
};

const urlToBlob = async (url) => {
  const response = await fetch(url);
  return await response.blob();
};

const commitSizeAdjustImage = async () => {
  if (!croppedImage.value) {
    showToast('请先选择图片');
    return;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  if (actType.value === 1) {
    try {
      console.log('图片上传');
      const blob = await urlToBlob(croppedImage.value);
      const imgData = new FormData();
      imgData.append('file', blob, 'croppedImage.png');
      imgData.append('mobile', successPhone.value);
      const res = await httpRequest.post('/90018/checkAndBindBornCertificate', imgData);
      // console.log(res, '上传结果');
      // 如果ocr识别失败
      if (!res.data.isCertificateNumber) {
        showToast('出生证号识别失败，请重新上传');
        croppedImage.value = '';
        return;
      }
      if (!res.data.isBindCertificateNumber) {
        showToast('此出生证号已被绑定，无法重复绑定');
        croppedImage.value = '';
        return;
      }
      if (res.data.isCertificateNumber && res.data.isBindCertificateNumber) {
        // 成功
        router.replace('/equityPage');
      }
      closeToast();
    } catch (error) {
      closeToast();
      showToast(error.message);
    }
  } else if (actType.value === 2) {
    closeToast();
    showToast('上传成功');
    router.replace('/equityPage');
  } else {
    console.log('actType未知');
  }
};

</script>

<style lang='scss' scoped>
.upload-page {
  width: 100%; /* 宽度自适应 */
  min-height: 100vh;
  padding-top: 2.2rem;
  padding-bottom: 0.2rem;
  position: relative;
  text-align: center;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/261951/17/28337/16422/67c7fd2bF4c5324b3/1bcc5f249930f655.jpg");
    repeat: no-repeat;
    size: 100% 100%;
  };

  .logo {
    width: 2.48rem;
    position: absolute;
    top: .5rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .title-image {
    width: 5.81rem;
    margin: 0 auto;
  }
  .subhead{
    margin: 0 auto;
    font-size: 0.43rem;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0.02rem;
    background: linear-gradient(to right, #6494f8, #426fd8, #1f47b8);
    -webkit-background-clip: text;
    color: transparent; /* 隐藏文字本身的颜色 */
  }

  .upload-area {
    width: 6.26rem;
    margin: 1.3rem auto 0;
  }

  .result-btn {
    width: 3.14rem;
    margin: 1.3rem auto 0;
  }

  .hidden-input {
    display: none;
  }

  .cropper-view {
    width: 6.26rem;
    height: 6.1rem;
    border-radius: .5rem;
    margin: 1.3rem auto 0;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 5.5rem;
    }
  }
}

.container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.cropper-container {
  width: 100%;
  height: 100%;
  max-width: 500px;
  max-height: 500px;
  margin: 20px 0;
}

.cropper-container img {
  display: block;
  max-width: 100%;
}

.cropper-tip {
  text-align: center;
  color: #FFFFFF;
  font-size: .27rem;
}

.controls-view {
  width: 6.5rem;
  margin: 1rem auto 0;
  display: flex;
  align-items: center;
  justify-content: space-evenly;

  .icon-btn {
    width: 1.2rem;
  }
}
.bottom{
  margin: 3rem auto 0;
  width: 7.2rem;
}

</style>
