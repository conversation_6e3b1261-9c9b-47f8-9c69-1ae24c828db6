<template>
  <div class="bk">
    <div class="kv">
      <img src="../assets/kv.png" alt="" class="kv-img" />
      <div class="btn-list">
        <img src="../assets/prize.png" alt="" @click="myPrizePopup = true" />
        <img src="../assets/rule.png" alt="" @click="rulePopup = true" />
      </div>
      <div class="main-button">
        <img v-if="lockStatus" src="../assets/goBuy.png" alt="" />
        <img v-else src="../assets/pay.png" alt="" class="pay" @click="showPayPopup" />
      </div>
    </div>
    <img class="step" src="../assets/step.png" alt=""  />
    <div class="content">
      <div class="item-bk" v-for="(item, index) in prizeList" :key="item.shopId" :style="{ backgroundImage: `url(${require(`../assets/shopList/bk${(index % 3) + 1}.png`)})` }">
        <div class="shop-info">
          <img v-if="item.shopId === 175953" src="../assets/mengniuLogo.png" alt="" />
          <img v-else :src="item.logo" alt="" :class="{ cover: item.shopId === 1000309923 || item.shopId === 10668387 }" />
          <span>
            {{ item.shopName }}
          </span>
        </div>
        <div class="prize-info">
          <div class="left">
            <div class="name">{{ item.prizeName }}</div>
            <div class="num">
              剩余库存：<span>{{ item.num }}</span>
            </div>
            <div class="btn" @click="joinAct(item)" :style="{ backgroundImage: `url(${require(`../assets/shopList/btn${(index % 3) + 1}.png`)})` }">
              <div class="text3" v-if="!lockStatus">购物满{{ item.amount }}元即得</div>
              <div class="text3" v-else-if="item.status === '4'">活动结束后商家统一发放</div>
              <div class="text3" v-else-if="item.status === '5'">权益已发放</div>
              <div class="text3" v-else-if="item.status === '6'">该店权益已抢光</div>
              <div class="text3" v-else-if="item.status === '3'">已获得 请等待发奖</div>
              <div v-else>
                <div class="text1">买满{{ item.amount }}即得></div>
                <div class="text2">还差￥{{ item.thresholdAmount }}</div>
              </div>
            </div>
          </div>
          <div class="right">
            <img :src="item.prizeImg" alt="" class="prize-img" />
            <div class="finish" v-if="item.num <= 0">
              <img src="../assets/finish.png" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <PopupBk v-model:show="memberPopup" :title="require('../assets/memberPopup.png')">
    <MemberPopup @closeScreen="closeScreen" :memberList="memberInfo.filter((item) => !item.status)" />
  </PopupBk>
  <PopupBk v-model:show="rulePopup" :title="require('../assets/rulePopup.png')">
    <RulePopup />
  </PopupBk>
  <PopupBk v-model:show="myPrizePopup" :title="require('../assets/prizePopup.png')">
    <MyPrize v-if="myPrizePopup" />
  </PopupBk>
  <PopupBk v-model:show="addressPopup" :title="require('../assets/addressPopup.png')">
    <AddressPopup @close="closeAddress" />
  </PopupBk>
  <VanPopup teleport="body" v-model:show="payPopup" class="no-margin">
    <PayPopup v-if="payPopup" :prizeList="prizeList" :allFinish="allFinish" :currentClickShopId="currentClickShopId" @close="payPopup = false" @pay="toPay" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, onMounted, onUnmounted, ref } from 'vue';
import PopupBk from '../components/PopupBk.vue';
// import MemberPopup from '../components/MemberOneKeyPopup.vue';
import MemberPopup from '../components/MemberPopup.vue';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AddressPopup from '../components/AddressPopup.vue';
import PayPopup from '../components/PayPopup.vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import constant from '@/utils/constant';
import { BaseInfo } from '@/types/BaseInfo';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

const baseInfo = inject('baseInfo') as BaseInfo;

const memberPopup = ref(false);
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const addressPopup = ref(false);
const payPopup = ref(false);

// 锁权状态
const lockStatus = ref(false);
// 全部奖品已领光
const allFinish = ref(false);

const memberInfo = ref<any[]>([]);
const prizeList = ref<any[]>([]);

const needCheckPay = ref(false);
const merchantOrderId = ref('');

const currentClickShopId = ref('');

const showPayPopup = () => {
  if (memberInfo.value.findIndex((item) => !item.status) !== -1) {
    memberPopup.value = true;
    return;
  }
  payPopup.value = true;
};

const toPay = async () => {
  if (memberInfo.value.findIndex((item) => !item.status) !== -1) {
    memberPopup.value = true;
    return;
  }
  try {
    showLoadingToast({
      message: '支付中...',
      duration: 0,
      forbidClick: true,
    });
    const { data } = await httpRequest.post('/91001/pay', {
      token: sessionStorage.getItem(constant.LZ_JD_TOKEN),
      activityId: baseInfo.activityId,
      shopId: baseInfo.shopId,
      source: '01',
      amount: 1,
    });
    closeToast();
    merchantOrderId.value = data.orderId;
    const param = {
      orderId: data.orderId,
      paySign: data.paySign,
    };
    const payUrl = `openapp.jdmobile://virtual?params={"category":"jump","des":"jdmp","appId":"2B43F9A518AE09BAE8789053047A685E","vapptype":"1","path":"pages/saas-pay/saas-pay.html","pageAlias":"","param":${JSON.stringify(param)}}`;
    needCheckPay.value = true;
    payPopup.value = false;
    window.location.href = payUrl;
  } catch (error: any) {
    console.error(error);
    showToast(error.message);
    if (error.message === '请先填写地址') {
      addressPopup.value = true;
    }
  }
};

const closeAddress = (isSuccess: boolean) => {
  if (isSuccess) {
    addressPopup.value = false;
    payPopup.value = true;
  }
};

const getMemberInfo = async () => {
  try {
    const { data } = await httpRequest.post('/91001/getMemberInfo');
    memberInfo.value = data;
    if (data.length === 0) {
      memberPopup.value = false;
    }
  } catch (error: any) {
    console.error(error);
    showToast(error.message);
  }
};
const getActivity = async () => {
  try {
    const { data } = await httpRequest.post('/91001/activity');
    const hsaNum = data.prizeResponseList.filter((item: any) => item.num > 0 || String(item.shopId) === baseInfo.shopId);
    const noNum = data.prizeResponseList.filter((item: any) => item.num <= 0 && String(item.shopId) !== baseInfo.shopId);
    prizeList.value = [...hsaNum, ...noNum];
    lockStatus.value = !!data.status;
    let allNum = 0;
    prizeList.value.forEach((item) => {
      allNum += item.num;
    });
    allFinish.value = allNum <= 0;
    console.log(allFinish.value, 'allFinish');

  } catch (error: any) {
    console.error(error);
    showToast(error.message);
  }
};

let initNum = 0;
const init = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  await Promise.all([getMemberInfo(), getActivity()]);
  closeToast();
  initNum++;
  // if (initNum > 1) {
  //   return;
  // }
  // memberInfo.value.forEach((item) => {
  //   if (!item.status) {
  //     memberPopup.value = true;
  //   }
  // });
};

// 参与店铺活动
const joinAct = (item: any) => {
  if (!lockStatus.value) {
    showPayPopup();
    return;
  }
  showPayPopup();
  currentClickShopId.value = item.shopId;
  // if (status === 4) {
  //   showToast('活动结束后商家统一发放');
  // } else if (status === 5) {
  //   showToast('权益已发放');
  // } else if (status === 6) {
  //   showToast('该店权益已抢光');
  // }
  router.push({ path: '/shopInfo',
    query: {
      showId: item.shopId,
      amount: item.amount,
      payAmount: item.payAmount,
      thresholdAmount: item.thresholdAmount,
      shopId: baseInfo.shopId,
      activityId: baseInfo.activityId,
    } });
};

const getPayMember = async () => {
  try {
    const res = await httpRequest.post('/91001/checkOrderStatus', {
      activityId: baseInfo.activityId,
      merchantOrderId: merchantOrderId.value,
    });
    return res.data;
  } catch (error: any) {
    showToast(error.message);
    return false;
  }
};

// 轮询支付状态
const pollingPayStatus = async () => {
  const type = await getPayMember();
  if (!type) {
    setTimeout(() => {
      pollingPayStatus();
    }, 5000);
  } else {
    lockStatus.value = true;
    showToast('支付成功');
    getActivity();
  }
};

// 检查支付状态
const checkPay = () => {
  if (document.visibilityState !== 'visible') return;
  if (!needCheckPay.value) {
    init();
    return;
  }
  needCheckPay.value = false;
  pollingPayStatus();
};
const closeScreen = () => {
  getMemberInfo();
};
onMounted(() => {
  // 从上一页返回时，重新获取数据
  document.addEventListener('visibilitychange', checkPay);
});
onUnmounted(() => {
  document.removeEventListener('visibilitychange', checkPay);
});
init();
</script>

<style>
/* @font-face {
  font-family: 'SourceHanSansCN-Medium';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Medium.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Medium.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'SourceHanSansCN-Medium';
} */
</style>

<style scoped lang="scss">
.bk {
  background-color: #ff8c7e;
  min-height: 100vh;
}
.kv {
  position: relative;
  .kv-img {
    width: 100%;
  }
  .btn-list {
    position: absolute;
    right: 0;
    top: 2.7rem;
    img {
      width: 0.43rem;
      margin-bottom: 0.4rem;
    }
  }
  .main-button {
    position: absolute;
    top: 6.8rem;
    left: 0;
    right: 0;
    img {
      width: 5.58rem;
      margin: 0 auto;
    }
    // 反复缩放动画
    @keyframes scale {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(0.9);
      }
      100% {
        transform: scale(1);
      }
    }
    .pay {
      animation: scale 1s infinite;
    }
  }
}
.step {
  width: 7.32rem;
  margin: 0.3rem auto 0;
}
.content {
  padding: 0.3rem;
  .item-bk {
    background-image: url('../assets/shopList/bk1.png');
    background-size: 100%;
    background-repeat: no-repeat;
    height: 3.3rem;
    position: relative;
    padding: 0.79rem 0.2rem 0 0.3rem;
    .shop-info {
      position: absolute;
      left: 0.29rem;
      top: 0.25rem;
      display: flex;
      align-items: center;
      font-size: 0.3rem;
      color: #fff;
      line-height: 0.3rem;
      img {
        width: 0.5rem;
        height: 0.5rem;
        object-fit: contain;
        border-radius: 50%;
        margin-right: 0.16rem;
        background-color: #fff;
      }
      span {
        display: inline-block;
        width: 2.5rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .cover {
        object-fit: cover;
      }
    }
    .prize-info {
      display: flex;
      .left {
        flex: 1;
        padding-right: 0.3rem;
        padding-top: 0.33rem;
        .name {
          font-size: 0.24rem;
          line-height: 0.3rem;
          height: 0.6rem;
          margin-bottom: 0.15rem;
          // 文字最多2行展示
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        .num {
          font-size: 0.24rem;
          font-weight: 300;
          margin-bottom: 0.15rem;
          line-height: 0.3rem;
          span {
            font-size: 0.3rem;
            color: #f92b28;
            font-weight: 500;
          }
        }
        .btn {
          width: 3.6rem;
          height: 0.64rem;
          background-image: url('../assets/shopList/btn1.png');
          background-repeat: no-repeat;
          background-size: 100%;
          text-align: center;
          color: #fff;
          .text1 {
            padding-top: 0.12rem;
            font-size: 0.27rem;
            line-height: 0.27rem;
          }
          .text2 {
            font-size: 0.18rem;
            line-height: 0.18rem;
          }
          .text3 {
            font-size: 0.27rem;
            line-height: 0.64rem;
          }
        }
      }
      .right {
        position: relative;
        border-radius: 0.3rem;
        overflow: hidden;
        .finish {
          position: absolute;
          right: 0;
          top: 0;
          left: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(0, 0, 0, 0.7);
          img {
            width: 1.4rem;
          }
        }
      }
      .prize-img {
        width: 2.27rem;
        height: 2.27rem;
        box-shadow: 0.01rem 0.017rem 0.08rem 0rem rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
