<template>
  <div class="">
    <img src="../assets/addressTip.png" alt="" class="tip" />
    <div class="form">
      <div>
        <div class="form">
          <VanField v-model="form.realName" :label="'姓名'" maxlength="20" placeholder="请输入收货人姓名"></VanField>
          <VanField v-model="form.mobile" label="联系电话" maxlength="11" type="number" placeholder="请输入收货人手机号码"></VanField>
          <VanField v-model="addressCode" label="所在地区" readonly @click="addressSelects = true" placeholder="请选择省市区"></VanField>
          <VanField v-model="form.address" label="详细地址" maxlength="100" placeholder="请输入详细地址"></VanField>
        </div>
        <img src="../assets/submit.png" alt="" class="submit" @click="checkForm" />
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from 'vue';
import { areaList } from '@vant/area-data';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const emits = defineEmits(['close']);

const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});

const addressSelects = ref(false);

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/91001/userAddressInfo', {
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (reg.test(form.realName)) {
    showToast('姓名不能包含表情');
  } else if (!form.mobile) {
    showToast('请输入联系电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的联系电话');
  } else if (!form.province) {
    showToast('请选择所在地区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else if (reg.test(form.address)) {
    showToast('详细地址不能包含表情');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.tip {
  width: 5.98rem;
  margin: 0.28rem auto 0.45rem;
}
.submit {
  width: 3.54rem;
  margin: 0.65rem auto 0;
}
</style>
<style lang="scss">
.form {
  width: 5.68rem;
  margin: 0 auto;
  .van-cell {
    color: #262626;
    padding: 0;
    background-color: transparent;
    align-items: center;
    font-size: 0.28rem;
    border-radius: 0.15rem;
    margin-bottom: 0.26rem;
    .van-cell__title {
      width: 1.25rem;
    }

    input {
      height: 0.74rem;
      border-radius: 0.2rem;
      border: solid 1px #d8345a;
      padding: 0 0.2rem;
      background-color: #fff;
    }

    &::after {
      display: none;
    }
  }
}
</style>
