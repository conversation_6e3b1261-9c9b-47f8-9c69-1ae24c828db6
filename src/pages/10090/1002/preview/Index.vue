<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/94932/32/34595/128720/65004926Fe5cf254f/29b818fbf4518997.png'" alt="" class="kv-img"/>
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div
            class="header-btn"
            :style="furnishStyles.headerBtn.value"
            v-for="(btn, index) in btnList"
            :key="index"
            @click="btn.event"
          >
            {{btn.name}}
          </div>
        </div>
      </div>
    </div>
    <div class="draw-btn select-hover" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)">
      <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart"/>
      <div class="prizes"
           :style="{'background-image':'url('+furnish.prizeBg ?? `//img10.360buyimg.com/imgzone/jfs/t1/236270/10/12377/7158/65b31c26F15b49008/2060a620ea40d9bc.png`+`)`}"
      >
        <div class="gift-show">
          <div class="gift-img">
            <img class="imgs" :src="prizeList?.prizeImg ? prizeList.prizeImg : 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png'" alt="">
<!--            <div class="back">{{prizeList.prizeName}}</div>-->
          </div>
          <div class="gift-info">
            <div :style="furnishStyles.prizeNameColor.value">{{prizeList?.prizeName}}</div>
            <div class="remain-prize" :style="furnishStyles.prizeNameColor.value">奖品剩余：{{prizeList?.remainCount || prizeList?.sendTotalCount || 'xx'}}份</div>
          </div>
          <div class="get-prize-btn" @click="ShowToast">
            <img :src="furnish.getPrizeBtn" alt="">
          </div>
        </div>
      </div>
    </div>
    <div class="sku" v-if="showSelect || (isExposure === 1 && skuListPreview?.length > 0)">
      <img class="title-img" :src="furnish.showSkuBg" alt="" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in skuListPreview" :key="index">
          <img :src="item.skuMainPicture" alt="">
          <div class="sku-text">{{item.skuName}}</div>
          <div class="sku-price">￥<span>{{item.jdPrice}}</span></div>
          <div class="sku-btns">
          </div>
        </div>
        <div class="more-btn" v-if="skuList.length > 18" @click="toast">点我加载更多</div>

      </div>
    </div>
    <div class="winners select-hover" :style="furnishStyles.winnersBg.value" :class="{ 'on-select': selectedId === 4 }" @click="onSelected(4)">
      <div class="winners-content">
        <div class="winner-list swiper-container" ref="swiperRef">
          <div class="swiper-wrapper" v-if="activityGiftRecords.length != 0">
            <div class="winner swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
              <div>
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" v-if="!item.userImg" />
                <img v-else :src="item.userImg" alt="" />
                <span>{{ item.nickName }}</span>
              </div>
              <span>{{ item.giftName }}</span>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
    <div class="bottom-shop-share select-hover" :class="{ 'on-select': selectedId === 5 }" @click="onSelected(5)">
      <div class="to-shop" :style="furnishStyles.btnToShop.value" @click="ShowToast"/>
    </div>
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!--    我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
    </VanPopup>
    <!--    活动商品弹窗-->
    <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
      <GoodsPopup :data="orderSkuListPreview" :orderSkuList="orderSkuList" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, reactive } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';
import CountDown from '../components/CountDown.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import GoodsPopup from '../components/GoodsPopup.vue';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);

const isLoadingFinish = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}
const prizeList = ref<Prize>(defaultStateList);

type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>([]); // 曝光商品
const skuList = ref<Sku[]>([]); // 曝光商品
const orderSkuList = ref<Sku[]>([]);
const orderSkuListPreview = ref<Sku[]>([]);

const nextStateAmount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showAward = ref(false);
const showOrderRecord = ref(false);
const showGoods = ref(false);

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};
const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => { showRule.value = true; } },
  {
    name: '我的奖品',
    event: () => { showMyPrize.value = true; },
  },
  {
    name: '活动商品',
    event: () => {
      showGoods.value = true;
    },
  },
  { name: '我的订单',
    event: () => { showOrderRecord.value = true; },
  },
];

const orderSkuisExposure = ref(1);
const isExposure = ref(1);

const activityGiftRecords = reactive([
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
]);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  showOrderRecord.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const list = data.prizeList[0];
  if (list?.prizeType !== 0) {
    prizeList.value = list;
  } else {
    prizeList.value = defaultStateList;
  }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
    skuListPreview.value = data.skuListPreview;
  }
  if (data.orderSkuListPreview) {
    orderSkuList.value = data.orderSkuList;
    orderSkuListPreview.value = data.orderSkuListPreview;

  }
  ruleTest.value = data.rules;
  orderSkuisExposure.value = data.orderSkuisExposure;
  isExposure.value = data.isExposure;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  if (activityData) {
    const data = activityData;
    endTime.value = dayjs(data.endTime).valueOf();
    const list = data.prizeList[0];
    if (list?.prizeType !== 0) {
      prizeList.value = list;
    } else {
      prizeList.value = defaultStateList;
    }
    startTime.value = new Date(data.startTime).getTime();
    if (startTime.value > new Date().getTime()) {
      isStart.value = false;
    }
    if (startTime.value < new Date().getTime()) {
      isStart.value = true;
    }
    endTime.value = new Date(data.endTime).getTime();
    if (data.skuList) {
      skuList.value = data.skuList;
      skuListPreview.value = data.skuListPreview;
    }
    if (data.orderSkuListPreview) {
      orderSkuList.value = data.orderSkuList;
      orderSkuListPreview.value = data.orderSkuListPreview;
    }
    ruleTest.value = data.rules;
    orderSkuisExposure.value = data.orderSkuisExposure;
    isExposure.value = data.isExposure;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  // if (activityGiftRecords.length > 4) {
  //   nextTick(() => {
  //     const mySwiper = new Swiper('.swiper-container', {
  //       autoplay: {
  //         delay: 1000,
  //         stopOnLastSlide: false,
  //         disableOnInteraction: false,
  //       },
  //       direction: 'vertical',
  //       loop: true,
  //       slidesPerView: 5,
  //       loopedSlides: 8,
  //       centeredSlides: true,
  //     });
  //   });
  // }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    line-height: 0.52rem;
    position: relative;
    right: -0.3rem;
    top: 0.6rem;
    width: 1.25rem;
    height: 0.58rem;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.draw-btn {
  width: 6.9rem;
  margin: 0.4rem auto 0 auto;
  img {
    width: 100%;
  }
}

.prizes {
  margin: 0.7rem auto 0 auto;
  border-radius: 0.3rem;
  background-repeat: no-repeat;
  background-size: 100%;
  height: 3.71rem;
  padding-top: 0.1rem;

  .gift-show {
    margin-left: 0.6rem;
    margin-top: 0.32rem;
    margin-right: 0.26rem;
    padding-bottom: 0.22rem;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    font-size: 0.25rem;
    align-items: center;

    .gift-img {
      width: 2rem;
      height: 2rem;
      //border: 0.02rem solid #ff3633;
      //margin: 0 0.5rem 0 0;
      border-radius: 50%;
      background-image: linear-gradient(0deg, #ffffba 0%, #fff 100%),linear-gradient(0deg, #fcdab3 0%, #f7c79e 100%);
      overflow: hidden;
      position: relative;
      .imgs{
        width: 2rem;
        height: auto;
        border-radius: 0.16rem;
        margin: 0 auto;
      }
      .back{
        width: 1.92rem;
        height: 0.7rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
        background-size: 100%;
        background-repeat: no-repeat;
        position: absolute;
        top: 1.3rem;
        //position: relative;
        //top:-0.6rem;
        text-align: center;
        padding-top: 0.32rem;
        font-size: 0.24rem;
        color: #fff;
        overflow:hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow:ellipsis;
      }
    }

    .gift-info {
      -webkit-box-align: end;
      align-items: flex-end;
      text-align: left;
      font-weight: 700;
      margin-left: 0.4rem;
      position: relative;
      top: -0.4rem;
      right: 0;
      .remain-prize{
        //color: #999999;
        font-weight: 500;
        margin: 0.1rem 0 0 0;
      }
    }

    .get-prize-btn {
      width: 1.91rem;
      height: 0.55rem;
      //background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
      border-radius: 0.25rem;
      font-size: 0.24rem;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0;
      color: rgb(255, 255, 255);
      text-align: center;
      line-height: 0.5rem;
      position: relative;
      top: 0.6rem;
      right: 1.9rem;
    }
  }
}

.sku{
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
  .title-img{
    //width: 2.82rem;
    //height: 0.4rem;
    width: 6.9rem;
    margin: 0 auto;
    background-repeat:no-repeat;
    background-size: 100%;
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    width: 6.83rem;
    margin: 0 auto;
    place-content: flex-start space-between;
    padding: 0.2rem;
    //background: rgb(253, 249, 243);
    max-height: 9.85rem;
    //position: relative;
    border-radius:0 0 0.3rem 0.3rem;
    overflow-y: scroll;
  }
  .sku-item{
    width: 3.1rem;
    margin-bottom: 0.1rem;
    background: url("https://img10.360buyimg.com/imgzone/jfs/t1/84987/36/42964/86384/650819ccFdb6fcee0/29a5ccc81e2356d4.png") no-repeat;
    background-size: 100%;
    border-radius: 0.3rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    img{
      margin-top: 0.2rem;
      display: block;
      width: 2.7rem;
      height: 2.7rem;
      border-radius: 0.1rem;
    }
    .sku-text{
      width: 3.1rem;
      display: -webkit-box;
      display: inline-block;
      white-space: nowrap;
      text-overflow:ellipsis;
      overflow: hidden;
      font-size: 0.3rem;
      color: #ffffff;
      //height: 0.8rem;
      padding: 0 0.2rem;
      margin: 0.1rem 0 0.1rem;
      box-sizing: border-box;
      line-height: .4rem;
    }
    .sku-price{
      font-size: 0.3rem;
      font-weight: 500;
      color: #ffffba;
      line-height: 0.3rem;
      position: relative;
      left:-0.1rem;
      span{
        color: #ffffba;
        font-size: 0.5rem;
        font-weight: 700;
        text-align: left;
        line-height: 0.3rem;
      }
    }
    .sku-btns{
      width: 2.6rem;
      height: 0.87rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.14rem auto;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/108980/33/44336/16949/650807f2F16d469d8/9a3c8988ac75730a.png);
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 2rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share{
  //display: flex;
  position: fixed;
  bottom: 0;
  //left:0.3rem;
  margin: 0 auto;
  .to-shop{
    //flex: 1;
    height: 0.8rem;
    width: 7.5rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .share-friends{
    flex: 1;
    height: 0.88rem;
    width: 3.75rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1rem;

  .winners-content {
    width: 6.4rem;
    height: 3.8rem;
    //background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}
.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem 0;
  border-bottom: 1px dashed rgb(230, 230, 230);

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
