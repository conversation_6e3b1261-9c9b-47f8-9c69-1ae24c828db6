<template>
  <div class="bk">
    <div class="tabs">
      <div class="tab" v-for="item in tabs" :key="item.id" :class="{ 'tab-active': item.id === activeTab }" @click="changTab(item.id)">{{ item.title }}</div>
    </div>
    <div class="content">
      <div class="tab0" v-if="activeTab === 1">
        <div v-if="powerTime">{{ powerTime }}锁定权益</div>
        <div v-else>暂无锁权记录~</div>
      </div>
      <div class="tab1" v-else-if="activeTab === 2">
        <div class="address" v-if="activity10104AddressResponse.realName">
          <p>姓名：{{ activity10104AddressResponse.realName }}</p>
          <p>手机号：{{ activity10104AddressResponse.mobile }}</p>
          <p>省市区：{{ activity10104AddressResponse.province }} {{ activity10104AddressResponse.city }} {{ activity10104AddressResponse.county }}</p>
          <p>详细地址：{{ activity10104AddressResponse.address }}</p>
          <p style="margin-top: 0.35rem">快递单号:</p>
          <p class="id">{{ activity10104AddressResponse.trackingNum || '暂无快递单号' }}</p>
          <div class="btn copy-btn" v-if="activity10104AddressResponse.trackingNum" :copy-text="activity10104AddressResponse.trackingNum">复制单号<img src="../assets/clickIcon2.png" alt="" /></div>
        </div>
        <div class="no-data" v-else>
          <div class="tip">暂无收货信息</div>
          <div class="btn" v-if="actData.status === 0 || actData.status === 1" @click="toFillInAddress">去填写<img src="../assets/clickIcon2.png" alt="" /></div>
        </div>
      </div>
      <div class="tab0" v-else-if="activeTab === 3">
        <div style="line-height: 0.9rem" v-if="exchangeTime">
          亲爱的用户<br />
          您在{{ exchangeTime }}<br />
          成功兑换成功兑换好礼
        </div>
        <div v-else>暂无好礼兑换记录~</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { inject, ref } from 'vue';
import Clipboard from 'clipboard';
import { actData } from '../ts/logic';

const props = withDefaults(defineProps<{ tab: number }>(), {
  tab: 1,
});

const emits = defineEmits(['toFillInAddress']);

const tabs = [
  {
    id: 1,
    title: '锁权记录',
  },
  {
    id: 2,
    title: '收货信息',
  },
  {
    id: 3,
    title: '兑换记录',
  },
];
const activeTab = ref(props.tab);

const powerTime = ref('');
const activity10104AddressResponse = ref<any>('');
const exchangeTime = ref('');

const getMyRecord = async (type: number) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10104/getMyRecord', {
      type,
    });
    closeToast();
    if (type === 1) {
      powerTime.value = res.data.powerTime ? dayjs(res.data.powerTime).format('YY年M月D日') : '';
    } else if (type === 2) {
      activity10104AddressResponse.value = res.data.activity10104AddressResponse;
    } else if (type === 3) {
      exchangeTime.value = res.data.exchangeTime ? dayjs(res.data.exchangeTime).format('YY年M月D日') : '';
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

const changTab = (id: number) => {
  activeTab.value = id;
  getMyRecord(activeTab.value);
};

getMyRecord(activeTab.value);

const toFillInAddress = () => {
  emits('toFillInAddress', 1);
};

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.bk {
  width: 5.86rem;
}
.tabs {
  padding: 0.45rem 0.25rem 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tab {
    width: 1.7rem;
    height: 0.5rem;
    background: url(../assets/btnBlack.png) no-repeat;
    background-size: 100% 100%;
    font-size: 0.3rem;
    text-align: center;
    line-height: 0.5rem;
    color: #fff;
  }
  .tab-active {
    background: url(../assets/btnRed2.png) no-repeat;
    background-size: 100% 100%;
  }
}
.content {
  .tab0 {
    padding: 1.15rem 0;
    div {
      text-align: center;
      font-family: 'CenturyGothic', 'FZLTHJW';
      font-size: 0.4rem;
    }
  }
  .tab1 {
    .address {
      padding-top: 0.43rem;
      padding-bottom: 0.33rem;
      p {
        padding: 0 0.25rem;
        text-align: center;
        font-family: 'CenturyGothic', 'FZLTHJW';
        font-size: 0.24rem;
        line-height: 0.45rem;
      }
      .id {
        width: 4.95rem;
        margin: 0 auto 0.4rem;
        background-color: #dee3e9;
        border: solid 0.01rem #ffffff;
        height: 0.45rem;
      }
    }
    .no-data {
      padding: 1.15rem 0 1rem;
      .tip {
        text-align: center;
        font-family: 'CenturyGothic', 'FZLTHJW';
        font-size: 0.5rem;
        margin-bottom: 1.5rem;
      }
    }
  }
  .btn {
    width: 3.71rem;
    height: 0.64rem;
    background: url(../assets/btnRed.png) no-repeat;
    background-size: 100%;
    margin: 0 auto;
    font-size: 0.366rem;
    color: #fff;
    text-align: center;
    line-height: 0.64rem;
    img {
      display: inline-block;
      width: 0.38rem;
      vertical-align: middle;
      margin-left: 0.2rem;
    }
  }
}
</style>
