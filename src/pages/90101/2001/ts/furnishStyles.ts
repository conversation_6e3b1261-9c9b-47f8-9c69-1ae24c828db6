import { computed, reactive } from 'vue';
import { prizePlateArray } from '@/utils/prizePlateArray';

export const prizeInfo = reactive([] as any[]);

export const furnish = reactive({
  actBg: '',
  pageBg: '',
  actBgColor: '',
  ruleBtn: '',
  myPrizeBtn: '',
  shopNameColor: '',
  prizeContentBg: '',
  prizeBg: '',
  prizeNameColor: '',
  drawsNum: '',
  drawBtn: '',
  winnersBg: '',

  ruleBk: '',
  ruleTextColor: '',
  myPrizeBk: '',
  myPrizeTextColor: '',
  awardBk: '',
  notAwardBk: '',
  saveAddressBtn: '',
  confirmBtn: '',
  saveAddressBk: '',
  saveAddressColor: '',
  copyCardBk: '',
  copyCardTitle: '',
  copyCardText: '',
  noOrderBk: '',
  noOrderTextColor: '',
  joinBk: '',

  tipColor: '',
  tipImportColor: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const prizeContentBg = computed(() => ({
  backgroundImage: furnish.prizeContentBg ? `url(${furnish.prizeContentBg})` : '',
}));

const prizeBg = computed(() => ({
  backgroundImage: furnish.prizeBg ? `url(${furnish.prizeBg})` : '',
}));

const prizeNameColor = computed(() => ({
  color: furnish.prizeNameColor ?? '',
}));

const drawsNum = computed(() => ({
  color: furnish.drawsNum ?? '',
}));

const winnersBg = computed(() => ({
  backgroundImage: furnish.winnersBg ? `url(${furnish.winnersBg})` : '',
}));

const ruleBk = computed(() => ({
  backgroundImage: furnish.ruleBk ? `url(${furnish.ruleBk})` : '',
}));

const ruleTextColor = computed(() => ({
  color: furnish.ruleTextColor ?? '',
}));

const myPrizeBk = computed(() => ({
  backgroundImage: furnish.myPrizeBk ? `url(${furnish.myPrizeBk})` : '',
}));

const myPrizeTextColor = computed(() => ({
  color: furnish.myPrizeTextColor ?? '',
}));

const awardBk = computed(() => ({
  backgroundImage: furnish.awardBk ? `url(${furnish.awardBk})` : '',
}));

const notAwardBk = computed(() => ({
  backgroundImage: furnish.notAwardBk ? `url(${furnish.notAwardBk})` : '',
}));

const saveAddressBk = computed(() => ({
  backgroundImage: furnish.saveAddressBk ? `url(${furnish.saveAddressBk})` : '',
}));

const copyCardBk = computed(() => ({
  backgroundImage: furnish.copyCardBk ? `url(${furnish.copyCardBk})` : '',
}));

const copyCardTitle = computed(() => ({
  color: furnish.copyCardTitle ?? '',
}));

const copyCardText = computed(() => ({
  color: furnish.copyCardText ?? '',
  borderColor: furnish.copyCardText ?? '',
}));

const noOrderBk = computed(() => ({
  backgroundImage: furnish.noOrderBk ? `url(${furnish.noOrderBk})` : '',
}));

const noOrderTextColor = computed(() => ({
  color: furnish.noOrderTextColor ?? '',
}));

const joinBk = computed(() => ({
  backgroundImage: furnish.joinBk ? `url(${furnish.joinBk})` : '',
}));

const tipColor = computed(() => ({
  color: furnish.tipColor ?? '',
}));

const tipImportColor = computed(() => ({
  color: furnish.tipImportColor ?? '',
}));

export default {
  pageBg,
  shopNameColor,
  prizeContentBg,
  prizeBg,
  prizeNameColor,
  drawsNum,
  winnersBg,
  ruleBk,
  ruleTextColor,
  myPrizeBk,
  myPrizeTextColor,
  awardBk,
  notAwardBk,
  saveAddressBk,
  copyCardBk,
  copyCardTitle,
  copyCardText,
  noOrderBk,
  noOrderTextColor,
  joinBk,
  tipColor,
  tipImportColor,
};
