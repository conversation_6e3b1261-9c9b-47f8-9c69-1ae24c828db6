<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>{{ taskInfo[detail.taskType].label }}</div>
      <div class="rightLineDiv"></div>
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="content">
      <div v-if="detail.taskType === 8">
        <div class="tip-title">参与条件：</div>
        <div class="tip-text">
          <span
            >在
            <span class="color-cus">{{ dayjs(baseInfo.startTime).format('YYYY-MM-DD HH:mm:ss') }}~{{ dayjs(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
            期间，购买
            <span class="color-cus">{{ skuList.length ? '以下任意商品' : '店铺内任意商品' }}</span
            >，且订单状态为<span class="color-cus">已完成</span>
          </span>
        </div>
      </div>
      <div class="sku-list">
        <div class="sku" v-for="item in skuList" :key="item.skuId" @click="goSkuPage(item.skuId)">
          <img :src="item.skuMainPicture" alt="" class="sku-img" />
          <div class="sku-name">{{ item.skuName }}</div>
          <div v-if="detail.taskType === 5 && detail.optWay === 1">
            <div v-if="!item.isOperated" class="sku-button" @click="followSku(item.skuId)">点我关注</div>
            <div v-else class="sku-button sku-button-op">已关注</div>
          </div>
          <div v-else-if="detail.taskType === 7 && detail.optWay === 1">
            <div v-if="!item.status" class="sku-button" @click="addSku(item.skuId)">立即加购</div>
            <div v-else class="sku-button sku-button-op">已加购</div>
          </div>
          <div v-else-if="detail.taskType === 8">
            <div class="price">￥{{ item.jdPrice }}</div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="detail.taskType === 5">
      <!-- <div v-if="detail.optWay === 1" class="bottom-btn">
        已关注商品 <span class="num">{{ detail.taskUnitFinishCount }}</span
        >件，获得 <span class="num">{{ detail.taskFinishGiveAllLotteryCount }}</span
        >次抽奖机会
      </div> -->
      <div v-if="detail.optWay !== 1" class="bottom-btn" @click="followSku('')">一键关注商品</div>
    </div>
    <div v-if="detail.taskType === 7">
      <!-- <div v-if="detail.optWay === 1" class="bottom-btn">
        已加购商品 <span class="num">{{ detail.taskUnitFinishCount }}</span
        >件，获得 <span class="num">{{ detail.taskFinishGiveAllLotteryCount }}</span
        >次抽奖机会
      </div> -->
      <div v-if="detail.optWay !== 1" class="bottom-btn" @click="addSku('')">一键加购商品</div>
    </div>
    <div v-if="detail.taskType === 8" class="go-buy">
      <div class="bottom-btn">通过购物已获得{{ detail.taskFinishGiveAllLotteryCount }}次抽奖机会</div>
      <div class="bottom-btn go-shop" @click="gotoShopPage(baseInfo.shopId)">立即进店购物</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { addSkuToCart, gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { PropType, ref, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

const pros = defineProps({
  detail: {
    type: Object as PropType<any>,
  },
});

interface Sku {
  skuId: string;
  skuMainPicture: string;
  skuName: string;
  jdPrice: number;
  isOperated?: boolean;
  status?: number;
}

const skuList = ref([] as Sku[]);

const emits = defineEmits(['close', 'refreshTask']);

const close = () => {
  emits('close');
};

const taskInfo = {
  5: { label: '关注商品', button: '去关注', buttonDIs: '已关注' },
  7: { label: '加购商品', button: '立即加购', buttonDIs: '已加购' },
  8: { label: '购买商品', button: '去购买', buttonDIs: '已购买' },
};

// 获取用户在关注商品任务下的sku数据
const getFollowSkuTaskSkuList = async () => {
  try {
    const res = await httpRequest.post('/90101/getFollowSkuTaskSkuList', { taskId: pros.detail.id });
    if (res.code === 200) {
      skuList.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
};
// 加购商品查询
const getTaskAddSku = async () => {
  try {
    const res = await httpRequest.post('/90101/getTaskAddSku', { taskId: pros.detail.id });
    if (res.code === 200) {
      skuList.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
};
// 购买商品
const orderSku = async () => {
  try {
    const res = await httpRequest.post('/90101/getOrderSkuTaskSkuList', { taskId: pros.detail.id });
    if (res.code === 200) {
      skuList.value = res.data;
      // 对skuList中jdPrice除与100
      skuList.value.forEach((item) => {
        item.jdPrice /= 1000;
      });
    }
  } catch (error) {
    console.error(error);
  }
};
// 去商详页
const goSkuPage = (skuId: string) => {
  if (pros.detail.taskType === 8) {
    gotoSkuPage(skuId);
  }
};

const init = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  if (pros.detail.skuList) {
    skuList.value = pros.detail.skuList;
  } else if (pros.detail.taskType === 5) {
    await getFollowSkuTaskSkuList();
  } else if (pros.detail.taskType === 7) {
    await getTaskAddSku();
  } else if (pros.detail.taskType === 8) {
    await orderSku();
  }
  closeToast();
};

// 关注商品
const followSku = async (skuId: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90101/followSku', { skuId });
    closeToast();
    if (res.code === 200) {
      showToast('关注成功');
      emits('refreshTask');
      if (!skuId) return;
      const index = skuList.value.findIndex((item) => item.skuId === skuId);
      skuList.value[index].isOperated = true;
    } else {
      showToast(res.message);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 加购商品
const addSku = async (skuId: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90101/addSku', { skuId });
    closeToast();
    if (res.code === 200) {
      showToast('加购成功');
      emits('refreshTask');
      if (!skuId) {
        addSkuToCart(skuList.value.map((item) => item.skuId));
        return;
      }
      addSkuToCart(skuId);
      const index = skuList.value.findIndex((item) => item.skuId === skuId);
      skuList.value[index].status = 1;
    } else {
      showToast(res.message);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

init();
</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .tip-title {
    font-size: 0.3rem;
    color: #262626;
    font-weight: bold;
  }

  .tip-text {
    font-size: 0.24rem;
    color: #262626;
    font-weight: 500;
    margin-top: 0.27rem;

    .color-cus {
      color: #f2270c;
    }
  }

  .content {
    max-height: 60vh;
    min-height: 3rem;
    border-top: 0.3rem solid transparent;
    padding: 0 0.3rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
  }

  .sku-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .sku {
      width: 3.35rem;
      border-radius: 0.2rem;
      overflow: hidden;
      margin-bottom: 0.2rem;
      background-color: #fff;
    }

    .sku-img {
      width: 3.35rem;
      height: 3.35rem;
      object-fit: cover;
    }

    .sku-name {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
      font-size: 0.3rem;
      line-height: 0.35rem;
      height: 0.7rem;
      color: #333333;
      margin: 0.2rem;
    }

    .sku-button {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }

    .sku-button-op {
      background: #ffaca1;
    }

    .price {
      font-size: 0.24rem;
      color: #f2270c;
      margin-left: 0.2rem;
      margin-bottom: 0.2rem;
    }
  }

  .bottom-btn {
    height: 1rem;
    text-align: center;
    line-height: 1rem;
    font-size: 0.24rem;
    color: #262626;
    background: #ffe3e3;
  }

  .go-buy {
    display: flex;

    .bottom-btn {
      flex: 0.5;
    }

    .go-shop {
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    }
  }
}
</style>
