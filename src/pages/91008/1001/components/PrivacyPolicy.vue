<template>
  <div class="privacy-bg">
    <div class="text">
      敬请期待
<!--      1、本政策适用于【CPB肌肤之钥京东自营官方旗舰店】为其品牌会员所提供的服务。<br />-->
<!--      2、商家/品牌承诺对用户的个人信息严格保密，但经用户授权或给予法律及程序要求的除外。您知悉并同意，商家/品牌将通过短信、专属客服等渠道向您发送品牌及店铺最新消息。<br />-->
<!--      CPB肌肤之钥非常重视您的隐私和个人信息保护，限于字数，若您想了解您的个人信息利用方式，请点击链接：<a-->
<!--        href="https://minimembership.shiseido.com.cn/h5/Privacypolicy/policy.html"-->
<!--        rel="noopener noreferrer"-->
<!--        target="_blank"-->
<!--        >https://minimembership.shiseido.com.cn/h5/Privacypolicy/policy.html</a-->
<!--      >，仔细阅读《隐私政策》（下称“本政策”）。-->
<!--      本政策适用于我们所收集的个人信息，包括我们通过官方网站、在线商城、线下柜台、微信公众号、微信小程序等途径收集的个人信息。您选择使用我们的服务，即表示您已充分理解本政策的全部内容，并同意本政策以及本政策的更新，并同意我们根据本政策收集、使用、储存和共享您的个人信息。若您不同意您选择使用我们的服务，即表示您已充分理解本政策的全部内容，并同意本政策以及本政策的更新，并同意我们根据本政策收集、使用、储存和共享您的个人信息。若您不同意您选择使用我们的服务，即表示您已充分理解本政策的全部内容，并同意本政策以及本政策的更新，并同意我们根据本政策收集、使用、储存和共享您的个人信息。若您不同意您选择使用我们的服务，即表示您已充分理解本政策的全部内容，并同意本政策以及本政策的更新，并同意我们根据本政策收集、使用、储存和共享您的个人信息。若您不同意您选择使用我们的服务，即表示您已充分理解本政策的全部内容，并同意本政策以及本政策的更新，并同意我们根据本政策收集、使用、储存和共享您的个人信息。若您不同意<br />-->
<!--      您选择使用我们的服务，即表示您已充分理解本政策的全部内容，并同意本政策以及本政策的更新，并同意我们根据本政策收集、使用、储存和共享您的个人信息。若您不同意本政策或其任何一部分，请停止使用我们的服务。-->
    </div>
    <div class="close" @click="close"></div>
  </div>
</template>
<script lang="ts" setup>
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.privacy-bg {
  width: 5.97rem;
  height: 7.59rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/285038/25/26307/11970/680b032eFa174b943/bff0a894dfde2c32.png) no-repeat;
  background-size: 100%;
  position: relative;
  .text {
    width: 6rem;
    height: 6rem;
    color: #000;
    font-family: Arial, sans-serif; /* 选择适合的字体 */
    line-height: 1.5; /* 设置行高，以提高可读性 */
    position: absolute;
    top: 4rem;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 0 0.5rem;
    font-size: .2rem;
    overflow-y: auto; /* 添加滚动条，当内容溢出时显示滚动条 */
    a {
      color: #007bff;
      text-decoration: none;
      word-break: break-all; /* 允许单词内部断行 */
    }
  }
  .close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    bottom: -0.3rem;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
