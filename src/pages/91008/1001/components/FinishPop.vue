<template>
  <div class="member-bg">
    <div class="close" @click="close"></div>
  </div>
</template>
<script lang="ts" setup>
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.member-bg {
  width: 6.48rem;
  height: 4.33rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/226433/33/14759/13462/66cc4fedF9f4a9585/c4128b2b44f0f146.png) no-repeat;
  background-size: 100%;
  position: relative;
  .close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    bottom: -0.3rem;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
