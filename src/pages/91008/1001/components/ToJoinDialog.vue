<template>
  <!-- 上传图片弹窗 -->
  <div class="box">
    <div class="btn" @click="openCard"/>
    </div>
</template>

<script lang='ts' setup>
import { defineEmits, inject } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const emit = defineEmits(['closeDialog']);

const openCard = () => {
  console.log('入会');
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const closeDialog = () => {
  emit('closeDialog', 'join');
};
</script>
<style lang='scss'>
.van-popup--center {
  max-width: unset !important;
}
</style>
<style lang='scss' scoped>
.box {
  align-items: center;
  justify-content: flex-end;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/279056/31/26457/9754/680af0a0F8cc55f14/a5473de36d455228.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  z-index: 1;
  width: 5.89rem;
  height: 3.84rem;
  margin: 0 auto;
  text-align: center;
  display: flex;
  img {
    margin: 0 auto;
  }

  .popup {
    width: 6rem;
  }
  .btn {
    font-size: 0.25rem;
    //background-color: red;
    width: 2rem;
    height: 0.5rem;
    margin-right: 0.6rem;
  }

  .close-btn {
    width: 0.9rem;
    height: 0.7rem;
    /* cursor: pointer; */
    z-index: 10;
    margin: 1.8rem auto 0;
    //background-color: #fff;
  }
}
</style>
