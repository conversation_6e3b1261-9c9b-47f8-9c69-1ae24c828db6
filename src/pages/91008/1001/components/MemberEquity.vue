<template>
  <div class="member-bg">
    <div class="container">
      <section class="membership-section">
        <strong>敬请期待</strong>
<!--        <div class="membership-tier">-->
<!--          <strong>1. 会员身份获得</strong>-->
<!--          <p>在京东自营旗舰店入会绑卡即可成为银卡-粉丝会员，会员身份终身有效。</p>-->

<!--          <strong>2. 会员等级划分</strong>-->
<!--          <ul>-->
<!--            <li><strong>银卡-粉丝会员</strong>：入会绑卡即可。</li>-->
<!--            <li><strong>银卡-已购会员</strong>：入会绑卡，且近12个月内订单累计实付金额在0至3000元之间。</li>-->
<!--            <li><strong>金卡会员</strong>：入会绑卡，且近12个月内订单累计实付金额在3000元至8000元之间。</li>-->
<!--            <li><strong>白金卡会员</strong>：入会绑卡，且近12个月内订单累计实付金额在8000元至20000元之间。</li>-->
<!--            <li><strong>铂钻卡会员</strong>：入会绑卡，且近12个月内订单累计实付金额在20000元至48000元之间。</li>-->
<!--            <li><strong>黑钻卡会员</strong>：入会绑卡，且近12个月内订单累计实付金额在48000元以上。</li>-->
<!--          </ul>-->
<!--          <p class="note">温馨提示：会员在入会前确认收货的订单不参与等级计算。</p>-->

<!--          <strong>3. 会员等级有效期</strong>-->
<!--          <p>银卡-粉丝会员/银卡-已购会员等级终身有效；金卡/白金卡/铂钻卡/黑钻卡会员等级根据近12个月内的订单累计实付金额不定期重新评定。</p>-->

<!--          <strong>4. 会员等级升级/降级</strong>-->
<!--          <p>会员等级升级在订单确认收货后7个工作日内完成评定。</p>-->

<!--          <strong>5. 充值购物金是否参与会员等级计算</strong>-->
<!--          <p>购物金充值的订单不参与会员等级计算。</p>-->
<!--        </div>-->
      </section>

<!--      <section class="points-section">-->
<!--        <strong>关于会员积分</strong>-->
<!--        <div class="membership-tier">-->
<!--          <strong>1. 会员积分获得</strong>-->
<!--          <p>银卡至黑钻卡会员，购买正装实付1元=1积分。</p>-->
<!--          <p class="note">温馨提示：会员在入会前确认收货的订单不享积分。</p>-->

<!--          <strong>2. 消费订单积分计算</strong>-->
<!--          <p>非正装消费订单不计入积分和等级计算。</p>-->

<!--          <strong>3. 积分到账时间</strong>-->
<!--          <ul>-->
<!--            <li>购物积分：订单确认收货后7个工作日内到账。</li>-->
<!--            <li>互动积分：互动任务完成后7个工作日内到账。</li>-->
<!--          </ul>-->

<!--          <strong>4. 积分有效期</strong>-->
<!--          <p>每年1月1日对已满1年的未使用积分清零。</p>-->

<!--          <strong>5. 订单退款是否影响会员积分</strong>-->
<!--          <p>退款订单对应的积分会被扣除。</p>-->

<!--          <strong>6. 充值购物金是否享会员积分</strong>-->
<!--          <p>购物金充值的订单不享会员积分，但使用购物金付款的正装订单正常享积分。</p>-->
<!--        </div>-->
<!--      </section>-->

<!--      <section class="cross-channel-section">-->
<!--        <strong>关于接入CPB肌肤之钥全渠道会员体系</strong>-->
<!--        <p>CPB肌肤之钥京东自营官方旗舰店未接入全渠道会员体系，各渠道会员积分、等级不互通。温馨提示：各渠道均需自行操作入会绑卡。</p>-->
<!--      </section>-->
    </div>
    <div class="close" @click="close"></div>
  </div>
</template>
<script lang="ts" setup>
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.member-bg {
  width: 5.97rem;
  height: 7.59rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/282766/36/25744/11552/680b032eF407fc412/b68835c30af30597.png) no-repeat;
  background-size: 100%;
  position: relative;
  .container {
    width: 6rem; /* 容器宽度 */
    height: 6rem; /* 容器高度 */
    overflow-y: auto; /* 当内容超出容器高度时显示垂直滚动条 */
    // background-color: #fff; /* 背景色 */
    padding: 0.5rem; /* 内边距 */
    box-sizing: border-box; /* 盒模型设置为border-box，以包含内边距和边框在总宽度/高度内 */
    /* 可选：添加边框、阴影等样式 */
    // border: 1px solid #ccc; /* 边框 */
    // border-radius: 0.3rem; /* 圆角 */
    position: absolute;
    top: 4rem;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 0 0.5rem;
    font-size: .2rem;
    overflow-y: auto; /* 添加滚动条，当内容溢出时显示滚动条 */
  }

  /* 文案内容样式 */
  .container p,
  .container div,
  .container div,
  .container div,
  .container ul,
  .container li {
    margin: 0.2rem 0; /* 调整段落、标题、列表等元素的上下外边距 */
    padding: 0; /* 重置内边距 */
    line-height: 1.2; /* 行高，可根据需要调整 */
    color: #333; /* 文本颜色 */
  }

  /* 移除列表样式 */
  .container ul {
    list-style-type: none;
    padding-left: 0;
  }

  /* 其他特定样式 */
  .note {
    font-style: italic;
    color: #666;
  }

  .close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    bottom: -0.3rem;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
