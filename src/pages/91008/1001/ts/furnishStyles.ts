import { computed, reactive } from 'vue';

export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  // drawsNum: '', // 剩余抽奖次数颜色
  countDown: '', // 倒计时组件
  prizeBg: '', // 奖品背景
  ruleBg: '', // 规则背景
  getPrizeBg: '', // 领取奖品背景
  finishInfo: '', // 完善信息题目
  submitBtnBg: '', // 按钮背景
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));
const finishInfo = computed(() => ({
  backgroundImage: furnish.finishInfo ? `url("${furnish.finishInfo}")` : '',
}));
const submitBtnBg = computed(() => ({
  backgroundImage: furnish.submitBtnBg ? `url("${furnish.submitBtnBg}")` : '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  finishInfo,
  submitBtnBg,
};
