<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv"></div>
    <div class="info" :style="furnishStyles.finishInfo.value">
      <div class="info-form">
        <van-form ref="formEl" @submit="onSubmit" class="van-form">
          <van-cell-group inset>
            <div v-for="(item, index) in infoList" :key="index">
              <van-field v-if="item.personName === '姓名'" v-model="userName" :disabled="item.disable  || allDisable" class="field" name="姓名" maxlength="10" label="姓名" placeholder="姓名" :rules="[{ validator: validatorName, message: validatorNameMessage }]" />
              <van-field v-if="item.personName === '生日'" v-model="birthday" :disabled="item.disable || allDisable" class="field" name="生日" label="生日" readonly placeholder="点击选择生日" @click="changeDate" :rules="[{ required: true, message: '请填写生日' }]" />
              <van-field v-if="item.personName === '手机号'" v-model="phoneNumber" :disabled="item.disable || allDisable" class="field" name="手机号" label="手机号" placeholder="手机号" maxlength="11" :rules="[{ validator: validatorPhone, message: validatorPhoneMessage }]" />
              <van-field v-if="item.personName === '性别'" v-model="gender" :disabled="item.disable || allDisable" class="field" readonly name="性别" label="性别" placeholder="点击选择性别" @click="changeGender" :rules="[{ required: true, message: '请填写性别' }]" />
              <van-field v-if="item.personName === '邮箱'" v-model="email" :disabled="item.disable || allDisable" class="field" name="邮箱" maxlength="30" label="邮箱" placeholder="邮箱" :rules="[{ validator: validatorEmail, message: validatorEmailMessage }]" />
              <van-field v-if="item.personName === '地址'"  v-model="area" :disabled="item.disable || allDisable" is-link readonly class="field" name="地址" label="选择地址" placeholder="点击选择地址" @click="changeArea(item)" :rules="[{ required: true, message: '请填写地址' }]" />
            </div>
          </van-cell-group>
        </van-form>
<!--        <div class="check-box">-->
<!--          <div class="termCheckbox">-->
<!--            <van-checkbox v-model="termCheckbox" checked-color="#000" icon-size="12px"></van-checkbox>-->
<!--            <p>我已阅读和了解CPB肌肤之钥<span class="underline" @click="privacyPolicy = true">隐私政策</span>和<span class="underline" @click="memberEquity = true">会员权益</span>并且同意接受其中所有的条款。</p>-->
<!--          </div>-->
<!--        </div>-->
      </div>
    </div>
    <div class="submit" :style="furnishStyles.submitBtnBg.value" @click="checkForm">确认提交</div>
    <!--        <div class="submit" v-else :style="furnishStyles.submitBtnBg.value">信息已完善</div>-->
  </div>
  <!-- 聚合弹窗活动门槛 -->
  <Threshold2  v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!-- 非会员 -->
  <VanPopup teleport="body" v-model:show="showToJoinVip" position="center" :close-on-click-overlay="closeOnClickOverlay">
    <ToJoinDialog @close="showToJoinVip=false"></ToJoinDialog>
  </VanPopup>
  <!--  时间选择-->
  <van-popup v-model:show="showDatePicker" position="bottom">
    <van-date-picker v-model="pickerDate" @confirm="confirmBirthday" @cancel="showDatePicker = false" :max-date="maxDate" :min-date="minDate" />
  </van-popup>
  <!--  性别选择-->
  <van-popup v-model:show="showGenderPicker" position="bottom">
    <van-picker :columns="genderColumns" @confirm="confirmGender" @cancel="showGenderPicker = false" />
  </van-popup>
  <!--  地址选择-->
  <van-popup v-model:show="showArea" position="bottom">
    <van-area :area-list="areaList" @confirm="confirmArea" @cancel="showArea = false" />
  </van-popup>
  <!-- 隐私政策 -->
  <VanPopup teleport="body" v-model:show="privacyPolicy">
    <PrivacyPolicy :rule="ruleTest" @close="privacyPolicy = false"></PrivacyPolicy>
  </VanPopup>
  <!-- 会员权益 -->
  <VanPopup teleport="body" v-model:show="memberEquity">
    <MemberEquity :rule="ruleTest" @close="memberEquity = false"></MemberEquity>
  </VanPopup>
  <!-- 提交成功弹窗 -->
  <VanPopup teleport="body" v-model:show="isShowFinishPop">
    <FinishPop :rule="ruleTest" @close="isShowFinishPop = false"></FinishPop>
  </VanPopup>
  <!--提交确认弹窗-->
  <VanPopup teleport="body" v-model:show="checkInfo" position="bottom" @click="checkInfo = false">
    <CheckInfo v-if="checkInfo"></CheckInfo>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, inject, computed } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast, Toast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import PrivacyPolicy from '../components/PrivacyPolicy.vue';
import MemberEquity from '../components/MemberEquity.vue';
import FinishPop from '../components/FinishPop.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { areaList } from '@vant/area-data';
import CheckInfo from '../components/CheckInfo.vue';
import dayjs from 'dayjs';
import ToJoinDialog from '../components/ToJoinDialog.vue';
import useThreshold from '@/hooks/useThreshold';
import '../Threshold2/CPBStyle.scss';
import Threshold2 from '../Threshold2/ThresholdCPB.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const formEl = ref();
const termCheckbox = ref(true);
const noticeCheckbox = ref(false);
// 隐私政策
const privacyPolicy = ref(false);
// 会员权益
const memberEquity = ref(false);
// 提交成功
const isShowFinishPop = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
const prizeInfo = ref({
  activityPrizeId: '',
});
const showToJoinVip = ref(false);
const isFinished = ref(false);
const isFinishedBrith = ref(false);
// 展示门槛显示弹框
const showLimit = ref(false);
const openCardArr = baseInfo.thresholdResponseList.filter((item) => item.type === 1);
if (openCardArr.length > 0) {
  showToJoinVip.value = true;
} else {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList.filter((item) => item.type !== 1),
    className: 'common-message-cpb',
  });
}
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const userName = ref('');
const phoneNumber = ref('');
const email = ref('');

const infoList = ref([]);
const maxDate = new Date();
const minDate = new Date(new Date().getFullYear() - 100, new Date().getMonth(), new Date().getDate());

// 生日
const birthday = ref();

const pickerDate = ref([dayjs().format('YYYY'), dayjs().format('MM'), dayjs().format('DD')]);
const showDatePicker = ref(false);
const changeDate = () => {
  if (!isFinishedBrith.value) {
    showDatePicker.value = true;
  }
};
const confirmBirthday = (res: any) => {
  pickerDate.value = res.selectedValues;
  birthday.value = res.selectedValues.join('/');
  showDatePicker.value = false;
};

// 性别修改
const showGenderPicker = ref(false);
const gender = ref('');
const genderColumns = [
  { text: '男', value: '1' },
  { text: '女', value: '0' },
];
const changeGender = () => {
  if (!isFinished.value) {
    showGenderPicker.value = true;
  }
};
const confirmGender = ({ selectedOptions }: { selectedOptions: { text: string } }) => {
  gender.value = selectedOptions[0]?.text;
  showGenderPicker.value = false;
};

// 住址修改
const showArea = ref(false);
const area = ref('');
const allDisable = ref(false); // 如果生日填写则所有信息都不可以再填写
const changeArea = (itemData) => {
  if (itemData.disable || allDisable.value) {
    return;
  }
  showArea.value = true;
};
const confirmArea = ({ selectedOptions }: any) => {
  showArea.value = false;
  area.value = selectedOptions.map((item: any) => item.text).join('/');
};

// 获取填写的信息列表
const getInfo = async (dataList) => {
  try {
    const { data } = await httpRequest.post('/91008/person');
    // console.log(data, '查询已填写信息');
    // const data = {
    //   address: '',
    //   birth: '',
    //   email: '',
    //   male: '',
    //   name: '',
    //   phone: '',
    // };
    dataList.forEach((item: any) => {
      item.disable = false; // 先默认所有信息未填写
      if (item.personName === '生日') {
        // 填过生日，不许修改生日
        if (data.birth) {
          birthday.value = data.birth;
          item.disable = true;
          allDisable.value = true;
        } else {
          birthday.value = '';
        }
      }

      if (item.personName === '姓名') {
        if (data.name) {
          userName.value = data.name;
          item.disable = true;
        } else {
          userName.value = '';
        }
      }
      if (item.personName === '手机号') {
        if (!data.phone) {
          phoneNumber.value = '';
        } else {
          item.disable = true;
          phoneNumber.value = data.phone;
        }
      }
      if (item.personName === '性别') {
        if (!data.male) {
          gender.value = '';
        } else {
          item.disable = true;
          gender.value = data.male;
        }
      }
      if (item.personName === '邮箱') {
        if (!data.email) {
          email.value = '';
        } else {
          item.disable = true;
          email.value = data.email;
        }
      }
      if (item.personName === '地址') {
        if (!data.address) {
          area.value = '';
        } else {
          item.disable = true;
          area.value = data.address;
        }
      }

    });
    infoList.value = dataList;
    console.log(dataList, 'dataList===');
  } catch (e) {
    showToast(e.message);
  }
};
const getActivity = async () => {
  try {
    const { data } = await httpRequest.post('/91008/activity');
    await getInfo(data);
  } catch (error) {
    console.error(error);
  }
};

const isStart = ref(false);
const startTime = ref();
const endTime = ref();
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

const onSubmit = async () => {
  lzReportClick('tjxx');
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const submitData = {
      name: userName.value,
      phone: phoneNumber.value,
      male: gender.value,
      birth: birthday.value,
      email: email.value,
      address: area.value,
    };
    // console.log(submitData, 'submitData=====');
    await httpRequest.post('/91008/addInfo', {
      ...submitData,
    });
    showToast({
      message: '填写成功',
      duration: 2000,
      onClose: (() => {
        getActivity();
      }),
    });
    // isShowFinishPop.value = true;
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
  console.log('submit');
};

const checkInfo = ref(false);

// 姓名校验
const validatorName = (val: string) => {
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (!val) {
    return false;
  }
  if (reg.test(val)) {
    return false;
  }
  return true;
};

const validatorNameMessage = (val: string) => {
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (!val) {
    return '请输入姓名';
  }
  if (reg.test(val)) {
    return '姓名不能包含表情';
  }
  return '';
};

const validatorPhoneMessage = (val: string) => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!val) {
    return '请输入手机号';
  }
  if (!checkPhone.test(val)) {
    return '请输入正确的手机号';
  }
  return '';
};
const validatorPhone = (val: string) => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!val) {
    return false;
  }
  if (!checkPhone.test(val)) {
    return false;
  }
  return true;
};
const validatorEmailMessage = (val: string) => {
  const checkEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+$/;
  if (!val) {
    return '请输入邮箱';
  }
  if (!checkEmail.test(val)) {
    return '请输入正确的邮箱';
  }
  return '';
};
const validatorEmail = (val: string) => {
  const checkEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+$/;
  if (!val) {
    return false;
  }
  if (!checkEmail.test(val)) {
    return false;
  }
  return true;
};
// 检查表单
const checkForm = async () => {
  const openCardArr = baseInfo.thresholdResponseList.filter((item) => item.type === 1);
  if (openCardArr.length > 0) {
    showToJoinVip.value = true;
    return;
  }
  const openCardArr1 = baseInfo.thresholdResponseList.filter((item) => item.type !== 1);
  // console.log(openCardArr1, 'openCardArr1========');
  if (baseInfo.thresholdResponseList.length > 0 && openCardArr1.length > 0) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList.filter((item) => item.type !== 1),
      className: 'common-message-cpb',
    });
    // showLimit.value = true;
    console.log(showLimit.value, 'openCardArr1========213123');
    return;
  }
  if (allDisable.value) {
    showToast('您已经提交过信息了哦~');
    return;
  }
  await formEl.value.validate();
  if (termCheckbox.value) {
    await onSubmit();
    isFinishedBrith.value = true;
  } else {
    showToast('请您仔细阅读隐私政策及会员权益');
  }
};

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivity()]);
    closeToast();
    if (baseInfo.status === 1) {
      setTimeout(() => {
        // showToast('活动未开始');
      }, 1000);
      closeToast();
      return;
    }
    if (baseInfo.status === 3) {
      setTimeout(() => {
        // showToast('活动已结束');
      }, 1000);
      closeToast();
      return;
    }
  } catch (error) {
    closeToast();
  }
};

init();
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.5rem;
  padding-top:2.90rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/271312/29/25843/16690/6809e621F68b44b24/919f405d872d2445.png');
  background-color: #ecd7ba;
  position: relative;
}

.info {
  width: 6.77rem;
  height: 7.94rem;
  margin: 0 auto 0 auto;
  position: relative;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 0.5rem 0.1rem 0;
  overflow: hidden;
  .info-form {
    height: auto;
    font-size: 5rem;
    border-radius: 0 0 0.3rem 0.3rem;
    overflow: hidden;
    overflow-y: scroll;

    .van-form {
      min-height: 3rem;
      max-height: 6rem;
      overflow: hidden;
      overflow-y: scroll;
    }

    .field {
      border: none;
      font-size: 0.25rem;
      --van-field-label-width: 1rem;
      min-height: 1rem;
      //line-height: 1rem;
      display: flex;
      align-items: center;
      padding-top: 0;
      padding-bottom: 0;
      border-bottom: 0.02rem solid #000;
      border-top: none;
    }
    .check-box {
      font-size: 0.23rem;
      padding: 0 0.7rem;
      position: absolute;
      bottom: 0.3rem;
      .termCheckbox {
        display: flex;
        justify-content: flex-start;
        align-items: start;
        padding-bottom: 0.15rem;
        .van-checkbox {
          width: 0.5rem;
          height: 0.5rem;
          display: flex;
          align-items: flex-start;
          padding-top: 0.06rem;
        }
        .underline {
          text-decoration: underline;
          text-decoration-color: #b0a8a5;
        }
        p {
          text-align: center;
        }
      }
    }
  }
}

.submit {
  width: 2.80rem;
  height: 0.55rem;
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0.26rem auto 0.2rem auto;
  color: white;
  font-size: 0;
  text-align: center;
  line-height: 0.55rem;
  letter-spacing: 0.08rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/284249/13/25527/12177/6809e301F5949fda6/fd68a32019168c32.png');
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
.van-cell {
  border-bottom: 1px solid #000;
}
.van-cell-group--inset {
  border-radius: 0;
}
.info-form {
  .van-cell-group {
    background: transparent;
  }
  .van-cell {
    background: transparent;
  }
}
</style>
