import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/73422/37/24682/94803/65a4ff35F5481275e/4468d332c1d0d762.png',
  actBgColor: '#fef3e4',
  shopNameColor: '#000',
  btnColor: '#e2dacf',
  btnBg: '#ffffff',
  btnBorderColor: '#ffffff',
  prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/181783/27/40927/5865/6541a799F132b6f29/13d12e94fb16f45a.png',
  ruleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/87795/20/33857/5806/6541a799F006e526b/70706a4a7beb2e26.png',
  countDown: 'https://img10.360buyimg.com/imgzone/jfs/t1/225565/27/853/3654/6541ad94F5c7f2013/8a7958c349378faf.png',
  prizeListImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/205000/25/22540/56298/6295a8aeE539e8fd0/cb70c28f8503aa7d.png',
  finishInfo: 'https://img10.360buyimg.com/imgzone/jfs/t1/240144/31/3378/14872/65a5e902F5d4f9f42/30e04cf15475ecb6.png',
  submitBtnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/231349/10/12251/3107/65a4ff33Fad233d4f/65a73b1438cefa19.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/234076/18/1387/61595/6541a798Fd7db570c/19fa6ebb6d2587a6.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/234076/18/1387/61595/6541a798Fd7db570c/19fa6ebb6d2587a6.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/234076/18/1387/61595/6541a798Fd7db570c/19fa6ebb6d2587a6.png',
};
const baseInfo1 = {
  thresholdResponseList: [
  //   {
  //   btnContent: "",
  //   icon: null,
  //   sort: 2,
  //   thresholdCode: 2,
  //   thresholdContent: "敬请关注其他活动",
  //   thresholdStatus: 0,
  //   thresholdTitle: "抱歉，活动已结束",
  //   type: 0,
  // },
    {
      btnContent: '立即关注',
      icon: 5,
      sort: 5,
      thresholdCode: 5,
      thresholdContent: '必须是关注店铺用户方可参加活动',
      thresholdStatus: 0,
      thresholdTitle: '您不是关注店铺用户',
      type: 2,
    },
    {
      btnContent: '去升级',
      icon: 7,
      sort: 3,
      thresholdCode: 7,
      thresholdContent: '会员等级要求：二星会员,三星会员,四星会员,五星会员',
      thresholdStatus: 0,
      thresholdTitle: '会员等级不满足',
      type: 3,
    }],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '完善信息';
  app.provide('pathParams', pathParams);
  app.provide('baseInfo', baseInfo1);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
