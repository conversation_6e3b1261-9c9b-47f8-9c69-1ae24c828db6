<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/132945/25/27003/524870/624d0471E967292db/3135a9470c63fbd2.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div class="header-btn-all">
          <div class="header-btn"  :style="furnishStyles.headerBtn.value" v-click-track="'hdgz'" @click="showRulePopup()"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-click-track="'wdjp'" @click="showMyPrize = true"><div>我的奖品</div></div>
        </div>
      </div>
      <div class="prizeDivAll" :style="{'backgroundImage':'url(' + furnish.prizeBgAll + ')'}">
        <div class="prizeContentDiv" :style="{'backgroundColor': furnish.prizeBgColor }">
          <div class="leftDiv" :style="{'backgroundImage':'url(' + prizeData.prizeImg + ')'}">
          <div class="prizeStatusNoneDiv" v-if="prizeData.status === 4" :style="{'backgroundImage':'url(' + furnish.prizeStatusNoneBg + ')'}"></div>
          <div class="prizeStatusReceivedDiv" v-else-if="prizeData.status === 3" :style="{'backgroundImage':'url(' + furnish.prizeStatusReceivedBg + ')'}"></div>
            <div class="backDiv">{{PrizeTypeName[prizeData.prizeType]}}</div>
          </div>
          <div class="rightDiv">
            <div class="prizeNameDiv">{{prizeData.prizeName ? prizeData.prizeName : '--'}}</div>
            <div class="textDiv">奖品数量有限，先到先得哦~</div>
            <div class="restDiv">奖品剩余:{{prizeData.prizeNum ? prizeData.prizeNum : 0}}份</div>
          </div>
        </div>
      </div>
      <div class="btnDivAll">
        <div class="btnDiv" v-if="prizeData.isFollow === 2" :style="{'backgroundImage':'url(' + furnish.followShop + ')'}" v-threshold-click="followShopClick"></div>
        <div v-else>
          <div class="btnGrayDiv" v-if="prizeData.status === 3" :style="{'backgroundImage':'url(' + furnish.drawBtnBg + ')'}"></div>
          <div class="btnDiv" v-else-if="prizeData.status === 1" :style="{'backgroundImage':'url(' + furnish.drawBtnBg + ')'}" v-threshold-click="lotteryDraw"></div>
          <div class="btnGrayDiv" v-else :style="{'backgroundImage':'url(' + furnish.drawBtnBg + ')'}" v-threshold-click="lotteryDraw"></div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div>
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @savePhone="showSavePhone" @showCardNum="showCardNum"></MyPrize>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @savePhone="showSavePhone" @showCardNum="showCardNum"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="activityPrizeId" :userPrizeId="userPrizeId" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
    </VanPopup>
    <!-- 领取京元宝权益 -->
    <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
      <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
    </VanPopup>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo, PrizeTypeName } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const shopName = ref(baseInfo.shopName);
const prizeData = ref({
  prizeImg: '',
  prizeName: '',
  prizeNum: 0,
  prizeType: 0,
  status: 0,
});
const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const showMyPrize = ref(false);

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string, userPrizeIds: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  userPrizeId.value = userPrizeIds;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

// 主接口
const getPrizesData = async () => {
  try {
    const { data } = await httpRequest.post('/10069/getPrizes', {
      x: 'activity',
    });
    closeToast();
    if (data.status === 1) {
      // 1可领取 2不可领取 3已领取 4 已发完
      if (data.prizeNum <= 0) {
        data.status = 4;
      }
    }
    prizeData.value = data;
    console.log(data, '主接口数据=======');
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};
// 关注店铺
const followShopClick = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 2000,
    overlay: true,
  });
  try {
    const res = await httpRequest.post('/common/followShop');
    closeToast();
    await getPrizesData();
  } catch (error: any) {
    showToast(error.message);
  }
};
// 抽奖接口
const lotteryDraw = async () => {
  try {
    lzReportClick('kscj');
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10069/sendPrize');
    closeToast();
    award.value = res.data;
    showAward.value = true;
    await getPrizesData();
  } catch (error) {
    console.error(error);
    showToast({
      message: error.message,
      duration: 2000,
      onClose: (() => {
        getPrizesData();
      }),
    });
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getPrizesData()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color:#f2f2f2
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }
  .header-btn-all{
    .header-btn {
      width: 1.28rem;
      height:0.49rem;
      margin-bottom: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      text-align: center;
      border-radius: 0.22rem;
      border: 0.01rem;
      border-style: solid;
      font-size: 0.2rem;
    }
  }
}
.prizeDivAll{
  padding-top: 0.75rem;
  width: 6.9rem;
  height: 3.4rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/89515/6/26880/12016/624d572eE936c26d5/ead791217526c6ed.png);
  background-repeat: no-repeat;
  margin: 0.2rem auto;
  background-size: 100%;
  display: flex;
  .prizeContentDiv{
    width: 6.4rem;
    height: 2.4rem;
    padding: 0.2rem;
    display: flex;
    border-radius: 0.1rem;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    margin: 0px auto;
    position: relative;
    .leftDiv{
      width: 2rem;
      height: 2rem;
      border: 2px solid #f2270c;
      border-radius: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      position: relative;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .prizeStatusNoneDiv{
        width: 1.2rem;
        height: 1.2rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/207926/6/20219/7916/624e431fEe53ca351/4ceaad436c6ff8a3.png);
        background-size: 100%;
        background-repeat: no-repeat;
        position: absolute;
      }
      .prizeStatusReceivedDiv{
        width: 1.2rem;
        height: 1.2rem;
        background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/216244/24/16748/8014/624d572eEe1717d14/347f084339fe7452.png);
        background-size: 100%;
        background-repeat: no-repeat;
        position: absolute;
      }
      .backDiv{
        width: 1.96rem;
        height: 0.7rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
        background-size: 100%;
        background-repeat: no-repeat;
        position: absolute;
        bottom: -2px;
        left: 0;
        text-align: center;
        padding-top: 0.32rem;
        font-size: 0.24rem;
        color: #fff;
      }
    }
    .rightDiv{
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-left: 0.43rem;
      .prizeNameDiv{
        text-align: left;
        color: rgb(38, 38, 38);
      }
      .textDiv{
        text-align: left;
        margin-top: 0.22rem;
        color: rgb(140, 140, 140);
        font-size: 0.22rem;
      }
      .restDiv{
        text-align: right;
        margin-top: 0.58rem;
      }
    }
  }
}
.btnDivAll{
  .btnDiv{
    width: 6.9rem;
    height: 1rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/114673/16/24459/82046/624d572eE006c0965/ade2af887e2cf39c.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin-left: 50%;
    transform: translate(-50%);
  }
  .btnGrayDiv{
    width: 6.9rem;
    height: 1rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/114673/16/24459/82046/624d572eE006c0965/ade2af887e2cf39c.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin-left: 50%;
    transform: translate(-50%);
    filter: grayscale(1);
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

</style>
