<template>
  <div class='box'>
    <div class='close' @click='closePopup()'></div>
    <div class='box-bk'>
      <div class='content'>
        <div class='gift-image'>
          <img :src='currentGift.prizeImg' alt=''>
        </div>
        <div class='gift-name'>{{ currentGift.prizeName }}</div>
        <div class='botton' @click='handleDrawBtn(currentGift)'>{{ currentGift.prizeType === 3 ? '填写地址' : '我知道了' }}</div>
        <div class='tip'>（可至我的{{ getTip(currentGift.prizeType) }}中查看）</div>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { PropType } from 'vue';
import { IGift } from '../ts/type';
import { closePopup, openPopup } from '../ts/popup';

const props = defineProps({
  currentGift: {
    type: Object as PropType<IGift>,
  },
});

const getTip = (type: number) => {
  switch (type) {
    case 1:
      return '券包';
    case 901:
      return '皮肤';
    case 902:
      return '背景';

    default:
      return '奖品';
  }
};

const handleDrawBtn = (gift: IGift) => {
  if (gift.prizeType === 3) {
    openPopup('addressPopup');
  } else {
    closePopup();
  }
};
</script>

<style scoped lang='scss'>

.box {
  width: 6rem;
  height: 8rem;
  position: relative;

  .box-bk {
    width: 6rem;
    height: 7rem;
    position: absolute;
    top: 1rem;
    left: 0;
    padding-top: 1.8rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/178689/30/51621/7356/672c72d2F47ef108a/01d4c24eed10609c.png");
      repeat: no-repeat;
      size: contain;
    };
  }

  .close {
    width: .4rem;
    height: .4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/156491/25/50723/319/672acd28Fbda234d2/2910e6697479f05f.png");
      repeat: no-repeat;
      size: contain;
    };
    position: absolute;
    top: .3rem;
    right: 0;
  }

  .content {
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    text-align: center;

    .gift-image {
      width: 2.65rem;
      height: 2.65rem;
      display: flex;
      margin: 0 auto;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        max-height: 100%;
      }
    }

    .gift-name {
      font-size: .22rem;
      color: #7C7C7C;
      margin-top: .12rem;
    }

    .botton {
      width: 3.61rem;
      height: .61rem;
      line-height: .65rem;
      text-align: center;
      font-size: .4rem;
      border-radius: .3rem;
      margin: .6rem auto 0;
      color: #FFFFFF;
      background-color: #CA8970;
    }

    .tip {
      font-size: .22rem;
      color: #7C7C7C;
      margin-top: .12rem;
    }
  }
}

</style>
