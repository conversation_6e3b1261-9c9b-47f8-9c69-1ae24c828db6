import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/', // 主页
    name: 'index',
    component: () => import('../views/index.vue'),
  },
  {
    path: '/send/detail', // 实物发货详情
    name: 'sendDetail',
    hidden: true,
    component: () => import('../views/SendDetail.vue'),
  },
  {
    path: '/gift/card/detail', // 礼品卡详情
    name: 'giftCardDetail',
    hidden: true,
    component: () => import('../views/GiftCardDetail.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
    name: 'notFound',
    hidden: true,
  },
];

// 路由
function initializeRouter() {
  const router = createRouter({
    history: createWebHistory(`${process.env.VUE_APP_PATH_PREFIX_NO_CDN}999999/1001/`),
    routes,
  });
  return router;
}

export default () => initializeRouter();
