<template>
  <div class="bg">
    <div class="state-box" :style="{'background-image':stateBgList[stateInfo.inKindStatus]}">
      <div class="state-title">{{ stateTextList[stateInfo.inKindStatus] }}</div>
      <div class="state-Icon" :style="{ 'background-image': 'url(' + stateIconList[stateInfo.inKindStatus] + ')' }"/>
    </div>
    <div class="address-box">
      <div><van-icon name="location-o" color="#0083ff"/></div>
      <div class="address-info-box">
        <div class="receive-info"><span class="receiver-name">{{stateInfo.receiverName}}</span><span>{{stateInfo.mobile}}</span></div>
        <div class="address-info">{{stateInfo.address}}</div>
      </div>
    </div>
    <div class="goods-box">
      <div><van-icon name="point-gift-o" color="#0083ff"/></div>
      <div class="goods-info-box">
        <div class="goods-info"><div>{{stateInfo.rightsName}}</div><div>x{{stateInfo.rightsNum}}</div></div>
        <div class="express-delivery-info" v-show="stateInfo.inKindStatus === 1"><div class="express-delivery">{{stateInfo.deliverName}}</div><div>运单号：<span>{{stateInfo.deliverNo}}</span></div></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';

export interface stateInfoData {
  inKindStatus: any,
  receiverName: string,
  mobile: any,
  address: string,
  rightsName: string,
  rightsNum: any,
  deliverName: string,
  deliverNo: any,
}
const route = useRoute();
const stateInfo = ref(route.query.stateInfo ? JSON.parse(route.query.stateInfo) as stateInfoData : {
  inKindStatus: '',
  receiverName: '',
  mobile: '',
  address: '',
  rightsName: '',
  rightsNum: '',
  deliverName: '',
  deliverNo: '',
});
const stateBgList = ['linear-gradient(90deg, #fd483f 0%, #ff794c 100%)', 'linear-gradient(90deg, #779dff 0%, #7fbfff 100%)', 'linear-gradient(90deg, #cccccc 0%, #e6e6e6 100%)'];
const stateTextList = ['未发货', '已发货', '已取消'];
const stateIconList = ['//img10.360buyimg.com/imgzone/jfs/t1/238237/24/6447/1141/6572b2ccFb30a9457/a99ffc5d3622950c.png', '//img10.360buyimg.com/imgzone/jfs/t1/226988/40/7033/3732/6572b2cbF77fdbae5/44a6e6d022fe8398.png', '//img10.360buyimg.com/imgzone/jfs/t1/237262/1/6636/4146/6572b2caF187bf2e9/b3de5d6f46e1d1c4.png'];
</script>

<style scoped lang="scss">
.bg {
  position: relative;
  background-color: #F2F2F2;
  height: 100vh;
  .state-box {
    position: absolute;
    top: 0.2rem;
    width: 7.5rem;
    height: 1.9rem;
    .state-title {
      position: absolute;
      top: 0.47rem;
      left: 0.3rem;
      font-family: MicrosoftYaHei;
      font-size: 0.3rem;
      color: #ffffff;
    }
    .state-Icon {
      position: absolute;
      top: 0.29rem;
      right: 0.63rem;
      width: 0.68rem;
      height: 0.64rem;
      background-size: cover;
    }
  }
  .address-box {
    position: absolute;
    top: 1.39rem;
    left: 0.3rem;
    z-index: 20;
    padding: 0 0.34rem;
    display: flex;
    align-items: center;
    width: 6.9rem;
    height: 1.8rem;
    background-color: #ffffff;
    border-radius: 0.1rem;
    .address-info-box {
      margin-left: 0.3rem;
      .receive-info {
        margin-bottom: 0.18rem;
        font-family: PingFang-SC-Bold;
        font-size: 0.3rem;
        color: #333333;
        .receiver-name {
          margin-right: 0.36rem;
        }
      }
      .address-info {
        font-family: PingFang-SC-Regular;
        font-size: 0.2rem;
        color: #666666;
      }
    }
  }
  .goods-box {
    position: absolute;
    top: 3.39rem;
    left: 0.3rem;
    z-index: 20;
    padding: 0 0.34rem;
    display: flex;
    align-items: center;
    width: 6.9rem;
    height: 1.8rem;
    background-color: #ffffff;
    border-radius: 0.1rem;
    .goods-info-box {
      margin-left: 0.3rem;
      flex: 1;
      .goods-info {
        display: flex;
        justify-content: space-between;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #333333;
      }
      .express-delivery-info {
        margin-top: 0.22rem;
        display: flex;
        font-family: PingFang-SC-Regular;
        font-size: 0.2rem;
        color: #666666;
      }
      .express-delivery {
        margin-right: 0.19rem;
        padding: 0 0.12rem;
        height: 0.32rem;
        line-height: 0.32rem;
        text-align: center;
        background-color: #f2f2f2;
      }
    }
  }
}
</style>
