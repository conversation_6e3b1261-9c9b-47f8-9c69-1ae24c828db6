<template>
  <div class="bk">
    <div class="tip">订单状态为已完成7天后才可以领取京豆</div>
    <div class="record">
      <div class="item" v-for="(item, index) in record" :key="index">
        <div class="text">
          <div>订单编号：{{ item.orderId }}</div>
          <div>订单金额：￥{{ item.orderAmount }}</div>
        </div>
        <div class="text">
          <div>下单时间：{{ dayjs(item.orderTime).format('YYYY.MM.DD HH:mm:ss') }}</div>
          <div>订单状态：{{ item.orderStatus }}</div>
        </div>
        <div class="line"></div>
        <div class="footer">
          <div class="num">{{ item.canReceive !== 2 ? '预计可领' : '已领' }}京豆数：{{ item.count }}个&nbsp;&nbsp;&nbsp;&nbsp;价值：{{ item.value }}元</div>
          <div class="btn gray" v-if="item.canReceive === 1 && item.value == 0">联系客服登记</div>
          <div class="btn" v-else-if="item.canReceive !== 2 && item.value > 0" :class="{ gray: item.canReceive !== 1 }" @click="getOnePrize(item)">立即领取</div>
          <div class="btn gray" v-else>已领取</div>
        </div>
      </div>
      <div class="no-data" v-if="!record.length">暂无数据~</div>
    </div>
    <div class="footer-btn" v-if="record.length">
      <div class="btn" @click="getAllPrize">一键领取</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';
import dayjs from 'dayjs';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const record = ref<any[]>([]);

const getRecord = async () => {
  try {
    const { data } = await httpRequest.post('/member/starbucks/pay/member/getRebateDetail');
    record.value = data;
    return true;
  } catch (error: any) {
    showToast(error.message);
    return false;
  }
};

const init = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      duration: 0,
      forbidClick: true,
    });
    await getRecord();
    closeToast();
  } catch (error: any) {
    showToast(error.message);
  }
};
init();

const getPrize = async (receivePrizeId: string) => {
  try {
    const { data } = await httpRequest.post('/member/starbucks/pay/member/renovation/receiveRebate/h5', {
      receivePrizeId,
    });
    return data;
  } catch (error: any) {
    return false;
  }
};

const getOnePrize = async (item: any) => {
  if (item.canReceive === 4 && Number(item.value) >= 100) {
    showToast('领取失败，请联系客服');
    return;
  }
  if (item.canReceive === 4) {
    showToast('温馨提示：本月至高可领取100元福利，您已满足本月领取条件，剩余京豆可延至下月提取~');
    return;
  }

  if (item.canReceive !== 1) {
    showToast('订单状态为已完成7天后才可以领取京豆');
    return;
  }
  showLoadingToast({
    message: '领取中...',
    duration: 0,
    forbidClick: true,
  });
  lzReportClick('ljlq');
  const data = await getPrize(item.receivePrizeId);
  if (data && data?.status) {
    closeToast();
    showToast('领取成功');
    getRecord().then();
  } else {
    showToast('领取失败，请联系客服');
  }
};

const getAllPrize = () => {
  const canDraw = record.value.filter((item) => item.canReceive === 1 && item.value > 0);
  const receivePrizeId = canDraw.map((item) => item.receivePrizeId);
  if (!receivePrizeId.length) {
    showToast('暂无可领取京豆');
    return;
  }
  showLoadingToast({
    message: '领取中...',
    duration: 0,
    forbidClick: true,
  });
  // 循环领取，一个结束再领取下一个
  const draw = async (index: number) => {
    if (index >= receivePrizeId.length) {
      closeToast();
      showToast('领取完成');
      getRecord();
      return;
    }
    lzReportClick('xhlq');
    const res = await getPrize(receivePrizeId[index]);
    if (res && res?.status) {
      draw(index + 1);
    } else {
      showToast('领取失败，请联系客服');
      getRecord();
    }
  };
  draw(0);
};
</script>

<style>
@font-face {
  font-family: 'OPPOSans';
  src: url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-S-M.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-S-M.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'OPPOSans';
}
.notice-marquee {
  display: none;
}
</style>

<style scoped lang="scss">
.bk {
  min-height: 100vh;
  background-color: #f3f3f3;
  padding-bottom: 1.5rem;
  .tip {
    font-size: 0.24rem;
    color: #7f8080;
    padding: 0.25rem;
    text-align: center;
  }
  .record {
    padding: 0 0.25rem;
  }
  .item {
    position: relative;
    background-color: #fff;
    border-radius: 0.15rem;
    padding: 0.3rem;
    margin-bottom: 0.25rem;
    overflow: hidden;
    .text {
      font-size: 0.2rem;
      color: #7f8080;
      margin-bottom: 0.1rem;
      display: flex;
      div:first-child {
        flex: 0.6;
      }
      div:nth-child(2) {
        flex: 0.4;
      }
    }
    .line {
      height: 0.01rem;
      background-color: #cccccc;
      margin: 0.2rem 0;
    }
    .footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .num {
        font-size: 0.2rem;
        color: #4d4d4d;
      }
      .btn {
        width: 1.8rem;
        height: 0.5rem;
        background-image: linear-gradient(0deg, #642516 0%, #842c20 100%);
        border-radius: 0.25rem;
        color: #fff;
        font-size: 0.24rem;
        line-height: 0.5rem;
        text-align: center;
      }
      .gray {
        background: #b3b3b3;
      }
    }
  }
  .no-data {
    font-size: 0.28rem;
    color: #7f8080;
    text-align: center;
    padding-top: 30vh;
  }
}
.footer-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1.5rem;
  background-color: #fff;
  padding-top: 0.3rem;
  .btn {
    width: 4.8rem;
    height: 0.8rem;
    background-image: linear-gradient(0deg, #642516 0%, #842c20 100%);
    border-radius: 0.4rem;
    font-size: 0.36rem;
    color: #fff;
    text-align: center;
    line-height: 0.8rem;
    margin: 0 auto;
  }
}
</style>
