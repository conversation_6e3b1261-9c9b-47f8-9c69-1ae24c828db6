import { computed, reactive } from 'vue';

export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  cutDownBg: '', // 倒计时背景
  cutDownColor: '', // 倒计时字体颜色
  cutDownNumColor: '', // 倒计时数字颜色
  cutDownNumBg: '', // 倒计时数字背景
  calendarBg: '', // 日历背景
  prizeBg: '', // 奖品背景
  prizeNameColor: '', // 奖品名称颜色
  getPrizeBtn: '', // 领取按钮
  signInBeforeIcon: '', // 签到前图标
  signInAfterIcon: '', // 签到后图标
  signInBeforeBt: '', // 签到前按钮图片
  signInAfterBt: '', // 签到后按钮图片
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const prizeNameColor = computed(() => ({
  color: furnish.prizeNameColor ?? '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));

const cutDownColor = computed(() => ({
  color: furnish.cutDownColor ?? '',
}));

const cutDownBg = computed(() => ({
  backgroundImage: furnish.cutDownBg ? `url(${furnish.cutDownBg})` : '',
}));

const calendarBg = computed(() => ({
  backgroundImage: furnish.calendarBg ? `url(${furnish.calendarBg})` : '',
}));

const cutDownNumColor = computed(() => ({
  color: furnish.cutDownColor ?? '',
  backgroundColor: furnish.cutDownNumBg ?? '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  calendarBg,
  cutDownBg,
  cutDownColor,
  prizeNameColor,
  cutDownNumColor,
};
