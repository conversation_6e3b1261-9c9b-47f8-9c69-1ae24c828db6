<!--
 * @actName:
 * @author: <PERSON><PERSON><PERSON> lin
-->
<template>
  <!-- result-background -->
  <div class='page-view'>
    <img class='logo' src='../assets/img/logo-icon.png' alt=''>

    <img class='result-title' src='../assets/img/equity-title.png' alt=''>

    <div class='equity-view'>
      <div class='equity-item' v-for='(item,index) in prizeList' :key='index'>
        <div class='equity-bg' :class="{'equity-bg-active':index===currentEquityIndex}" >
          <img :src='item.prizePicture' alt='' @click='chooseEquity(item,index)'>
        </div>
        <div class='equity-name'>{{item.prizeName}}</div>
      </div>
    </div>

    <img class='result-btn' src='../assets/img/sure-btn.png' v-click-track="'ljlq-new2'" @click="handleResult('')" alt=''>
    <img class="bottom" src="../assets/img/bottom-text.png" alt="">

  </div>

  <VanPopup teleport="body" v-model:show="showSureDrawDialog" :close-on-click-overlay="false">
    <SureDrawDialog @closeDialog='showSureDrawDialog= false' @sureDraw='sureDraw'></SureDrawDialog>
  </VanPopup>
</template>

<script lang='ts' setup>
import { ref, inject, computed } from 'vue';
import { showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { getDataInterface } from '../ts/port';
import SureDrawDialog from '../components/SureDrawDialog.vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';

const router = useRouter();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const store = useStore();

const prizeList = ref([]);
// 手机号
const successPhone = computed(() => store.getters.getSuccessPhone);

const showSureDrawDialog = ref(false);

const currentEquityIndex = ref();
const chooseEquity = async (e: any, index: number) => {
  // 查询库存
  const res = await getDataInterface('checkPrizeStock', 'post', { prizeId: prizeList.value[index].prizeId });
  // console.log(res.data);
  if (res.result) {
    // 选中奖品
    currentEquityIndex.value = index;
  } else {
    showToast('该权益已领完，请重新选择');
  }
};

const handleResult = () => {
  if (!(currentEquityIndex.value >= 0)) {
    showToast('请先选择权益奖品');
    return;
  }
  showSureDrawDialog.value = true;
};

const sureDraw = async () => {
  showSureDrawDialog.value = false;
  // 领取新客礼2
  const res = await getDataInterface('sendNewGift2Prize', 'post', {
    prizeId: prizeList.value[currentEquityIndex.value].prizeId,
    mobile: successPhone.value,
  });
  // status 0 未中奖  1 中奖
  if (res.data.status === 1) {
    console.log('领取成功');
    store.commit('setUserType', 'pass');
    store.commit('setPrizeList', {
      prizeName: res.data.prizeName,
      prizeImg: res.data.prizeImg,
      skuId: res.data.skuId,
    });
    router.replace({ path: '/result' });
  } else {
    console.log('领取失败');
    store.commit('setUserType', 'fail');
    router.replace('/result');
  }
};
// 获取新客权益2奖品信息
const getNewPrizeInfo = async () => {
  const res = await getDataInterface('getNewGift2PrizeInfo', 'post');
  // console.log(res.data);
  if (res.data) {
    prizeList.value = res.data;
  }
};
getNewPrizeInfo();
</script>

<style lang='scss'>
.page-view {
  width: 100%; /* 宽度自适应 */
  min-height: 100vh;
  padding: 1.9rem 0 0.2rem;
  position: relative;
  text-align: center;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/281303/24/4269/45429/67d9437eF2a2c7784/9fe50b4d988ce4ac.png");
    repeat: no-repeat;
    size: 100% 100%;
  };

  .logo {
    width: 2.48rem;
    position: absolute;
    top: .5rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .result-title {
    width: 5.21rem;
    margin: 0 auto;
  }

  .equity-view {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-evenly;
    margin: 1.7rem auto 0;

    .equity-item {
      text-align: center;
      color: #9d5322;

      .equity-bg {
        margin: 0 auto;
        width: 2.84rem;
        height: 2.84rem;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 0.2rem 0 0;
        background: {
          image: url("../assets/img/equity-item.png");
          repeat: no-repeat;
          size: contain;
        };
        img {
          width: 2.3rem;
        }
      }
      .equity-name {
        width: 2.7rem;
        overflow: hidden;
        height: 0.8rem;
        margin: 0 auto;
        line-height: 0.4rem;
        /* text-align: left; */
        /* background-color: aliceblue; */
        font-size: 0.25rem;
      }

      .equity-bg-active {
        background: {
          image: url("../assets/img/equity-item-active.png") !important;
        };
      }
    }
  }

  .result-btn {
    //width: 2.69rem;
    margin: 0.8rem auto 0;
  }
  .bottom{
    margin: 3rem auto 0;
    width: 7.2rem;
  }
}

</style>
