import { computed, reactive } from 'vue';

export const furnish = reactive({
  actBg: '',
  pageBg: '',
  actBgColor: '',
  stepImg: '',
  canNotJoinKv: '',
  jumpLink: '',
  disableShopName: false,
  cmdImg: '',
  h5Img: '',
  mpImg: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const stepImg = computed(() => ({
  backgroundImage: furnish.stepImg ? `url("${furnish.stepImg}")` : '',
}));

export default {
  pageBg,
  stepImg,
};
