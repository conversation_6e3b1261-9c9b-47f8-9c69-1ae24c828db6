<template>
  <div class="rule-bk">
<!--    <div class="title">-->
<!--      <img src="//img10.360buyimg.com/imgzone/jfs/t1/229892/2/9728/15203/658551deF2f33797b/71778b88816624c9.png" alt="" class="text" />-->
      <div class="close" @click="close"></div>
<!--    </div>-->
    <div class="content">
      <div class="content-height px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
        <div v-if="skuList.length || data.length">
          <div class="sku-list-box text-gray-400 text-sm flex justify-center pb-4">
            <div class="grid grid-cols-2 gap-2">
              <div v-for="(item, index) in skuList.length ? skuList : data" class="bg-white py-2 px-3.5" :key="index" @click="gotoSkuPage(item.skuId)">
                <div class="flex justify-center">
                  <img class="w-32 h-32" :src="item.skuMainPicture" alt="" />
                </div>
                <div class="text-xs mt-5 lz-multi-ellipsis--l2" v-text="item.skuName"></div>
                <div class="text-red-500 text-xs mt-3">¥ <span v-text="item.jdPrice"></span></div>
              </div>
            </div>
            <div class="load-more" @click="handleLoadMore">加载更多</div>
          </div>
        </div>
        <div v-else class="no-data">
          活动商品为本店全部商品
          <div class="btn" @click="gotoShopPage">进店逛逛</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { gotoSkuPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps(['data']);
const emits = defineEmits(['close', 'openShowGoShop']);

const close = () => {
  emits('close');
};
const gotoShopPage = () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  emits('openShowGoShop');
};
const skuList = ref<any[]>([]);
const pageNum = ref(1);

// 获取曝光商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/80090/getExposureSkuPage', {
      pageNum: pageNum.value,
      pageSize: 20,
      type: 1,
    });
    if (data.records.length === 0 && pageNum.value > 1) {
      showToast({
        message: '没有更多数据了',
        duration: 2000,
      });
      return;
    }
    skuList.value.push(...data.records);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
const handleLoadMore = async () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  pageNum.value++;
  await getSkuList();
};
if (!isPreview) {
  getSkuList();
} else {
  watch(props.data, () => {
    skuList.value = props.data;
  });
}
</script>

<style scoped lang="scss">
//.title {
//  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
//}
.no-data {
  text-align: center;
  padding-top: 2rem;
  font-size: 0.3rem;
  .btn {
    width: 2.4rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    color: white;
    font-size: 0.3rem;
    border-radius: 0.1rem;
    background-color: #ff9900;
    margin: 0.3rem auto;
  }
}
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/135319/38/42324/19752/65f8f2c2Fc6dc39cc/86822b4362b30639.png) no-repeat;
  background-size: 100%;
  width: 100vw;
  padding-top:0.86rem;

  //.title {
  //  position: relative;
  //  height: 0.86rem;
  //  display: flex;
  //  align-items: flex-end;
  //  justify-content: space-between;
  //  padding: 0 0.33rem;
  //  .text {
  //    height: 0.6rem;
  //  }
  //}

  .close {
    width: 0.55rem;
    height: 0.55rem;
    background-repeat: no-repeat;
    background-size: 100%;
    position:absolute;
    right:0.24rem;
    top:0.28rem;
  }

  .content {
    width: 7rem;
    margin: 0.3rem auto 0;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .content-height{
      height: 7rem;
      overflow: hidden;
      overflow-y: scroll;
    }

    .no-data {
      font-size: 0.24rem;
      color: #999999;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/232560/34/9110/4825/65854f01Fdebcf9a2/a37203c75a9d6936.png) no-repeat;
      background-size: 100%;
      height: 6.6rem;
      border-radius: 0.3rem;
      //display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.no-more{
  margin: 0 auto;
  text-align: center;
  color: #eeeeee;
}
.sku-list-box {
  position: relative;
  padding-bottom: 1rem;
  .load-more {
    width: 2rem;
    height: 0.4rem;
    line-height: 0.4rem;
    text-align: center;
    background: linear-gradient(to right, #ff1f53, #ffd102);
    border-radius: 0.2rem;
    color: white;
    position: absolute;
    bottom: 0.24rem;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 600;
  }
}
</style>
