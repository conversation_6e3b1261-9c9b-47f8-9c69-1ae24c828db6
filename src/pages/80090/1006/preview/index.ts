import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/188902/34/39832/234153/65321c8bF676c8a11/38b8c73ca0504df7.png',
  pageBg: '',
  actBgColor: '#3278b7',
  shopNameColor: '#000000',
  btnColor: '#ffffff',
  btnBg: '',
  btnBorderColor: '#df4226',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/242310/38/545/2604/65854223F47cabc8a/a78ad29812b5bf0e.png',
  myPrizeBg: '',
  myOrderBg: '',
  cutDownBg: '',
  cutDownColor: '#f2280c',
  prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/133121/4/40892/36650/65321c90Fc8d22dd9/ee5d3292b8308a69.png',
  prizeNameColor: '#000000',
  getPrizeBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/45668/31/21377/3750/63072ce9E149122d1/cafaa96f82228c46.png',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/232408/14/9454/38135/65854223Fed7cc06a/20e74f3c789fafb6.png',
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/233878/3/285/26299/65321c90Fe3f8f369/bbf57d6780fa1f92.png',
  btnToShop: 'https://img10.360buyimg.com/imgzone/jfs/t1/177438/13/39160/9317/65321c90F092295c0/1e0febe0ac3dd7b8.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/187730/38/39135/86130/65321c8dFb8345558/f22056a877ddb6f5.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/187730/38/39135/86130/65321c8dFb8345558/f22056a877ddb6f5.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/187730/38/39135/86130/65321c8dFb8345558/f22056a877ddb6f5.png',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
