<template>
  <CommonDrawer title="活动规则" @close="emits('close')">
    <div class="h-[40vh] px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs pb-4" v-html="rule"></div>
  </CommonDrawer>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';

const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);
</script>

<style scoped lang="scss">
.title {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
}
</style>
