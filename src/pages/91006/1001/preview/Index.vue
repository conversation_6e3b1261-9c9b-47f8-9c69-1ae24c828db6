<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <!-- 动态生成的热区按钮 -->
    <div class="hotBtn" v-click-track="`btn${index + 1}`" :style="furnishStyles.showData.value" @click="ShowToast"></div>
    <div class="header-kv" @click="onSelected(1)">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/281916/40/14063/139906/67ece270F4150e86b/7d93a290a3fbef22.png'" alt="" class="kv-img"/>
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="{'backgroundImage': 'url(' + furnish.ruleBg + ')' }" @click="showRule = true"><div></div></div>
          <div class="header-btn" :style="{'backgroundImage': 'url(' + furnish.myPrizeBg  + ')'}" @click="showMyPrize = true"><div></div></div>

        </div>
      </div>
    </div>
    <div class="prizeDivAll">
      <div class="prizeTitle" :style="{'backgroundImage': 'url(' + furnish.prizeTitle + ')' }"></div>
      <div class="prizeList" :style="{'backgroundImage': 'url(' + furnish.prizeBg + ')' }">
        <div class="prizeItem" v-for="(item,index) in prizeList" :key="index">
          <div class="prizeImage" :style="{'backgroundImage': 'url(' + item.prizeImg + ')' }"></div>
          <div class="prizeName" :style="{'color':  furnish.prizeNameColor }">{{item.prizeName}}</div>
          <div class="prizeBottom">
            <div class="prizeRest" :style="{'color':  furnish.prizeRestColor }">剩余数量：{{item.sendTotalCount ?item.sendTotalCount : 0}}</div>
            <div class="prizeChange" :style="{'backgroundImage': 'url('+ furnish.getPrizeBtn +')' }"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="sku" v-if="showSelect || isExposure === 1">
      <img class="title-img" :src="furnish.exposureTitle" alt="" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
      <div class="sku-list-all" :style="{'background':  furnish.exposureListBg }">
        <div class="moreDiv" :style="{'backgroundImage': 'url('+ furnish.moreSku +')' }"></div>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text=""
            class="sku-list"
            @load="loadMore"
          >
            <div class="sku-item" v-for="(item, index) in skuListPreview" :key="index">
              <div class="sku-top" :style="{'backgroundImage': 'url('+ furnish.exposureItemBg +')' }">
                <img :src="item.skuMainPicture" alt="">
                <div class="sku-text" :style="{'color':  furnish.exposureNameColor }">{{item.skuName}}</div>
              </div>
              <div class="sku-btns" :style="{'backgroundImage': 'url('+ furnish.exposureBtn +')' }" @click="ShowToast">
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
<!--        <div class="sku-list">-->
<!--          <div class="sku-item" v-for="(item,index) in skuListPreview" :key="index">-->
<!--            <div class="sku-top" :style="{'backgroundImage': 'url('+ furnish.exposureItemBg +')' }">-->
<!--              <img :src="item.showSkuImage" alt="">-->
<!--              <div class="sku-text" :style="{'color':  furnish.exposureNameColor }">{{item.skuName}}</div>-->
<!--            </div>-->
<!--            <div class="sku-btns" :style="{'backgroundImage': 'url('+ furnish.exposureBtn +')' }">-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
      </div>
    </div>
    <div class="bottom-div">
      <div class="goToShop" @click="ShowToast" :style="{'backgroundImage': 'url('+ furnish.btnToShop +')' }"></div>
      <div class="nextMonth" :style="{'backgroundImage': 'url('+ furnish.nextMonthBg +')' }"></div>
    </div>
    <VanPopup teleport="body" v-model:show="showRule" position="center">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="center">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, reactive, computed } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultHhotZoneList, defaultSkuList, defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';
import { gotoSkuPage } from 'utils/platforms/jump';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const isLoadingFinish = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
}
const prizeList = ref<Prize>(defaultStateList);

type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>(defaultSkuList); // 曝光商品
const skuList = ref<Sku[]>([]); // 曝光商品

const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showAward = ref(false);

const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};
const loading = ref(false); // 是否加载中
const finished = ref(false);
const refreshing = ref(false);
const loadMore = async () => {
  console.log('加载更多');
  if (skuListPreview.value.length < 20) {
    finished.value = true;
    loading.value = false;
  }
};
const onRefresh = () => {
  console.log('刷新====');
  if (skuListPreview.value.length > 20) {
    finished.value = true;
    loading.value = false;
    showToast('活动预览，仅供参考');
  } else {
    finished.value = false;
  }
};
// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  console.log(data, '活动数据监听');
  prizeList.value = data.prizeList;
  if (data.skuList) {
    skuList.value = data.skuList;
    skuListPreview.value = data.skuList;
  }
  ruleTest.value = data.rules;
  isExposure.value = data.isExposure;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  if (activityData) {
    const data = activityData;
    prizeList.value = data.prizeList;
    // else {
    //   prizeList.value = defaultStateList;
    // }
    if (data.skuList) {
      skuList.value = data.skuList;
      skuListPreview.value = data.skuList;
    }
    ruleTest.value = data.rules;
    isExposure.value = data.isExposure;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}
.prize-select-box{
  padding-bottom: 0.6rem;
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}
.hotBtn{
  z-index: 10;
}
.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0;
  }

  .header-btn {
     width: 1.78rem;
    height: 0.60rem;
    cursor: pointer;
    background-size: 100%;
    background-repeat: no-repeat;
  }
}
.prizeDivAll{
  margin-top:-1rem;
  position: relative;
  .prizeTitle{
    width: 3.98rem;
    height: 1.75rem;
    background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/272990/21/14982/12966/67ece987Fe5c07aec/7dfdae03a17abbf8.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin-left:calc(50% - 3.98rem / 2)
  }
  .prizeList{
    width: 7.5rem;
    height: 14.80rem;
    background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/283981/19/14184/39028/67ece273Fb39dcecc/91c513b727120fac.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin-top:-1.08rem;
    padding-top:1.08rem;

    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    place-content: flex-start space-between;
    position: relative;
    overflow-y: scroll;
    .prizeItem{
      width: 50%;
      margin-bottom: 0.1rem;
      overflow: hidden;
      padding-top:0.32rem;
      display: flex;
      flex-direction: column;
      /* justify-content: center; */
      align-items: center;
      .prizeImage{
        width:2rem;
        height:2rem;
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .prizeName{
       color: #e60014;
        font-size:0.32rem;
        font-weight: bold;
      }
      .prizeBottom{
        display:flex;
        align-items: center;
        justify-content: center;
        .prizeRest{
          color:#000;
          font-size:0.21rem;
        }
        .prizeChange{
          filter: grayscale(1);
          width:1.42rem;
          height:0.42rem;
          background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/284846/28/13977/6194/67eced5dF5f9ecbd3/f9e619ed241c6300.png);
          background-size: 100%;
          background-repeat: no-repeat;
          margin-left:0.3rem;
        }
      }
    }
  }
}
.sku{
  width: 7.5rem;
  margin: -3.02rem auto 0;
  padding: 0.2rem;
  position: relative;
  .title-img{
    height: 2.23rem;
    width: 5.56rem;
    margin: 0 auto;
    background-repeat:no-repeat;
    background-size: 100%;
  }
  .sku-list-all{
    background:#f1f1f1;
    padding: 0.15rem 0.08rem;
    .moreDiv{
      width: 3.34rem;
      height: 0.47rem;
      background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/275060/22/14977/4435/67ecf3e8F81a9a932/0690b2a82b3e9584.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
    .sku-list{
      margin-top:0.3rem;
      justify-content: space-between;
      flex-wrap: wrap;
      display: flex;
      width: 100%;
      place-content: flex-start space-between;
      max-height: 10.45rem;
      position: relative;
      overflow-y: scroll;
      .sku-item{
        width: 3.45rem;
        margin-bottom: 0.1rem;
        overflow: hidden;
        .sku-top{
          width: 3.47rem;
          height: 4.43rem;
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/270561/12/15329/9443/67ecf1ddF5426d85d/7205bc11425038b6.png);
          background-size: 100%;
          background-repeat: no-repeat;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          img{
            display: block;
            width: auto;
            height: 2.45rem;
          }
          .sku-text{
            max-width: 3.0rem;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            font-size: 0.27rem;
            color: #fff;
            padding: 0 0.2rem;
            margin: 0.2rem 0 0.2rem 0;
            box-sizing: border-box;
            text-align: center;
          }
        }
        .sku-btns{
          width: 2.89rem;
          height: 0.61rem;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          margin: 0.07rem auto;
          background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/283842/10/13433/9641/67ece272F37710db5/d86d0dd2d1742163.png);
        }
      }
    }
    }
  }

.bottom-div {
  .goToShop{
    width: 6.61rem;
    height: 0.74rem;
    background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/273165/27/14839/12016/67ece271Fb9397b19/724adf947aafa0c8.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin-left:calc(50% - 6.61rem / 2);
  }
  .nextMonth{
    margin-top:0.43rem;
    width: 6.81rem;
    height: 7.66rem;
    background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/271670/10/13827/92169/67ece275F0cb77084/47a595234f72f286.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin-left:0.69rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
