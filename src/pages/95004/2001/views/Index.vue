<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" >
    <div class="header-kv select-hover" >
      <img :src="furnish.pageBg" alt=""/>
    </div>
    <div class="header-btn-all" >
      <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
        <div>{{ btn.name }}</div>
      </div>
    </div>
    <div class="countDown" >
   <CountDown
        :endTime="endTime"
        :startTime="startTime"
        v-if="baseInfo.status === 2"
      />
    </div>
    <div class="select-hover wheelClass" >
      <div class="wheel">
        <lz-lucky-wheel ref="myLucky" width="90vw" height="90vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
      </div>
      <div class="chance">
        *有效消费金额 {{orderPrice}} 元，共获得 {{totalChance}} 次抽奖机会
      </div>
      <div class="figure">
        <div class="draws-num" >您今天还有<span>{{chanceNum}}</span>次抽奖机会</div>
      </div>
    </div>

    <div class="prizeBox" v-if="prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length>0">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/208127/5/42656/9188/668ce2a9Fc24685e2/35510c2e4d00be7a.png" alt=""/>
      <div class="box">
        <div class="swiper-container-prize" ref="swiperRef" >
          <div class="swiper-wrapper " >
            <div class="swiper-slide " v-for="(item,index) in prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与')" :key="index">
              <div class="prizeItem" v-if="item.prizeName">
                <img :src="item.prizeImg  " alt=""/>
                <div class="prizeName">{{item.prizeName }}</div>
              </div>
            </div>
          </div>
          <div v-if="prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length>2" class="swiper-button-prev" @click="prevSwiper"></div>
          <div v-if="prizeInfo.filter((it:any)=>it.prizeName !== '谢谢参与').length>2" class="swiper-button-next" @click="nextSwiper"></div>
        </div>
      </div>

    </div>

    <div class="winners">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/2059/20/23071/9887/668ce2a9F33b931d8/928a95bd1e6da0cc.png" alt=""/>
      <div class="winners-content">
        <div class="winner-list swiper-container-winner" ref="swiperRef" v-if="activityGiftRecords.length >0">
          <div class="swiper-wrapper" >
            <div class="winner swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
              <div>
                <img v-if="!item.avatar" src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" />
                <img v-else :src="item.avatar" alt="" />
                <span>{{ item.nickName }}</span>
              </div>
              <span>{{ item.prizeName }}</span>
            </div>
          </div>
        </div>
        <div v-else>
          <p class="winner-null">暂无记录</p>
        </div>
      </div>
    </div>

  </div>
  <!-- 活动门槛 -->
  <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false;getWinners()" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
    <OrderRecordPopup v-if="showOrderRecord" @close="showOrderRecord = false" ></OrderRecordPopup>
  </VanPopup>
  <!--抽奖记录弹窗-->
  <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
    <DrawRecordPopup @close="showDrawRecord = false" />
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :userPrizeId="userPrizeId" @close="saveAddressClose"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="userPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import Swiper, { Autoplay } from 'swiper';

import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import Threshold2 from '@/components/Threshold2/index.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import SavePhone from '../components/SavePhone.vue';
import CountDown from '../components/CountDown.vue';

import 'swiper/swiper.min.css';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';

import { closeToast, showLoadingToast, showToast } from 'vant';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import useThreshold from '@/hooks/useThreshold';
import { httpRequest } from '@/utils/service';

import { DecoData } from '@/types/DecoData';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);
let prizeSwiper: Swiper;
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
// 时间定义
const endTime = ref(0);
const startTime = ref(0);
const totalChance = ref(0);
const orderPrice = ref(0);
const isCanRecive = ref(false);
// 展示门槛显示弹框
const showLimit = ref(false);
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});
const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
const prevSwiper = () => {
  prizeSwiper.slidePrev();
};
const nextSwiper = () => {
  prizeSwiper.slideNext();
};
// 抽奖次数
const chanceNum = ref(0);

const showDrawRecord = ref(false);
const showMyPrize = ref(false);

const showGoods = ref(false);
const showOrderRecord = ref(false);

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  userPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '我的订单>',
    event: () => {
      showOrderRecord.value = true;
    },
  },
  {
    name: '活动规则',
    event: () => {
      console.log('活动规则');
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    event: () => {
      console.log('我的奖品');
      showMyPrize.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  userPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);
// 活动主接口
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/95004/activity');
    console.log(data);
    const nowTime = dayjs().valueOf();
    if (nowTime >= data.drawStartTime && nowTime <= data.drawEndTime) {
      isCanRecive.value = true;
    } else {
      isCanRecive.value = false;
    }
    endTime.value = data.drawEndTime;
    startTime.value = data.drawStartTime;

    orderPrice.value = data.orderPrice;
  } catch (error) {
    console.error(error);
  }
};
// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/95004/chanceNum');
    chanceNum.value = data.chanceNum;
    totalChance.value = data.total;
  } catch (error) {
    console.error(error);
  }
};

const myLucky = ref();
// 抽奖接口
const lotteryDraw = async () => {
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  try {
    const res = await httpRequest.post('/95004/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      myLucky.value.stop(index);
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      myLucky.value.stop(index);
    }
  } catch (error) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    myLucky.value.stop(index);
    console.error(error);
  }
  getChanceNum();
};
const startCallback = async () => {
  lzReportClick('kscj');
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  if (!isCanRecive.value) {
    showToast('请在抽奖时间范围内进行抽奖');
    return;
  }
  if (chanceNum.value <= 0) {
    showToast('您的抽奖次数已用完');
    return;
  }
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  lotteryDraw();
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/95004/getPrizes');
    prizeInfo.splice(0);
    prizeInfo.push(...data);
    console.log(prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length);
    nextTick(() => {
      prizeSwiper = new Swiper('.swiper-container-prize', {
        direction: 'horizontal',
        loop: prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length > 2,
        slidesPerView: 2 || 'auto',
        spaceBetween: prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length > 2 ? 30 : 0,
        centeredSlides: prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length !== 2,
        navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
      });
    });
  } catch (error) {
    console.error(error);
  }
};

// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/95004/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    await nextTick(() => {
      const mySwiper = new Swiper('.swiper-container-winner', {
        autoplay: activityGiftRecords.length > 4 ? {
          delay: 1000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        } : false,
        direction: 'vertical',
        loop: activityGiftRecords.length > 4,
        slidesPerView: 4,
        loopedSlides: 8,
        spaceBetween: 10,
      });
    });
  } catch (error) {
    console.error(error);
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityInfo(), getChanceNum(), getPrizes(), getWinners()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
  if (baseInfo.status === 1) {
    const time = baseInfo.startTime - dayjs().valueOf();
    setTimeout(() => {
      window.location.reload();
    }, time);
  }
};
init();
// 保存地址弹窗关闭
const saveAddressClose = (type: any) => {
  if (type) {
    showSaveAddress.value = false;
    showMyPrize.value = false;

    init();
  } else {
    showSaveAddress.value = false;
  }
};
onMounted(() => {
  nextTick(() => {
    prizeSwiper = new Swiper('.swiper-container-prize', {
      direction: 'horizontal',
      loop: prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length > 2,
      slidesPerView: 2 || 'auto',
      spaceBetween: prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length > 2 ? 30 : 0,
      centeredSlides: prizeInfo.filter((it:any) => it.prizeName !== '谢谢参与').length !== 2,
      navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
    });
  });
});
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  .kv-img {
    width: 100%;
  }

}
.header-btn-all{
  display: flex;
  justify-content: space-around;
  margin: 0.3rem 0;
  .header-btn {
    width: 2rem;
    height: 0.56rem;
    border-radius: 0.35rem;
    border: 1px solid #f6fbf7;
    background: #fff;
    font-size: 0.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.countDown {
  width: 100%;
  margin: 0.3rem auto;
}
.wheelClass{
  position: relative;
  .wheel {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    .wheel-img {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      object-fit: contain;
    }
  }
}
.chance {
  margin-top: .5rem;
  color: #69b17d;
  font-size: 0.24rem;
  text-align: center;
}
.prizeBox {
  font-size: 0.3rem;
  color: #fff;
  overflow: hidden;
  position: relative;
  .box {
    width: 80%;
    margin:0 auto;
    overflow: hidden;
  }
  img {
    width: 4rem;
    margin: 0.2rem auto;
  }
  .prizeItem {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      width: 1.5rem;
      background: #fff;
    }
    .prizeName {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: center;
    }
  }}

.winners {
  width: 6.96rem;
  height: 7.36rem;
  margin: 0 auto;
  padding-top: 0.8rem;
  img {
    width: 4rem;
    margin: 0.2rem auto;
  }
  .winners-content {
    width: 6.6rem;
    height: 5.34rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
    .winner-list {
      width: 100%;
      height: 100%;
      overflow: hidden;
      //padding: 0 0.3rem;
    }
  }
}
.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem;
  //margin-top: 0.1rem;
  // background: #ffffff;
  border-radius: 0.1rem;

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.winner-null {
  text-align: center;
  line-height: 4.9rem;
  font-size: 0.24rem;
}
.draws-num {
  width: 5.94rem;
  height: 0.66rem;
  display: flex;
  background-size: 100% 100%;
  border-radius: 0.35rem;
  background-repeat: no-repeat;
  margin: 0.36rem 0.8rem 0;
  align-items: center;
  justify-content: center;
  padding: 0.06rem 0;
  font-size: 0.4rem;
  color: #fff;
  span {
    padding: 0.04rem 0.08rem;
    color: #e4c783;;
  }
}
.swiper-button-prev {
  position: absolute;
  left: 0;
  top: 70%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/180230/35/37580/745/66755088F83e4bb06/fe2cefbfd6ece6c5.png') no-repeat;
  background-size: 100%;
  z-index: 1;
}
.swiper-button-next {
  z-index: 1;
  position: absolute;
  right: 0;
  top: 70%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/233542/28/20707/755/66755089Fd2d6dab8/628521ff436b49fa.png') no-repeat;
  background-size: 100%;
}
.bottom-div {
  padding-top: 0.8rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #fff;
  text-align: center;
}
</style>
