<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/231616/37/9833/1864/658574f0F2d4c424d/fb7d1d5e85db4e8e.png" alt="" class="text" />
      <div class="close" @click="emits('close')"></div>
    </div>
    <div class="h-[40vh] px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="text-gray-400 text-sm flex justify-center pb-4" v-if="skuList.length || data.length">
        <div class="grid grid-cols-2 gap-2">
          <div v-for="(item, index) in skuList.length ? skuList : data" class="bg-white py-2 px-3.5" :key="index" @click="gotoSkuPage(item.skuId)">
            <div class="flex justify-center">
              <img class="w-32 h-32" :src="item.skuMainPicture" alt="" />
            </div>
            <div class="text-xs mt-5 lz-multi-ellipsis--l2" v-text="item.skuName"></div>
            <div class="text-red-500 text-xs mt-3">¥ <span v-text="item.jdPrice"></span></div>
          </div>
          <div class="w-[6.34rem] text-gray-400 text-xs text-center my-1" v-if="skuList.length > 4 || data.length > 4">—— 没有更多了 ——</div>
        </div>
      </div>
      <div v-else class="no-data">
        活动商品为本店全部商品
        <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps(['data']);
const emits = defineEmits(['close']);
const skuList = ref<any[]>([]);
// 获取曝光商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/95004/getExposureSku', { type: 1 });
    skuList.value = data as any[];
    closeToast();
  } catch (error) {
    closeToast();
  }
};
if (!isPreview) {
  getSkuList();
} else {
  watch(props.data, () => {
    skuList.value = props.data;
  });
}
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/222600/40/35224/219814/65016dfeF9234602d/d99de4f864849a24.png) no-repeat;
  background-size: 100%;
  width: 100vw;
  min-height: 7rem;
  .title {
    position: relative;
    height: 1rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem 0.1rem 0.33rem;
    .text {
      height: 0.62rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }
}
.no-data {
  text-align: center;
  padding-top: 3rem;
  font-size: 0.3rem;
  color: #fff;
  .btn {
    width: 2.4rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    color: white;
    font-size: 0.3rem;
    border-radius: 0.1rem;
    background-color: #ff9900;
    margin: 0.3rem auto;
  }
}
</style>
