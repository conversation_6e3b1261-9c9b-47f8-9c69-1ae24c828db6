<template>
  <div class="rule-bk">
    <div class="title">
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="type">
          <span>{{ prizeType[item.prizeType] }}</span>
          <span>{{ item.userPrizeId ? item.userPrizeId : '' }}</span>
        </div>
        <div class="info">
          <img :src="item.prizeImg" alt="" class="show-img" />
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
            <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="status" v-if="item.prizeType === 3">
            <div class="orange" v-if="!item.deliveryStatus">待发货</div>
            <div class="green" v-else>已发货</div>
            <div class="blue" v-if="!item.deliveryStatus" @click="changAddress(item)">修改地址</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 7">
            <!-- <div class="orange">待发货</div> -->
            <!--            <div class="blue" @click="showCardNum(item)">如何兑换</div>-->
          </div>
          <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
            <!-- <div class="orange">待发货</div> -->
            <div class="blue" @click="exchangePlusOrAiqiyi">立即兑换</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 12">
            <div class="orange" v-if="item.isFuLuWaitingReceive">待领取</div>
            <div class="orange" v-else>已发放</div>
            <div class="blue" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive">点击领取</div>
          </div>
          <div class="status" v-else>
            <div class="green">已发放</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
    <div  class="close" @click="close" />
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :userPrizeId="userPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  userReceiveRecordId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  // 状态 0允许报名  1 报名成功 2 取消报名 3 已发奖
  status: number;
  showOrder: boolean;
  orderList: {
    orderId: string;
    orderStatus: string;
    orderPrice: string;
  }[];
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/95004/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const addressId = ref('');
const userPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  addressId.value = item.addressId;
  userPrizeId.value = item.userPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  emits('close', true);
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userReceiveRecordId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.8rem;
  height: 8rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/231711/29/23779/16011/66962425F91e4ed6c/092356b207f0210a.png);
  background-size: 100%;
  background-repeat: no-repeat;
  .title {
    height: 1.18rem;
  }
  .content {
    height: 5rem;
    border: 0.2rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    background-color: #fff;
    margin: 0.2rem 0.2rem 0 0.2rem;
    border-radius: 0.2rem ;
    padding:0.1rem;
    .prize {
      background: #ffffff;
      margin-bottom: 0.1rem;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.16rem;
        padding-bottom: 0.16rem;
        border-bottom: 0.02rem dashed #eee;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.24rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;

        .show-img {
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #ff3333;
          }

          .time {
            color: #999999;
            font-size: 0.2rem;
            margin-top: 0.2rem;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;

          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #0083ff;
          }

          .red {
            color: #ff3333;
          }
        }
      }
      .order-list {
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        margin-top: 0.2rem;
        border-top: 0.02rem dashed #eee;
      }
      .order-info {
        display: flex;
        justify-content: space-between;
        padding-top: 0.2rem;
        font-size: 0.2rem;
        color: #999999;

        .order-id {
          flex: 1.5;
        }
        .order-status {
          flex: 1;
        }
        .order-price {
          flex: 1.1;
        }
      }
    }

    .no-data {
      text-align: center;
      line-height: 28vh;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
  .close {
    height: 1rem;
    width: 1rem;
    margin: 0 auto;
  }
}
</style>
