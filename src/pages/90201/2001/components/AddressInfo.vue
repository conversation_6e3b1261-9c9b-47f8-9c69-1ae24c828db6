<template>
  <div class="info-popup-bk" :style="furnishStyles.addressInfoBk.value">
    <div class="content" :style="furnishStyles.addressInfoTextColor.value">
      <div class="text">
        <div v-if="isPreview">
          <p><span>姓名：</span>XXX</p>
          <p><span>手机号：</span>XXXXXXXX</p>
          <p><span>收件信息：</span>xxx省xxx市xxx区xxxxxxx</p>
        </div>
        <div v-else>
          <p><span>姓名：</span>{{ info.realName }}</p>
          <p><span>手机号：</span>{{ info.mobile }}</p>
          <p><span>收件信息：</span>{{ info.province }}{{ info.city }}{{ info.county }}{{ info.address }}</p>
        </div>
      </div>
      <div class="order" v-if="isPreview">
        <div class="text-order"><span>物流信息：</span>未发货/SF0000000001</div>
        <div class="btn">复制快递单号></div>
      </div>
      <div class="order" v-else>
        <div class="text-order"><span>物流信息：</span>{{ info.deliveryStatus ? `${info.deliverName}/${info.deliverNo}` : '未发货' }}</div>
        <div class="btn copy-btn" :copy-text="info.deliverNo" v-if="info.deliveryStatus">复制快递单号></div>
      </div>
    </div>
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/263842/20/14762/985/6790bd81Fccc95f7b/3e9f98b1917d891e.png" alt="" class="close" @click="emits('close')" />
  </div>
</template>

<script lang="ts" setup>
import { computed, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { BaseInfo } from '@/types/BaseInfo';
import { showToast } from 'vant';
import Clipboard from 'clipboard';

const isPreview = inject('isPreview') as boolean;
const baseInfo = inject('baseInfo') as BaseInfo;

const btnBorer = computed(() => `0.01rem solid ${furnish.addressInfoTextColor}`);

const props = defineProps(['info']);
const emits = defineEmits(['close']);

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.info-popup-bk {
  width: 5.9rem;
  background-repeat: no-repeat;
  background-size: 100%;
  position: relative;

  .content {
    width: 5.9rem;
    height: 5.14rem;
    padding: 1.4rem 0.24rem 0 0.53rem;
    .text {
      font-size: 0.21rem;
      line-height: 0.25rem;
      margin-bottom: 0.8rem;
      p {
        margin-bottom: 0.4rem;
      }
      span {
        font-weight: bold;
        font-size: 0.25rem;
      }
    }
    .order {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text-order {
        font-size: 0.21rem;
        line-height: 0.25rem;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        span {
          font-weight: bold;
          font-size: 0.25rem;
        }
      }
      .btn {
        width: 1.4rem;
        height: 0.42rem;
        border-radius: 0.1rem;
        font-size: 0.17rem;
        text-align: center;
        line-height: 0.42rem;
        border: v-bind(btnBorer);
      }
    }
  }

  .close {
    margin: 0.34rem auto 0;
    width: 0.5rem;
  }
}
</style>
