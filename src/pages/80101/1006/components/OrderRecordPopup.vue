<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/240195/20/584/14392/65854d11F813ec5d4/60b7c199856d9386.png" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="h-[40vh] px-2 pb-2 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="mt-2 text-black text-center">下单状态为已完成时，才可以领取对应奖励。</div>
      <div v-if="orderList.length">
        <div class="grid grid-cols-3 mt-3 bg-white p-2 rounded" v-for="(order, index) in orderList" :key="index">
          <div class="flex col-span-2">
            <div>订单编号：</div>
            <div>{{ order.orderId }}</div>
          </div>
          <div class="flex">
            <div>订单状态：</div>
            <div :class="order.orderStatus === '完成' ? 'text-yellow-500' : 'text-green-500'">{{ order.orderStatus }}</div>
          </div>
          <div class="flex col-span-2">
            <div>下单时间：</div>
            <div>{{ order.orderStartTime ? dayjs(order.orderStartTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</div>
          </div>
          <div class="flex">
            <div>订单金额：</div>
            <div>¥{{ order.orderPrice }}</div>
          </div>
        </div>
      </div>
      <div v-else class="text-black text-sm h-[80%] flex justify-center items-center">暂无订单记录哦～</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

interface Order {
  orderId: string;
  orderStatus: string;
  orderPrice: string;
  orderEndTime: number;
  orderStartTime: string;
}

const orderList = ref<Order[]>([]);
const props = defineProps(['orderRestrainStatus']);
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/80101/getMyOrder');
    orderList.value = data;
    closeToast();
  } catch (error) {
    closeToast();
  }
};

!isPreview && getRecord();
</script>

<style lang="scss" scoped>
.no-data {
  text-align: center;
  padding-top: 3rem;
  font-size: 0.3rem;
  .btn {
    width: 2.4rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    color: white;
    font-size: 0.3rem;
    border-radius: 0.1rem;
    background-color: #ff9900;
    margin: 0.3rem auto;
  }
}
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/237093/18/9216/14916/65854d10Ff48448e2/200d79f0adc87e68.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 0.97rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.77rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/108458/3/36291/1280/65854d1bFa872a3fe/b1c5a7fea47344d6.png");
    background-repeat: no-repeat;
    background-size: 100%;
  }

  .content {
    width: 7rem;
    margin: 0.3rem auto 0;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .no-data {
      height: 100%;
      font-size: 0.24rem;
      color: #8c8c8c;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/102250/3/42352/11653/65016dfeF263c3d2b/250af9864bd1312f.png) no-repeat;
      background-size: 100%;
      //display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.no-more{
  margin: 0 auto;
  text-align: center;
  color: #eeeeee;
}
</style>
