<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img
        :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'"
        alt=""
        class="kv-img" />
      <div class="header-content">
                <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>

        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
            <div>{{ btn.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <vueDanmaku
      v-if="activityGiftRecords.length !== 0"
      ref="danmaku"
      v-model:danmus="activityGiftRecords"
      useSlot
      loop
      :channels="1"
      :speeds="90"
      class="danmaku">
      <template v-slot:dm="{ danmu }">
        <div class="winner">
          <span>恭喜{{ danmu.nickName }}抽中了{{ danmu.prizeName }}</span>
        </div>
      </template>
    </vueDanmaku>
    <div class="select-hover">
      <div class="wheel">
        <lz-lucky-wheel
          ref="myLucky"
          width="95vw"
          height="95vw"
          :blocks="furnishStyles.params.value.blocks"
          :prizes="furnishStyles.params.value.prizes"
          :buttons="furnishStyles.params.value.buttons"
          @start="startCallback"
          @end="endCallback"
          :defaultConfig="furnishStyles.params.value.defaultConfig" />
        <!-- <img :src="wheelImg" alt="" class="wheel-img" /> -->
      </div>
      <div class="draws-num" :style="furnishStyles.drawsNum.value">当前还有 {{ chanceNum }} 次抽奖机会</div>
      <div class="draws-num">已有 {{ joinNum }} 人参与</div>
    </div>
    <div class="sku" v-if="skuList.length > 0">
      <div class="sku-list-box" :style="furnishStyles.winnersBg.value">
        <div class="sku-list">
          <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="gotoSkuPage(item.skuId)">
            <img :src="item.skuMainPicture" alt="" />
            <div class="sku-text">{{ item.skuName }}</div>
            <div class="sku-price">￥{{ item.jdPrice }}</div>
          </div>
        </div>
      </div>
      <div class="load-more" @click="handleLoadMore">加载更多</div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup
      v-if="showAward"
      :prize="award"
      @openShowGoShop="showGoShop = true"
      @close="showAward = false"
      @saveAddress="toSaveAddress"
      @showCardNum="showCardNum"
      @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
    <OrderRecordPopup v-if="showOrderRecord" @close="showOrderRecord = false" :orderRestrainStatus="orderRestrainStatus"></OrderRecordPopup>
  </VanPopup>
  <!--抽奖记录弹窗-->
  <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
    <DrawRecordPopup v-if="showDrawRecord" @close="showDrawRecord = false"></DrawRecordPopup>
  </VanPopup>
  <!-- 活动商品弹窗-->
  <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
    <GoodsPopup v-if="showGoods" :data="orderSkuList" @close="showGoods = false" @openShowGoShop="showGoShop = true"></GoodsPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard v-if="copyCardPopup" :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
  <!-- 进店逛逛 -->
  <VanPopup teleport="body" v-model:show="showGoShop" position="bottom" z-index="10000">
    <GoShopPop v-if="showGoShop" @close="showGoShop = false"></GoShopPop>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject, onMounted } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import GoShopPop from '../components/GoShopPop.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import GoodsPopup from '../components/GoodsPopupBosiden.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import vueDanmaku from 'vue3-danmaku';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';

import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pageNum = ref(1);

const shopName = ref(baseInfo.shopName);

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

// 抽奖次数
const chanceNum = ref(0);
// 参与人数
const joinNum = ref(0);
// 订单状态
const orderRestrainStatus = ref(0);

const showMyPrize = ref(false);

const showGoods = ref(false);
const showOrderRecord = ref(false);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  skuId: number;
  jdPrice: string;
};

const showGoShop = ref(false);
const skuList = ref<Sku[]>([]);
const handler = Handler.getInstance();
onMounted(() => {
  handler.on('onGoShopOpen', () => {
    showGoShop.value = true;
  });
});

const orderSkuList = ref<Sku[]>([]);
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const showDrawRecord = ref(false);
// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    event: () => {
      showMyPrize.value = true;
    },
  },
  {
    name: '活动商品',
    event: () => {
      showGoods.value = true;
    },
  },
  {
    name: '我的订单',
    event: () => {
      showOrderRecord.value = true;
    },
  },
  {
    name: '抽奖记录',
    event: () => {
      showDrawRecord.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 不在活动范围呢刷新页面
const unStart = (partakeStartTime: string) => {
  const now = dayjs().format('YYYY-MM-DD');
  let time = 0;
  if (partakeStartTime > dayjs().format('HH:mm:ss')) {
    time = dayjs(`${now} ${partakeStartTime}`).valueOf() - dayjs().valueOf();
  } else {
    time = dayjs(`${now} ${partakeStartTime}`).add(1, 'day').valueOf() - dayjs().valueOf();
  }
  setTimeout(() => {
    window.location.reload();
  }, time);
};

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/80101/chanceNum');
    chanceNum.value = data.chanceNum;
    joinNum.value = data.joinNum;
    orderRestrainStatus.value = data.orderRestrainStatus;

    const thresholdLength = await handler.trigger('onThresholdIf');
    const thresholdData = await handler.trigger('onThresholdData');
    if (thresholdLength) {
      const threshold = thresholdData.find((item: any) => item.thresholdCode === 201);
      if (threshold) {
        unStart(data.partakeStartTime);
      }
    }
  } catch (error) {
    console.error(error);
  }
};

const myLucky = ref();
// 抽奖接口
const lotteryDraw = async () => {
  try {
    const res = await httpRequest.post('/80101/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      myLucky.value.stop(index);
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      myLucky.value.stop(index);
    }
  } catch (error) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    myLucky.value.stop(index);
    console.error(error);
  }
  getChanceNum();
};
const startCallback = async () => {
  lzReportClick('kscj');
  if (chanceNum.value <= 0) {
    showToast('您的抽奖次数已用完');
    return;
  }
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  lotteryDraw();
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/80101/getPrizes');
    prizeInfo.splice(0);
    prizeInfo.push(...data);
  } catch (error) {
    console.error(error);
  }
};

// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/80101/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    if (activityGiftRecords.length > 4) {
      nextTick(() => {
        const mySwiper = new Swiper('.swiper-container', {
          autoplay: {
            delay: 1000,
            stopOnLastSlide: false,
            disableOnInteraction: false,
          },
          direction: 'vertical',
          loop: true,
          slidesPerView: 5,
          loopedSlides: 8,
          centeredSlides: true,
        });
      });
    }
  } catch (error) {
    console.error(error);
  }
};
// 获取曝光商品
const getSkuList = async () => {
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
    type: 0,
  };
  try {
    const { data } = await httpRequest.post('/80101/getExposureSkuPage', params);
    console.log(data);
    skuList.value = data.records as any[];
  } catch (error) {
    console.error(error);
  }
};
// 加载更多
const handleLoadMore = async () => {
  pageNum.value++;
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
    type: 0,
  };
  // console.log(params);
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 2000,
    overlay: true,
  });
  try {
    const { data } = await httpRequest.post('/80101/getExposureSkuPage', params);
    console.log(data);
    if (data.records.length === 0) {
      showToast({
        message: '没有更多数据了',
        duration: 2000,
      });
      return;
    }
    skuList.value.push(...data.records);
    closeToast();
  } catch (error) {
    console.error(error);
    closeToast();
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getChanceNum(), getPrizes(), getWinners(), getSkuList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
  if (baseInfo.status === 1) {
    const time = baseInfo.startTime - dayjs().valueOf();
    setTimeout(() => {
      window.location.reload();
    }, time);
  }
};
init();
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.18rem;
    line-height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.danmaku {
  width: 6.2rem;
  height: 1rem;
  margin: 0 auto;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  height: 0.65rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/168276/30/41149/4329/65683557F493dbec4/b7608a4a44724145.png') no-repeat;
  background-size: 100%;
  width: 5.2rem;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 0.15rem;
  padding-right: 0.4rem;
  color: #333;
  font-size: 0.24rem;
  margin: 0.1rem 0;
  span {
    margin-left: 0.8rem;
    width: 5.2rem;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}

.wheel {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}

.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  position: relative;
  .sku-list-box {
    background-repeat: no-repeat;
    background-size: 100%;
    width: 6.86rem;
    height: 11.09rem;
    margin: 0 auto;
    padding-top: 0.5rem;
    .sku-list {
      width: 6.6rem;
      height: 9.8rem;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      display: flex;
      margin: 0.2rem auto 0.1rem auto;
      overflow: hidden;
      overflow-y: scroll;
      .sku-item {
        width: 3.22rem;
        margin-bottom: 0.1rem;
        background: rgb(255, 255, 255);
        border-radius: 0.2rem;
        overflow: hidden;
        img {
          display: block;
          width: 3.22rem;
          height: 3.22rem;
        }
        .sku-text {
          width: 3.4rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          font-size: 0.3rem;
          color: #262626;
          line-height: 0.4rem;
          height: 0.8rem;
          padding: 0 0.2rem;
          margin: 0.1rem 0;
          box-sizing: border-box;
        }
        .sku-price {
          font-size: 0.3rem;
          color: #ff5f00;
          padding: 0 0.2rem;
          margin-bottom: 0.15rem;
        }
      }
    }
  }
  .load-more {
    width: 3rem;
    height: 0.6rem;
    line-height: 0.6rem;
    text-align: center;
    background: #ff64e4;
    border-radius: 0.2rem;
    color: white;
    position: absolute;
    bottom: 0.24rem;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 600;
  }
}

.draws-num {
  text-align: center;
  font-size: 0.3rem;
  margin-bottom: 0.2rem;
}

.draw-btn {
  width: 4rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
