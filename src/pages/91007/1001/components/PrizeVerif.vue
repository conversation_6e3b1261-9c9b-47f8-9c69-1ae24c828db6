<template>
  <div class="bk-all">
    <div class="bk" :style="{'backgroundImage': 'url(' + furnish.dialogGuideBuyBg + ')' }"  v-if="lockType === 2">
      <div class="goBuyBtnDiv" @click="goToBuyClick"></div>
    </div>

    <div class="bk1" v-else :style="{'backgroundImage': 'url(' + furnish.dialogLockSuccBg + ')' }" >
      <div class="drawPrizeDiv" @click="sureBtn"></div>
    </div>
    <div class="closeDiv" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { PropType, inject } from 'vue';
import { furnish } from '../ts/furnishStyles';
import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  prizeData: {
    type: Object,
    default: null,
  },
  lockType: {
    type: Number,
    default: 2,
  },
  isExposure: {
    type: Number,
    default: 1,
  },
});

const emits = defineEmits(['close', 'sureBtnEmit']);

const close = () => {
  emits('close');
};
const sureBtn = () => {
  emits('sureBtnEmit', props.prizeData);
};
// 去下单
const goToBuyClick = () => {
  if (props.isExposure === 1) {
    emits('sureBtnEmit', 'goToBuy');
  } else {
    gotoShopPage(baseInfo.shopId);
  }
};
</script>

<style scoped lang="scss">
.bk-all{
  .bk {
    width: 5.8rem;
    height: 7.6rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/284929/16/16871/148412/67f6085aFeb29d3b9/3874f1c29ccd4ff9.png);
    background-repeat: no-repeat;
    background-size: 100%;
    padding-top: 2.4rem;
    .goBuyBtnDiv{
      width: 2.44rem;
      height:0.6rem;
      position: absolute;
      bottom: 1.8rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .bk1{
    width: 5.8rem;
    height: 7.6rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/277627/15/17649/42995/67f4c324Fdb91cf04/9a13ca5c76544835.png);
    background-repeat: no-repeat;
    background-size: 100%;
    padding-top: 2.4rem;
    .drawPrizeDiv{
      width: 2.44rem;
      height:0.6rem;
      position: absolute;
      bottom: 1.6rem;
      left: 50%;
      transform: translateX(-50%);
      //background-color:red;
    }
  }
  .closeDiv{
    //position: absolute;
    //bottom:0rem;
    margin-left:calc(50% - 0.56rem / 2);
    //background:red;
    width:0.56rem;
    height:0.56rem;
    z-index:10;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/281508/32/16842/902/67f4c324F294d77b6/0ade7953b8f5c31c.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}

</style>
