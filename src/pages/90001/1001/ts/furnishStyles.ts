import { computed, reactive } from 'vue';

export const taskRequestInfo = reactive([
  {
    skuId: '',
    gradeValue: 0,
    id: '',
    level: '',
    gradeValueName: '',
    position: 1,
    giftSkuList: [],
    taskRule: '',
    sendTotalCount1: 1,
    joinSkuType: 1, // 1全店商品 2指定商品
    skuList: [],
    prizeList: [], // 根据实际情况，可能需要定义奖品的类型
  },
]);
export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  oneLevelPrizeImg: '',
  twoLevelPrizeImg: '',
  threeLevelPrizeImg: '',
  fourLevelPrizeImg: '',
  fiveLevelPrizeImg: '',
  oneTitle: '',
  twoTitle: '',
  threeTitle: '',
  fourTitle: '',
  fiveTitle: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const levelPrizeBgArr = computed(() => ([
  {
    width: '5.82rem',
    height: '4.43rem',
    backgroundImage: furnish.oneLevelPrizeImg ? `url("${furnish.oneLevelPrizeImg}")` : '',
    titleText: furnish.oneTitle ? furnish.oneTitle : '',
  }, {
    width: '6.12rem',
    height: '4.44rem',
    backgroundImage: furnish.twoLevelPrizeImg ? `url("${furnish.twoLevelPrizeImg}")` : '',
    titleText: furnish.twoTitle ? furnish.twoTitle : '',
  }, {
    width: '4.53rem',
    height: '4.43rem',
    backgroundImage: furnish.threeLevelPrizeImg ? `url("${furnish.threeLevelPrizeImg}")` : '',
    titleText: furnish.threeTitle ? furnish.threeTitle : '',
  }, {
    width: '4.60rem',
    height: '4.27rem',
    backgroundImage: furnish.fourLevelPrizeImg ? `url("${furnish.fourLevelPrizeImg}")` : '',
    titleText: furnish.fourTitle ? furnish.fourTitle : '',
  }, {
    width: '5.43rem',
    height: '4.38rem',
    backgroundImage: furnish.fiveLevelPrizeImg ? `url("${furnish.fiveLevelPrizeImg}")` : '',
    titleText: furnish.fiveTitle ? furnish.fiveTitle : '',
  },
]));
const pageBg1 = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.oneLevelPrizeImg ? `url("${furnish.oneLevelPrizeImg}")` : '',
}));

export default {
  pageBg,
  levelPrizeBgArr,
  pageBg1,
};
