<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" @click="onSelected(1)" v-if="isLoadingFinish">
    <div class="levelDivAll">
      <div class="levelDiv">
        <div class="levelItemDiv" v-for="(item, index) in levelArr" :key="index">
          <div class="levelItemDiv2" :class="{ levelItemDiv1: currentLevelIndex === index + 1 }" @click.stop="selectLevel(item, index)">{{ item.name }}</div>
        </div>
      </div>
      <div class="levelPrizeDivALL">
        <div class="levelPrizeBg" :style="furnishStyles.levelPrizeBgArr.value[currentLevelIndex - 1].backgroundImage ? furnishStyles.levelPrizeBgArr.value[currentLevelIndex - 1] : levelArr[currentLevelIndex - 1].styleData"></div>
        <div class="levelPrizeTitle">{{ furnishStyles.levelPrizeBgArr.value[currentLevelIndex - 1].titleText }}</div>
      </div>
      <div class="levelRuleDiv">
        <div class="contentDiv" v-html="levelArr[currentLevelIndex - 1].taskRule"></div>
      </div>
      <div class="bottomBtnDiv" @click.stop="toast"></div>
    </div>
  </div>
  <div v-if="!isCreateImg">
    <VanPopup teleport="body" v-model:show="showBirthDayPop" position="center">
      <BirthDay :itemData="null" @close="closeBirthDayPop"></BirthDay>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom" :close-on-click-overlay="closeOnClickOverlay">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" @close="showSaveAddress = false" user-prize-id="" :itemData="null"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish, taskRequestInfo } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';
import SaveAddress from '../components/SaveAddress.vue';
import BirthDay from '../components/birthdayPop.vue';

const showBirthDayPop = ref(false);
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const levelArr = reactive([
  {
    name: '银卡',
    value: 1,
    prizeType: 11,
    styleData: {
      width: '5.82rem',
      height: '4.43rem',
      backgroundImage: 'url(//img10.360buyimg.com/imgzone/jfs/t1/237747/22/9632/34331/65960d8dF186d23de/91cf816ff57753d9.png)',
    },
    taskRule: '',
  },
  {
    name: '金卡',
    value: 2,
    prizeType: 11,
    styleData: {
      width: '6.12rem',
      height: '4.44rem',
      backgroundImage: 'url(//img10.360buyimg.com/imgzone/jfs/t1/247858/5/2072/55653/6597b772F0484b965/17a0dbfa74cdd949.png)',
    },
    taskRule: '',
  },
  {
    name: '白金卡',
    value: 3,
    prizeType: 11,
    styleData: {
      width: '4.53rem',
      height: '4.43rem',
      backgroundImage: 'url(//img10.360buyimg.com/imgzone/jfs/t1/246348/16/1603/27869/65960d8fF7e27413a/67d7ed85cf5180aa.png)',
    },
    taskRule: '',
  },
  {
    name: '铂钻卡',
    value: 4,
    prizeType: 3,
    styleData: {
      width: '4.60rem',
      height: '4.27rem',
      backgroundImage: 'url(//img10.360buyimg.com/imgzone/jfs/t1/231099/37/11330/35395/65960d8fFc2d48e94/cd210d61ea8e3725.png)',
    },
    taskRule: '',
  },
  {
    name: '黑钻卡',
    value: 5,
    prizeType: 3,
    styleData: {
      width: '5.43rem',
      height: '4.38rem',
      backgroundImage: 'url(//img10.360buyimg.com/imgzone/jfs/t1/237123/24/10350/38704/65960d8fFcf53462c/55528bbcc4eef174.png)',
    },
    taskRule: '',
  },
]);
const isLoadingFinish = ref(false);
const closeOnClickOverlay = ref(false);
// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
// 当前选择对的会员等级，默认为等级1
const currentLevelIndex = ref(1);
// 选择等级
const selectLevel = (itemData: any, index: any) => {
  console.log(itemData, index);
  currentLevelIndex.value = Number(itemData.value);
};
// 手机生日信息
const birthDay = () => {
  console.log('手机生日信息');
  showBirthDayPop.value = true;
};
// 关闭手机生日信息弹窗
const closeBirthDayPop = (data: boolean) => {
  console.log(data, '关闭手机生日信息弹窗');
  showBirthDayPop.value = false;
  if (data) {
    // 手机生日信息成功 关闭弹窗 铂钻卡和黑金卡显示填写地址弹窗
    showSaveAddress.value = true;
  }
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const toast = () => {
  showToast('活动预览，仅供查看');
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  console.log(res.data, '装修实时数据修改');
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    if (data.gradeLabel && data.gradeLabel.length > 0) {
      const gradeLabelArr = data.gradeLabel;
      gradeLabelArr.forEach((itemDeco: any, index1: number) => {
        levelArr[index1].name = itemDeco.gradeName;
        levelArr[index1].value = itemDeco.gradeLevel;
        levelArr[index1].prizeType = itemDeco.prizeType;
      });
    }
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    if (data.taskRequestList.length) {
      taskRequestInfo.splice(0);
      taskRequestInfo.push(...data.taskRequestList);
    }
    levelArr.forEach((item, index) => {
      taskRequestInfo.forEach((item1: any, index1) => {
        if (item.value === item1.level) {
          item.taskRule = item1.taskRule;
        }
      });
    });
    console.log(levelArr, 'levelArr=========');
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  }
};

// 设置活动后的预览
onMounted(() => {
  console.log('onMounted装修实时数据修改');
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    taskRequestInfo.splice(0);
    taskRequestInfo.push(...activityData.taskRequestList);
    levelArr.forEach((item, index) => {
      taskRequestInfo.forEach((item1: any, index1) => {
        if (item.value.toString() === item1.level) {
          item.taskRule = item1.taskRule;
        }
      });
    });
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  .levelDivAll {
    position: relative;
    padding-top: 2.96rem;
    .levelDiv {
      width: 6.07rem;
      height: 0.43rem;
      margin-left: calc(50% - 6.07rem / 2);
      display: flex;
      justify-content: center;
      align-items: center;
      .levelItemDiv {
        flex: 1;
        display: flex;
        justify-content: center;
        .levelItemDiv2 {
          color: #726150;
          font-size: 0.24rem;
        }
        .levelItemDiv1 {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/244938/13/2031/1636/65961529F8bab7cea/8c8b13e7d9892a48.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 0.9rem;
          height: 0.37rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #f8f4f1;
          font-size: 0.24rem;
        }
      }
    }
  }
  .levelPrizeDivALL {
    margin-top: 0.46rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .levelPrizeBg {
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .levelPrizeTitle {
      width: 80%;
      font-size: 0.18rem;
      min-height: 0.8rem;
      display: flex;
      justify-content: center;
      align-items: center;
      word-break: break-all;
    }
  }
  .levelRuleDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/240815/4/2279/7956/65960d8fFb7fb05fa/01a02c35c6ec80a3.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.51rem;
    height: 3.06rem;
    margin-left: calc(50% - 6.51rem / 2);
    padding: 0.5rem 0.2rem;
    color: #876634;
    font-size: 0.24rem;
    .contentDiv {
      height: 2.06rem;
      overflow-y: scroll;
      white-space: pre-wrap;
    }
  }
  .bottomBtnDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/228241/39/10276/4755/65960d8eF25dae75f/46be013f0995c51a.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 2.4rem;
    height: 0.63rem;
    margin-left: calc(50% - 2.4rem / 2);
    margin-top: 0.24rem;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
