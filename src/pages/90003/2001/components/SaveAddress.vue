<template>
  <div class="address-bk">
    <div class="list">
      <div class="row name">
        <input v-model="form.realName" type="text" placeholder="请输入" />
      </div>
      <div class="row phone">
        <input v-model="form.mobile" type="text" placeholder="请输入" maxlength="11" oninput="value=value.replace(/[^\d]/g,'')" />
      </div>
      <div class="row province">
        <input :value="addressCode" type="text" placeholder="请选择" readonly @click="showAddress" />
      </div>
      <div class="row address">
        <input v-model="form.address" type="text" placeholder="请输入" />
      </div>
    </div>
    <div class="bottom-btn">
      <img src="../assets/submit.png" alt="" @click="checkForm" />
      <img src="../assets/goShop2.png" alt="" @click="gotoShopPage(baseInfo.shopId)" />
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, inject, reactive, ref } from 'vue';
import { areaList } from '@vant/area-data';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';

const baseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const addressSelects = ref(false);

const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const showAddress = () => {
  addressSelects.value = true;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90003/saveAddress', {
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    showToast('保存成功');
    emits('close');
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};

const getAddress = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90003/getAddress');
    form.realName = data.realName;
    form.mobile = data.mobile;
    form.province = data.province;
    form.city = data.city;
    form.county = data.county;
    form.address = data.address;
    closeToast();
  } catch (error: any) {
    showToast(error.message);
  }
};
getAddress();
</script>

<style scoped lang="scss">
.address-bk {
  width: 6.9rem;
  background: url('../assets/address-bk.png') no-repeat;
  background-size: 100%;
  height: 6rem;
  padding-top: 1.7rem;
  margin-bottom: 0.4rem;
}
.list {
  padding: 0 0.4rem;
  margin-bottom: 0.3rem;
  .row {
    background-size: 100%;
    background-repeat: no-repeat;
    height: 0.58rem;
    margin-bottom: 0.15rem;
    input {
      width: 5.55rem;
      height: 100%;
      background: none;
      border: none;
      font-size: 0.3rem;
      color: #fde2a5;
      padding-left: 1.8rem;
      padding-right: 0.3rem;
      text-align: center;
      &::placeholder {
        color: #fde2a5;
      }
    }
  }
  .name {
    background-image: url('../assets/name.png');
  }
  .phone {
    background-image: url('../assets/phone.png');
  }
  .province {
    background-image: url('../assets/province.png');
  }
  .address {
    background-image: url('../assets/address.png');
  }
}
.bottom-btn {
  padding: 0 0.4rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  img {
    width: 2.8rem;
  }
}
</style>
