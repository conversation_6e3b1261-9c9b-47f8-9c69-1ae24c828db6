<template>
  <div class="bk">
    <div>
      <img src="./assets/ruleBtn.png" alt="" class="rule-btn" @click="rulePopup = true" />
    </div>
    <div class="act-name" :style="{ fontSize: baseInfo.activityName.length <= 7 ? '1rem' : '0.7rem' }">{{ baseInfo.activityName }}</div>
    <div class="nick-name">尊敬的{{ userInfo.nickname }}</div>
    <CurrentStatus>
      <div class="current-text" v-if="!actStatus">
        <div>活动尚未开始</div>
        <div>进店看看其他活动吧</div>
      </div>
      <div class="current-text" v-else-if="actStatus === 1 && isChecking">
        <div>验证中……</div>
      </div>
      <div v-else-if="actStatus === 1 && !isChecking">
        <div class="current-text" v-if="isVerificated">
          <div>恭喜您符合活动条件</div>
          <div>可领取<span>以下好礼</span></div>
        </div>
        <div class="current-text" v-else>
          <div>抱歉您不符合活动条件</div>
          <div>进店看看其他活动吧</div>
        </div>
      </div>
      <div class="current-text" v-else-if="actStatus === 2">
        <div>恭喜您符合活动条件</div>
        <div>可领取<span>以下好礼</span></div>
      </div>
      <div class="current-text" v-else-if="actStatus === 3">
        <div>活动已结束</div>
        <div>进店看看其他活动吧</div>
      </div>
    </CurrentStatus>
    <div class="block">
      <div v-if="isVerificated">
        <div class="allow-count">可在以下{{ prizeList.length }}个奖品中选择{{ allowCount }}个，选择后不可更改</div>
        <div v-for="item in prizeList" :key="item.prizeKey">
          <!-- <SaveAddress v-if="item.prizeType === 3"></SaveAddress> -->
          <Coupon :prizeInfo="item" @reload="getActivityPrize" v-if="item.prizeType === 1"></Coupon>
          <OtherType :prizeInfo="item" @reload="getActivityPrize" v-else></OtherType>
        </div>
      </div>
      <img v-if="!isVerificated || decoData.prizeType !== 3" src="./assets/goShop.png" alt="" class="go-shop" @click="gotoShopPage(baseInfo.shopId)" />
      <SkuList></SkuList>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="rulePopup">
    <Rule></Rule>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, ref } from 'vue';
import CurrentStatus from './components/CurrentStatus.vue';
import Coupon from './components/Coupon.vue';
import OtherType from './components/OtherType.vue';
import SkuList from './components/SkuList.vue';
import Rule from './components/Rule.vue';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
import { UserInfo } from '@/utils/products/types/UserInfo';
import { httpRequest } from '@/utils/service';
import { showToast } from 'vant';

const baseInfo = inject('baseInfo') as BaseInfo;
const userInfo = inject('userInfo') as UserInfo;
const actStatus = inject('actStatus') as number;
const decoData = inject('decoData') as any;
const pathParams = inject('pathParams') as any;

// 正在验证中
const isChecking = ref(true);
// 验资是否通过
const isVerificated = ref(false);

// 允许选择奖品数
const allowCount = ref(0);
// 奖品列表
const prizeList = ref<any>([]);

const rulePopup = ref(false);

const verificate = async () => {
  try {
    // utf-8编码转换
    const reviewer = decodeURIComponent(pathParams.customerServiceName);
    const { data } = await httpRequest.post('/90003/verificate', {
      reviewer,
      reviewerId: pathParams.customerServiceId,
    });
    isVerificated.value = data;
    isChecking.value = false;
  } catch (error: any) {
    isChecking.value = false;
    console.error(error);
    showToast(error.message);
  }
};

const getActivityPrize = async () => {
  try {
    const { data } = await httpRequest.post('/90003/getActivityPrize');
    allowCount.value = data.allowCount ?? 1;
    prizeList.value = data.prizeInfo;
  } catch (error: any) {
    console.error(error);
  }
};

if (actStatus === 1 || actStatus === 2) {
  verificate();
  getActivityPrize();
}
</script>

<style>
@font-face {
  font-family: 'FZLTZHK';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTZHK/FZLTZHK--GBK1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTZHK/FZLTZHK--GBK1-0.woff') format('woff');
}
</style>

<style scoped lang="scss">
.bk {
  background-color: #043784;
  background-image: url('./assets/kv.png');
  background-repeat: no-repeat;
  background-size: 100%;
  min-height: 100vh;
  padding-top: 1.28rem;
  position: relative;
}
.rule-btn {
  position: absolute;
  top: 0.55rem;
  right: 0;
  width: 1.2rem;
}
.act-name {
  text-align: center;
  font-size: 1rem;
  font-family: 'FZLTZHK';
  font-weight: bold;
  line-height: 1rem;
  // 文字颜色渐变
  background: linear-gradient(180deg, #cba951 0%, #e5cb8e 25%, #ffeccb 50%, #ffe895 80%);
  background-size: 100% 1rem;
  background-repeat: repeat-y;
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 0.22rem;
}
.nick-name {
  text-align: center;
  color: #fff;
  font-size: 0.318rem;
  line-height: 0.318rem;
}
.current-text {
  font-size: 0.41rem;
  line-height: 0.5rem;
  color: #003984;
  text-align: center;
  font-family: 'FZLTZHK';
  // 文字阴影
  text-shadow: 0.01rem 0.02rem 0 #fff;
  span {
    color: #cd0202;
    font-size: 0.48rem;
  }
}
.block {
  padding: 1.3rem 0.3rem 0.3rem;
  .allow-count {
    font-size: 0.26rem;
    line-height: 0.4rem;
    color: #fff;
    text-align: center;
    margin-bottom: 0.3rem;
  }
}
.go-shop {
  width: 100%;
}
</style>
