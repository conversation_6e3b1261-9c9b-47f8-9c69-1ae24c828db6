<template>
  <div class="bg" :style="furnishStyles.actBgColor.value">
    <!-- 奖品主图区域 -->
    <div class="kv-box">
      <img alt="" :src="furnishStyles.actBgURL.value.backgroundImage" :style="{ width: '7.5rem', height: furnishStyles.actBgURL.value.backgroundImage ? '' : '2.4rem' }" />
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
        <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
      </div>
      <!-- <div class="user-points" :class="{ itemGap: userPoints.length <= 3 }">
        <div class="item" v-for="item in userPoints" :key="item" :style="pointsStyle(userPoints.length)">{{ item }}</div>
      </div> -->
    </div>
    <!-- 兑换产品信息区域 -->
    <div class="content" :style="furnishStyles.prizeInfoBg.value">
      <div class="block">
        <div class="goods-info">
          <img v-if="furnish.showPrizeImg" :src="activityInfo.prizeImg" alt="" class="goods-img" />
          <div class="info" :style="furnishStyles.prizeNameTextColor.value">
            <div class="name">{{ activityInfo.rightsName }}</div>
            <div class="points">
              消耗<span :style="furnishStyles.integralTextColor.value">{{ activityInfo.exchangePointNum }}</span
              >积分
            </div>
            <div class="points">
              当前剩余<span :style="furnishStyles.integralTextColor.value">{{ userPoints }}</span
              >积分
            </div>
          </div>
        </div>
        <div>
<!--          <div class="goods-num" v-if="exchangeLimit === 1">-->
<!--            活动期内限剩余兑换次数：<span :style="furnishStyles.exchangeNumTextColor.value">{{ activityInfo.rightsNum }}次</span>-->
<!--          </div>-->
<!--          <div class="goods-num" v-if="exchangeLimit === 2">-->
<!--            当日剩余兑换次数：<span :style="furnishStyles.exchangeNumTextColor.value">{{ activityInfo.rightsNum }}次</span>-->
<!--          </div>-->
          <div class="goods-num">
            奖品总剩余：<span :style="furnishStyles.exchangeNumTextColor.value">{{ activityInfo.actRightsNum ? activityInfo.actRightsNum : 0 }}份</span>
          </div>
        </div>
        <div class="exchange-limit">
          <span class="red-bk">{{ exchangeLimitTips }}</span
          ><span v-if="sameTermOnce">且同期内所有活动仅限参加{{ sameTermTimes }}个</span>
        </div>
        <div class="exchange-address-big-box" v-show="activityInfo.rightsType === 3" v-threshold-click="goChooseAddress">
          <div class="choose-address-title">送至</div>
          <div class="choose-address-btn">
            <span>{{ addressInfo ? addressInfo : activityInfo.value?.defAddressDetail ? activityInfo.value.defAddressDetail : '点击新建收货地址' }}</span>
          </div>
          <van-icon name="arrow" color="#a7a7a7" />
        </div>
      </div>
    </div>
    <!-- 指定商品区域 -->
    <div class="sku" v-if="orderSkuisExposure !== 0">
      <div :style="furnishStyles.ruleTitleBox.value">指定商品:</div>
      <div class="sku-list" >
        <div class="sku-item" v-for="(item,index) in orderSkuList" :key="index">
          <img :src="item.skuMainPicture" alt="">
          <div class="sku-text">{{item.skuName}}</div>
          <div class="sku-btns" @click="gotoSkuPage(item.skuId)">
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>
    <!-- 兑换规则区域 -->
    <div class="rules-box">
      <div class="title-box title-box-rule" :style="furnishStyles.ruleTitleBox.value">兑换规则:</div>
      <div class="rules-line" :style="furnishStyles.ruleContentBox.value">
        {{ activityInfo.rules }}
      </div>
    </div>
    <!-- 兑奖按钮区域 -->
    <div class="exchange-btn-box">
      <img class="exchange-record-btn" :src="furnish.recordImg" alt="" @click="goExchangeRecord" />
      <div class="exchange-btn" :class="{ gray: activityInfo.status !== 0 }" :style="furnishStyles.exchangeImg.value" v-threshold-click="clickExchange">
        <span v-if="activityInfo.status === 0">立即兑换</span>
        <span v-if="activityInfo.status === 1">已兑换</span>
        <span v-if="activityInfo.status === 2">不符合活动参与条件</span>
        <span v-if="activityInfo.status === 3">积分不足</span>
        <span v-if="activityInfo.status === 4">已兑完</span>
      </div>
    </div>
  </div>
  <div>
    <!-- 确认兑换弹窗 -->
    <VanPopup teleport="body" v-model:show="showExchangeConfirm" :close-on-click-overlay="false" position="bottom">
      <ExchangeConfirm :exchangePoint="activityInfo.exchangePointNum" :addressId="addressId" @close="confirmClose" :activityInfo="activityInfo" />
    </VanPopup>
    <!-- 兑换提示弹窗 -->
    <VanPopup teleport="body" v-model:show="showExchangeTips" :close-on-click-overlay="false">
      <ExchangeTips :exchangeTips="exchangeTips" :btnName="btnName" @close="tipsClose" @confirm="tipsConfirm" />
    </VanPopup>
    <!-- 兑换记录弹窗 -->
    <VanPopup teleport="body" v-model:show="showExchangeRecord">
      <ExchangeRecord />
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { ref, inject, watch } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import ExchangeConfirm from '../components/ExchangeConfirm.vue';
import ExchangeTips from '../components/ExchangeTips.vue';
import ExchangeRecord from './ExchangeRecord.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import dayjs from 'dayjs';
import useThresholdLength from '@/hooks/useThresholdLength';
import { gotoSkuPage } from '@/utils/platforms/jump';

interface ActivityInfo {
  rightsType?: number;
  defAddressDetail?: string;
  userPoint?: number;
  status?: number;
  detail?: string;
  sameTermOnce?: boolean;
  exchangeLimit?: number;
  sameTermTimes?: number;
  exchangeNum?: number;
  exchangeCycleStartTime?: string;
  rightsName?: string;
  exchangePointNum?: number;
  prizeImg?: string;
  rules?: string;
  rightsNum?: number;
  defAddressId?: number | string;
  orderSkuisExposure?: number;
}

const pointsStyle = (strLength: number) => {
  if (strLength <= 4) {
    return {
      width: '0.7rem',
      fontSize: '0.47rem',
    };
  }

  return {
    width: `${0.7 - 0.0967 * (strLength - 4)}rem`,
    fontSize: `${0.79 - 0.12 * (strLength - 4)}rem`,
  };
};

const router = useRouter();
const route = useRoute();
const addressInfo = ref(route.query.address ? route.query.address : '');
const addressId = ref(route.query.id ? route.query.id : ''); // 地址id
const goExchangeRecord = () => {
  router.push('/record');
};
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;
const activityInfo = ref<ActivityInfo>({}); // 活动相关数据
const exchangeLimit = ref(1); // 兑换限制 0不限制 1限制次数 2限制每日次数
const shopName = ref(baseInfo.shopName);
const userPoints = ref('0');
const showExchangeConfirm = ref(false);
const showExchangeRecord = ref(false);
const showExchangeTips = ref(false);
const exchangeTips = ref('');
const btnName = ref('知道了');
const exchangeLimitTips = ref('');
const sameTermTimes = ref(1);
const sameTermOnce = ref(false); // 同期内所有奖品是否限兑换1个

// 活动商品列表
type Sku = {
  skuId: string,
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const orderSkuList = ref<Sku[]>([]);

const myLucky = ref();
const btnDisabled = useThresholdLength();
const goChooseAddress = () => {
  router.push({
    path: '/address/list',
    query: { id: addressId.value ? addressId.value : activityInfo.value.defAddressId },
  });
};
// 获取活动信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/39002/getActivityInfo');
    userPoints.value = String(data.userPoint);
    activityInfo.value = data;
    // 用户状态  0:可以领取；1：已经达到领取份数上限；2：不可领取
    if (data.status === 1 || data.status === 2 || data.status === 3 || data.status === 4) {
      exchangeTips.value = data.detail;
      if (data.detail.includes('未到今日兑换周期')) {
        const now = dayjs().format('YYYY-MM-DD');
        // 时间差值
        const diff = dayjs(`${now} ${data.exchangeCycleStartTime}`).diff(dayjs(), 'ms');
        setTimeout(() => {
          window.location.reload();
        }, diff);
      }
    }
    sameTermOnce.value = data.sameTermOnce;
    exchangeLimit.value = data.exchangeLimit;
    sameTermTimes.value = data.sameTermTimes;
    if (data.exchangeLimit === 0) {
      exchangeLimitTips.value = '活动期内不限制';
    }
    if (data.exchangeLimit === 1) {
      exchangeLimitTips.value = `活动期限内可兑换${data.exchangeNum}次`;
    }
    if (data.exchangeLimit === 2) {
      exchangeLimitTips.value = `活动期内每日可兑换${data.exchangeNum}次`;
    }
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
// 获取指定商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/39002/getExposureSkuPage', {
      type: 1,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    orderSkuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error) {
    console.error(error);
  }
};

const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};

const clickExchange = () => {
  if (!activityInfo.value.rightsNum) return;
  if (activityInfo.value.status === 0) {
    addressId.value = addressId.value ? addressId.value : activityInfo.value.defAddressId;
    showExchangeConfirm.value = true;
  } else {
    showExchangeTips.value = true;
  }
};

const tipsConfirm = () => {
  // 用户状态  0:可以领取；1：已经达到领取份数上限；2：不可领取 3:积分不足 4:已兑完
  if (activityInfo.value.status === 1 || activityInfo.value.status === 2 || activityInfo.value.status === 3 || activityInfo.value.status === 4) {
    showExchangeTips.value = false;
  }
};
const tipsClose = () => {
  showExchangeTips.value = false;
};
const confirmClose = () => {
  showExchangeConfirm.value = false;
};
watch(
  () => showExchangeConfirm,
  (newShowExchangeConfirm, oldShowExchangeConfirm) => {
    if (newShowExchangeConfirm.value === false) {
      getActivityInfo();
    }
  },
  { deep: true },
);
watch(
  () => showExchangeTips,
  (newShowExchangeTips, oldShowExchangeTips) => {
    if (newShowExchangeTips.value === false) {
      getActivityInfo();
    }
  },
  { deep: true },
);
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getActivityInfo();
    await getSkuList();
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
img[src=''],
img:not([src]) {
  opacity: 0;
}
.bg {
  padding-bottom: 1rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}
.kv-box {
  width: 7.5rem;
  position: relative;
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .user-points {
    position: absolute;
    top: 4.85rem;
    left: 0;
    right: 0;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 0 1.9rem;
    .item {
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/154332/24/29826/2044/66c40543Ff63f56ca/e484d216d4f568ec.png') no-repeat;
      background-size: 100%;
      background-position-y: center;
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 0.71rem;
      height: 1rem;
      color: #f7e7d1;
      width: 0.7rem;
    }
  }
  .itemGap {
    justify-content: center;
    .item {
      margin: 0 0.1rem;
    }
  }
}
.content {
  width: 6.9rem;
  padding: 1rem 0.22rem 0.35rem;
  margin: 0 auto 0;
  background-repeat: no-repeat;
  background-size: 100% 1rem, 100% 0.5rem, 100% calc(100% - 1.5rem);
  background-position-y: top, bottom, 1rem;
  .block {
    position: relative;
    width: 100%;
    background-color: #ffffff;
    border-radius: 0.2rem;
    padding: 0.7rem 0.33rem 0.2rem;
    overflow: hidden;
    .goods-info {
      display: flex;
      align-items: center;
      .goods-img {
        width: 1.98rem;
        height: 1.98rem;
        object-fit: cover;
        border-radius: 50%;
      }
      .info {
        padding-left: 0.25rem;
        .name {
          font-size: 0.36rem;
        }
        .points {
          font-size: 0.3rem;
        }
      }
    }
    .goods-num {
      text-align: right;
      font-size: 0.24rem;
      span {
        font-size: 0.3rem;
      }
    }
    .exchange-address-big-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.2rem;
      height: 0.6rem;
      border-top: solid 0.01rem #dcdcdc;
      .choose-address-title {
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #333333;
      }
      .choose-address-btn {
        flex: 1;
        overflow: hidden;
        text-align: right;
        text-overflow: ellipsis;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #a7a7a7;
        padding-left: 0.25rem;
        white-space: nowrap;
      }
    }
    .exchange-limit {
      position: absolute;
      top: 0;
      left: 0;
      border-bottom-right-radius: 0.2rem;
      height: 0.45rem;
      line-height: 0.45rem;
      background-color: #48341f;
      color: #fff;
      font-size: 0.2rem;
      span {
        padding: 0 0.1rem;
      }
      .red-bk {
        display: inline-block;
        height: 0.45rem;
        line-height: 0.45rem;
        border-bottom-right-radius: 0.2rem;
        background-color: #d73b35;
      }
    }
  }
}
.sku{
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.5rem 0.3rem 0.2rem;
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    width: 6.9rem;
    margin: 0.2rem auto 0;
    place-content: flex-start space-between;
    padding: 0.2rem;
    background: rgb(254, 231, 201);
    border-radius: 0.4rem 0 0.05rem 0.05rem;
    max-height: 11rem;
    position: relative;
    overflow-y: scroll;
  }
  .sku-item{
    width: 3.15rem;
    margin-bottom: 0.1rem;
    background: rgb(255, 255, 255);
    border-radius: 0.2rem;
    overflow: hidden;
    img{
      display: block;
      width: 3.4rem;
      height: 3.4rem;
    }
    .sku-text{
      width: 3.2rem;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-height: 0.4rem;
      overflow: hidden;
      font-size: 0.27rem;
      color: #262626;
      height: 0.8rem;
      padding: 0 0.2rem;
      margin: 0.2rem 0 0.2rem 0;
      box-sizing: border-box;
    }
    .sku-btns{
      width: 2.56rem;
      height: 0.6rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.14rem auto;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/32236/20/17598/3026/6307406bE3b87a669/355ec23696288825.png);
    }
  }
}
.rules-box {
  margin-bottom: 0.2rem;
  padding: 0.3rem;
  width: 7.5rem;
  min-height: 2.4rem;
  .rules-line {
    margin-top: 0.3rem;
    font-family: PingFang-SC-Medium;
    font-size: 0.24rem;
    color: #666666;
    white-space: pre-wrap;
  }
}
.exchange-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  width: 100vw;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  font-family: PingFang-SC-Medium;
  font-size: 0.3rem;
  cursor: pointer;
  .exchange-btn {
    width: 5.1rem;
    height: 1rem;
    background-size: 100% 100%;
    font-size: 0.52rem;
    font-weight: bold;
    font-style: italic;
  }
  .exchange-record-btn {
    width: 2.4rem;
    height: 1rem;
  }
}
.gray {
  filter: grayscale(1);
}
.more-btn-all {
  width:6.9rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
