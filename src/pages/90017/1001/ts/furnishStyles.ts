import { computed, reactive } from 'vue';
import { prizePlateArray } from '@/utils/prizePlateArray';

export const prizeInfo = reactive([
  {
    // id: '',
    // index: 1,
    // prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    // prizeName: 'XX积分',
    // shareNum: 0,
    // sendTotalCount: 0,
    // prizeType: 4,
    // img: '',
    // num: 0,
    // peopleNum: 0,
    // status: 0,
    prizeName: 'XX积分',
    prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    prizeType: 4,
    sendTotalCount: 0,
    type: 0,
    peopleNum: 0,
    rank: 0,
    img: '',
    num: 0,
    status: 1,
    userImg: '',
  },
]);

export const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};
export const furnish = reactive({
  actBg: '',
  actBgColor: '#ffffff',
  shopNameColor: '#ffffff',
  btnColor: '#010a83',
  btnBg: '',
  btnBorderColor: '',
  btnImgBg: '',
  step1Bg: '',
  toInviteBtn: '',
  receiveInvitedPrizeBtn: '',
  rankPrizeImg: '',
  receiveRankPrizeBtn: '',
  rankListBg: '',
  invitedFriendsListBg: '',
  step2Bg: '',
  getFirstBuyPrizeBtn: '',
  getInvitedPrizeBtn: '',
  myFriendsBtn: '',
  myOrderBtn: '',
  moreActImg: '',
  bannerList: [
    {
      bg: '',
      hotZoneList: [
      ],
    },
  ],
  disableShopName: 0,
  cmdImg: '',
  h5Img: '',
  mpImg: '',
});

const actBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));
const kvBg = computed(() => ({
  backgroundImage: furnish.actBg ? `url("${furnish.actBg}")` : '',
}));
const btnImgBg = computed(() => ({
  backgroundImage: furnish.btnImgBg ? `url("${furnish.btnImgBg}")` : '',
  color: furnish.btnColor ?? '',
}));
const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));
const step1Bg = computed(() => ({
  backgroundImage: furnish.step1Bg ? `url("${furnish.step1Bg}")` : '',
}));
const toInviteBtn = computed(() => ({ backgroundImage: furnish.toInviteBtn ? `url("${furnish.toInviteBtn}")` : '' }));
const receiveInvitedPrizeBtn = computed(() => ({ backgroundImage: furnish.receiveInvitedPrizeBtn ? `url("${furnish.receiveInvitedPrizeBtn}")` : '' }));
const rankPrizeImg = computed(() => ({ backgroundImage: furnish.rankPrizeImg ? `url("${furnish.rankPrizeImg}")` : '' }));
const receiveRankPrizeBtn = computed(() => ({ backgroundImage: furnish.receiveRankPrizeBtn ? `url("${furnish.receiveRankPrizeBtn}")` : '' }));
const rankListBg = computed(() => ({ backgroundImage: furnish.rankListBg ? `url("${furnish.rankListBg}")` : '' }));
const invitedFriendsListBg = computed(() => ({ backgroundImage: furnish.invitedFriendsListBg ? `url("${furnish.invitedFriendsListBg}")` : '' }));
const step2Bg = computed(() => ({ backgroundImage: furnish.step2Bg ? `url("${furnish.step2Bg}")` : '' }));
const getFirstBuyPrizeBtn = computed(() => ({ backgroundImage: furnish.getFirstBuyPrizeBtn ? `url("${furnish.getFirstBuyPrizeBtn}")` : '' }));
const getInvitedPrizeBtn = computed(() => ({ backgroundImage: furnish.getInvitedPrizeBtn ? `url("${furnish.getInvitedPrizeBtn}")` : '' }));
const myFriendsBtn = computed(() => ({ backgroundImage: furnish.myFriendsBtn ? `url("${furnish.myFriendsBtn}")` : '' }));
const myOrderBtn = computed(() => ({ backgroundImage: furnish.myOrderBtn ? `url("${furnish.myOrderBtn}")` : '' }));
const moreActImg = computed(() => ({ backgroundImage: furnish.moreActImg ? `url("${furnish.moreActImg}")` : '' }));
const bannerList = computed(() => furnish.bannerList);
const disableShopName = computed(() => furnish.disableShopName);

export default {
  actBg,
  kvBg,
  shopNameColor,
  btnImgBg,
  step1Bg,
  toInviteBtn,
  receiveInvitedPrizeBtn,
  rankPrizeImg,
  receiveRankPrizeBtn,
  rankListBg,
  invitedFriendsListBg,
  step2Bg,
  getFirstBuyPrizeBtn,
  getInvitedPrizeBtn,
  myFriendsBtn,
  myOrderBtn,
  moreActImg,
  bannerList,
  disableShopName,
};
