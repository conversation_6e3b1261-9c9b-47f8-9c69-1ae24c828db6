<template>
  <div class="wheel">
    <lz-lucky-wheel ref="myLucky" width="95vw" height="95vw" :blocks="furnishStyles.params.value.blocks"
      :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback"
      @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
  </div>
</template>
<script lang="ts" setup>

import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import furnishStyles from '../ts/furnishStyles';
import { defineEmits } from 'vue';

const emit = defineEmits(['startCallback', 'endCallback']);
const startCallback = async () => {
  emit('startCallback');
};
const endCallback = () => {
  emit('endCallback');
};
</script>
<style scoped lang="scss">
.wheel {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}
</style>
