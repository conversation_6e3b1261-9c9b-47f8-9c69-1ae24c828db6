<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <p class="prize-name">恭喜您获得<br />{{ prize.prizeName }}</p>
    <img :src="prize.showImg" alt="" class="prize-img" />
    <!-- <div class="tip">蓄力好福气 再来一次</div> -->
    <div class="btn-list">
      <div class="btn btn-left" @click="shareAct">立即分享</div>
      <div class="btn btn-right" v-if="prize.prizeType === 3" @click="saveAddress">填写地址</div>
      <div class="btn btn-right" v-else-if="prize.prizeType === 5" @click="gotoSkuPage(prize.result)">立即购买</div>
      <div class="btn btn-right" v-else-if="prize.prizeType === 7" @click="showCardNum">如何兑换</div>
      <div class="btn btn-right" v-else-if="prize.prizeType === 9 || prize.prizeType === 10" @click="exchangePlusOrAiqiyi">立即兑换</div>
      <div class="btn btn-right" v-else-if="prize.prizeType === 12" @click="savePhone">立即领取</div>
      <div class="btn btn-right" v-else @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
    </div>
    <div class="closeDiv" @click="close"></div>
  </div>
  <div class="thanks-join" v-else>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage, gotoSkuPage, exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.result.result, props.prize.userPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, showImg: props.prize.showImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  emits('savePhone', props.prize.userPrizeId, props.prize.result.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  height: 7.6rem;
  width: 5.96rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/246204/9/2174/41126/6597eb80Fabb92632/9b6b0e13f19b6189.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 0.8rem;
  .closeDiv {
    position: absolute;
    bottom: 0rem;
    left: 2.8rem;
    width: 0.5rem;
    height: 0.5rem;
  }
  .prize-name {
    font-size: 0.46rem;
    margin: 0 0 0.5rem 0;
    text-align: center;
    color: #8d6a35;
  }
  .prize-img {
    width: 2rem;
    margin: 0 auto;
  }
  .tip {
    background-image: linear-gradient(270deg, #785334 0%, #a17d59 27%, #caa67d 54%, #785334 100%);
    width: 3rem;
    height: 0.45rem;
    border-radius: 0.225rem;
    line-height: 0.45rem;
    text-align: center;
    font-size: 0.27rem;
    color: #fff;
    margin: 0.5rem auto 0;
  }
  .btn-list {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 0.8rem;
    margin-top: 0.3rem;
    .btn {
      width: 2rem;
      height: 0.7rem;
      border-radius: 0.35rem;
      line-height: 0.7rem;
      text-align: center;
      color: #000;
      background-color: #fff;
      font-size: 0.27rem;
    }
  }
}
.thanks-join {
  width: 5.6rem;
  height: 5.2rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/245870/13/2237/28833/6597eb80Fde396f10/00ebfe5cc7510fd7.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 2.8rem;
  .close {
    position: absolute;
    top: 4.8rem;
    left: 2.65rem;
    width: 0.4rem;
    height: 0.4rem;
  }
  .btn {
    width: 1.62rem;
    height: 0.45rem;
    margin: 0 auto;
  }
}
</style>
