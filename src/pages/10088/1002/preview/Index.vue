<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" v-if="isLoadingFinish">
    <img alt="暂无图片" :src="furnishStyles.kvImg.value.src"  class="kv-img"/>
    <div :style="furnishStyles.shopNameColor.value" class="shop-name-text">
      <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
    </div>
    <div class="header-btn-box">
      <div class="header-btn" :style="furnishStyles.btnBg.value" @click="showRule = true">
        活动规则
      </div>
      <div class="header-btn" :style="furnishStyles.btnBg.value"  @click="showMyPrize = true">
        我的奖品
      </div>
      <div class="header-btn" :style="furnishStyles.btnBg.value" @click="showMyOrder = true">
        我的订单
      </div>
    </div>
    <div class="tips-top"/>
    <div class="time-icon-box">
      <van-count-down :time="orderEndTime - dayjs().valueOf()" format="DD:HH:mm:ss" class="time-box">
        <template #default="timeData">
          <span class="time-text">{{ timeData.days }}</span><span class="letter-space">天</span>
          <span class="time-text">{{ timeData.hours }}</span><span class="letter-space">时</span>
          <span class="time-text">{{ timeData.minutes }}</span><span class="letter-space">分</span>
          <span class="time-text">{{ timeData.seconds }}</span><span class="letter-space">秒</span>
        </template>
      </van-count-down>
    </div>
    <!--满额有礼-->
    <div class="total" v-if="fullAmountFlag === 0">
      <div class="titles"></div>
      <div class="goods-task-list-big-box select-hover">
        <div class="goods-task-list-content">
          <div class="goods-task-list swiper-container" ref="swiperRef">
            <div class="swiper-wrapper">
              <div class="goods-task-list-box swiper-slide"
                   v-for="(item, index) in fullPrizeList"
                   :key="index"
                   :style="{'margin-left': fullPrizeList.length > 1 ? '0.15rem' : '0.4rem'}"
              >
                <div class="goods-task-list-title">
                  满<span class="goods-task-list-limit">{{item.rule}}</span>元即送
                </div>
                <div class="list-title">距离完成当前节点还差0元</div>
                <div class="goods-task-goods">
                  <div class="goods-task-goods-img-box">
                    <img  class="goods-task-goods-img" :src="item.prizeImg" alt="暂无图片"/>
                  </div>
                  <div class="goods-task-goods-info">
                    <div class="goods-task-goods-name">{{ item.prizeName }}</div>
                    <div class="goods-task-goods-remain">奖品剩余：{{ item.sendTotalCount }}份</div>
                  </div>
                </div>
                <div class="task-progress-box">
                  <div class="task-progress-num"
                       :style="{'width':`${computedProgress(fullPrizeList.length,0, fullPrizeList)*100}%`}">
                    <div class="task-red-flex">
                      <div></div>
                      <div class="red-box" v-for="(item2,index2) in fullPrizeList" :key="index2">
                        <div class="red-circ"></div>
                        <div class="price-center">满{{item2.rule}}元</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="task-progress-text-box">
                  <div class="task-progress-text">您已确认收货：0元</div>
                </div>
                <div @click="toast()">
                  <!--去下单（不可领取）-->
                  <div class="receive-btn"  :style="{backgroundImage: `url(${btnBg[0]})`}"/>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--冲榜赢大奖-->
    <div class="total">
      <div class="titles2"></div>
      <div class="show-prizes swiper-container2" ref="swiperRef">
        <div class="swiper-wrapper">
          <div class="show-prize-box swiper-slide"  v-for="(item,index) in prizeInfo" :key="index">
            <div class="show-prize-box-title">{{item.rank }}可领取</div>
            <div class="show-prize-box-fle">
              <div class="prize-box">
                <img :src="item.prizeImg || 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png'" alt="">
              </div>
              <div class="prize-info">
                <div style="font-size:0.23rem">{{item.prizeName}}</div>
                <div style="color:red">奖品剩余：{{item.sendTotalCount}}份</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--指定商品-->
    <div>
      <!--参与活动的指定商品-->
      <div v-if="skuList.length > 0">
        <div class="title-order-sku"></div>
        <div class="goods-list-box">
          <div class="goods-list-title"></div>
          <div class="content-box" v-if="skuList.length !== 0">
            <div v-for="(item, index) in skuList" :key="item.skuId + index" class="goods-box">
              <img :src="item.skuMainPicture" alt="暂无图片" class="goods-pic"/>
              <div class="goods-info-box">
                <div class="goods-name">{{ item.skuName }}</div>
                <div class="goods-info">
                  <div class="goods-price"><span class="price-unit">¥</span>{{ item.jdPrice / 1000 }}</div>
                  <div class="bug-now-btn" @click="toast()"/>
                </div>
              </div>
            </div>
            <div class="more-btn-all">
              <div class="more-btn" v-if="skuListPreview.length && skuListPreview.length !== total" @click="toast()">点我加载更多</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--全部商品-->
    <div>
      <!--去下单按钮-->
      <div v-if="skuList.length === 0" @click="toast()" class="to-buy-btn" :style="{backgroundImage: `url(${btnBg[0]})`}"></div>
    </div>
    <!--排行榜-->
    <div class="ranking-box">
      <div class="ranking-box-tips">
        <div>排名正在进行中</div>
        <div style="font-size: 0.2rem;line-height: 0.2rem;">（排行榜门槛金额：{{thresholdPrice}}元）</div>
        <span>排名每两小时更新一次哦~</span>
      </div>
      <div class="ranking-box-list" v-if="rankInfo.length > 0">
        <div class="ranking-box-list-item" v-for="(item,index) in rankInfo" :key="index">
          <div class="items">
            <div class="items-num" v-if="index<3" :style="{ 'background-image': `url('${numImg[index]}')` }"></div>
            <div v-else class="items-num2">{{index+1}}</div>
            <div class="items-img">
              <img :src="item.userImg" alt="">
            </div>
            <div class="items-name">{{item.userName}}</div>
          </div>
          <div class="ranking-box-list-item-amount">
            订单金额累计：<span>0</span>元
          </div>
        </div>
      </div>
      <div class="rank-no-data" v-else>
        <img src="https://img10.360buyimg.com/imgzone/jfs/t1/91947/20/36202/11741/63aba2b1F8fe57418/101c9be628a3df7c.png" alt="">
      </div>
    </div>
    <div class="bottom-text">我也是有底线的哟~</div>
    <!--进店逛逛-->
    <div class="bottom-to-shop"></div>
    <!-- 获奖规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 我的订单弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyOrder" position="bottom">
      <MyOrder v-if="showMyOrder" @close="showMyOrder = false"></MyOrder>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject, nextTick } from 'vue';
import furnishStyles, { furnish, bgImg } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import RulePopup from '../components/RulePopup.vue';
import { showToast } from 'vant';
import MyPrize from '../components/MyPrize.vue';
import MyOrder from '../components/OrderRecordPopup.vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import useSendMessage from '@/hooks/useSendMessage';
import dayjs from 'dayjs';

console.log(bgImg);
Swiper.use([Autoplay]);
interface Prize {
  prizeImg: string;
  prizeName: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  rule: number;
}

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
console.log(activityData, 'activityData=========');
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const showMyPrize = ref(false);
const showMyOrder = ref(false);
const rankInfo = ref([]);
const fullPrizeList = ref([]);
type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuListPreview = ref<Sku[]>([]);
const total = ref(0);
const prizeInfo = ref([
  {
    prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    prizeName: 'XX积分',
    rank: '排名为第XX名~第XX名',
    sendTotalCount: 2,
  },
]);
const assignGoodsFlag = ref(1);
const thresholdPrice = ref(0);
const skuList = ref([]);
const numImg = [
  '//img10.360buyimg.com/imgzone/jfs/t1/214317/31/16906/3536/624f9d31E2e45d8cf/8363020f60f115ff.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/219322/7/17032/3636/624f9d31E79f6ea41/442e2f2451d5e1f3.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/145169/18/26369/3813/624f9d31E2fd9a567/021e24c4f043c941.png',
];
const btnBg = [
  'https://img10.360buyimg.com/imgzone/jfs/t1/185510/6/38712/4458/650a5762F86bf0a6c/8587bc39111a9f1f.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/133986/31/38091/4262/650a5763F412367dd/20d513d9e2fd40b5.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/100404/34/45871/4215/650a5762F3f94bc5a/b418e35a2d8ee154.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/6183/13/33396/5360/650a5763Fda74e76f/4b2dfd7b6996a3d0.png'];
const ruleTest = ref('');
// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  await useHtmlToCanvas(document.getElementById('interact-c'));
};
const orderEndTime = ref(dayjs().valueOf());
const fullAmountFlag = ref(1);
let mySwiper2;
const setDataInfo = (data:any) => { // 数据赋值
  if (data.fullAmountPrizeList.length > 0) {
    fullPrizeList.value = data.fullAmountPrizeList;
  }
  console.log(data, 'data===============');
  prizeInfo.value = data.rankPrizeList;
  fullAmountFlag.value = data.fullAmountFlag;
  orderEndTime.value = dayjs(data.orderEndTime).valueOf();
  assignGoodsFlag.value = data.assignGoodsFlag;
  skuList.value = data.skuList;
  thresholdPrice.value = data.thresholdPrice;
  nextTick(() => {
    const mySwiper = new Swiper('.swiper-container', {
      autoplay: false,
      direction: 'horizontal',
      loop: false,
      slidesPerView: 'auto',
      spaceBetween: 0,
    });
    if (mySwiper2) {
      mySwiper2.destroy(true, true);
      mySwiper2 = null;
    }
    mySwiper2 = new Swiper('.swiper-container2', {
      autoplay: {
        delay: 2000,
        disableOnInteraction: false,
      },
      loop: true,
      slidesPerView: 1,
      loopedSlides: 10,
      allowTouchMove: true,
    });
  });
  if (data.rules) {
    ruleTest.value = data.rules;
  }
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    // if (data.consumptionActivityType || data.consumptionActivityType === 0) {
    //   consumptionActivityType.value = data.consumptionActivityType;
    //   // activityTypeUnitText.value = ['元', '件', '单'][data.consumptionActivityType];
    // }
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    console.log(res, 'www');
    shopName.value = data.shopName;
    setDataInfo(data);
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
const toast = () => {
  showToast('活动预览，仅供查看');
};
// 截图监听
registerHandler('screen', () => {
  createImg();
});
//
registerHandler('activity', (data) => {
  setDataInfo(data);
  if (data.skuListPreview) {
    skuListPreview.value = data.skuListPreview;
  }
  console.log(data, 'activity1111');
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  if (activityData) {
    shopName.value = activityData.shopName;
    skuListPreview.value = activityData.skuListPreview;
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    console.log(decoData, 'decoData');
    isLoadingFinish.value = true;
  }
});

/**
 * 计算进度条
 * @param total 节点个数
 * @param userPrice 用户当前订单完成金额
 * @param list 金额列表
 */
const computedProgress = (total: number, userPrice: number, list:Prize[]) => {
  const pre = 1 / total; // 一份占比
  let price = 0; // 当前节点金额
  let nextPrice = 0; // 下一个节点金额
  let num = 0; // 完成的节点个数
  for (let i = 0; i < list.length; i++) {
    if (list[i].prizeMoney < userPrice) {
      price = +list[i].prizeMoney;
      num = i + 1;
      nextPrice = +list[i + 1].prizeMoney;
    }
    if (nextPrice > userPrice) {
      break;
    }
  }
  // console.log(num, userPrice, price, nextPrice, userPrice - price, nextPrice - userPrice, 'price');
  let y = 0;
  const a = userPrice - price;
  const b = nextPrice - price;
  if (num === 0) {
    nextPrice = +list[0].prizeMoney;
    // console.log(a, price, nextPrice, pre * (a / nextPrice), 'a');
    return (a / nextPrice) * pre;
  }
  if (userPrice > price) {
    y = num + a / b;
    // console.log(pre * y, 'y');
    return pre * y;
  } if (userPrice === price) {
    return pre * num;
  }
  return 0;
};

// onUnmounted(() => {
//   window.removeEventListener('message', receiveMessage);
// });
</script>

<style scoped lang="scss">
img[src=""]
,img:not([src])
{
  opacity:0;
}
.more-btn-all {
  width:6.9rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
}
.bg {
  position: relative;
  padding-bottom: 1rem;
  min-height: 100vh;
  max-width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  .kv-img {
    position: relative;
    width: 7.5rem;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .header-btn-box {
    position: absolute;
    top: 0.9rem;
    right: 0.2rem;
    .header-btn {
      width: 1.34rem;
      height: 0.58rem;
      margin-bottom: 0.1rem;
      font-size: 0.25rem;
      text-align: center;
      background-repeat: no-repeat;
      background-size: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
  .time-icon-box {
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/222135/5/28688/6975/65095bb3Fbdc90b3c/69f617964064105b.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.9rem;
    height: 0.78rem;
    margin-left: calc(50% - 3.45rem);
    display: flex;
    align-items: center;
    margin-top: 0;
    margin-bottom: 0.6rem;
    .time-box {
      line-height: 0.5rem;
      font-size: 0.24rem;
      font-weight: bold;
      height: 0.4rem;
      border-radius: 0.2rem;
      margin-left: 3.3rem;
      padding-right: 0.2rem;
      .time-text {
        display: inline-block;
        text-align: center;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/144371/16/39437/1518/65095bb3F52a81837/216d741d5f0ffb65.png);
        background-size: 100%;
        background-repeat: no-repeat;
        width: 0.44rem;
        height: 0.53rem;
        color: rgb(255, 250, 220);
        align-items: center;
        justify-content: center;
      }
      .letter-space {
        width: 0.44rem;
        height: 0.53rem;
        margin: 0 0.05rem;
        color: #de150c;
      }
    }
  }
  .total{
    margin:0 auto;
    .titles{
      width: 4.12rem;
      height: 0.47rem;
      margin: auto auto 0.3rem;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/215289/35/35665/2514/65095bb3F119949c4/db58166d135cb6e9.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .titles2{
      width: 4.75rem;
      height: 0.47rem;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/196213/14/37289/3285/654dd042F9a5105ec/41c75819346f1468.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.5rem auto;
    }
    .swiper-box{
      width: 377.727px;
      height: 5.94rem;
      position: relative;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/121409/34/30950/2963/63aa9786Fc49d9460/63e5b86dff524755.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin:0 auto;
    }
    .goods-task-list-big-box {
      display:flex;
      justify-content: center;
      height:7rem;
      width: 7.5rem;
      .goods-task-list-content {
        padding: 0 0.2rem;
        width: 7.5rem;
        height: 7rem;
        .goods-task-list {
          width: 100%;
          height: 100%;
          overflow: hidden;
          .goods-task-list-box {
            position: relative;
            margin-left:0.15rem;
            padding: 1rem 0.65rem 0.47rem;
            height:7rem;
            width: 6.34rem;
            transform: scale(0.9);
            background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/193758/9/37916/8728/65096da4F50a5e06d/2ee60af2a0f5038e.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            .goods-task-list-title {
              position: absolute;
              transform: translateX(-50%);
              top: 0.38rem;
              left: 50%;
              font-weight: bold;
              font-size: 0.36rem;
              color: #ffffff;
              .goods-task-list-limit {
                margin: 0 0.05rem;
                color: #fcff00;
              }
            }
            .list-title{
              margin: 0.3rem auto;
              text-align:center;
              width:80%;
            }
            .goods-task-goods {
              display: flex;
              justify-content: space-between;
              align-items: center;
              height: 2rem;
              background-color: #fdfcef;
              border-radius: 0.2rem;
              margin-bottom:0.4rem;
              .goods-task-goods-img-box {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 2rem;
                height: 2rem;
                background-color: #fef2cd;
                border-radius: 0.2rem;
                .goods-task-goods-img {
                  width: 2rem;
                  height: 2rem;
                }
              }
              .goods-task-goods-info {
                padding-left:0.2rem;
                .goods-task-goods-name {
                  margin-bottom:0.12rem;
                  width: 3rem;
                  font-size: 0.3rem;
                  color: #333333;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp:2;
                  -webkit-box-orient: vertical;
                }
                .goods-task-goods-remain {
                  font-size: 0.24rem;
                  line-height: 0.13rem;
                  color: #ff0000;
                  white-space: nowrap;
                }
              }
            }
            .task-progress-box {
              display: flex;
              align-items: center;
              width: 5.2rem;
              border: 1px solid rgb(245, 35, 83);
              border-radius: 10px;
              background: #fef8de;
              .task-progress-num {
                width: 0;
                height: 0.27rem;
                background: #f35436;
                border-radius: 0.201rem;
                background-size: 20px 20px;
              }
              .task-red-flex{
                width:5.2rem;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
              }
              .red-box{
                position: relative;
                .red-circ{
                  width: 0.27rem;
                  height: 0.27rem;
                  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/197478/32/34720/1477/650a5180Fb76e8ed0/fac6aeced90d12ef.png);
                  background-size: 100% 100%;
                  background-repeat: no-repeat;
                }
                .price-center{
                  position: absolute;
                  white-space: nowrap;
                  right: -10%;
                }
              }
            }
            .task-flex-price{
              width:5.2rem;
              display: flex;
              flex-direction: row;
              justify-content: space-between;
            }
            .task-progress-text-box {
              width: 5.23rem;
              font-size: 0.22rem;
              margin: 0.45rem auto;
              background-color: #ffffff;
              border-radius: 0.3rem;
              text-align: center;
              span{
                color:red;
              }
            }
            .receive-btn {
              margin-top: 0.1rem;
              width: 5.11rem;
              height: 0.9rem;
              background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/240802/20/6644/4676/66053856Fbc71a957/c7f34bc042465ec0.png");
              background-repeat: no-repeat;
              background-size: 100% 100%;
            }
          }
          .swiper-slide-active {
            transform: scale(1);
          }
        }
      }
    }
    .show-prizes{
      overflow: hidden;
      width:6.2rem;
      margin:0 auto;
      .show-prize-box{
        width:6rem;
        height:4rem;
        background: url('https://img10.360buyimg.com/imgzone/jfs/t1/97122/17/42973/6265/65095bb4Fa0465112/b041525bc0353045.png') no-repeat;
        background-size: 100%;
        margin: 0 auto;
        border-radius: 0.1rem;
        .show-prize-box-title{
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/185270/8/36683/1501/650a6029F8c61f91f/a416d053b86058a8.png);
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 3.95rem;
          height: 0.45rem;
          align-items: center;
          text-align: center;
          margin:0.3rem auto 0;
          color: #fbfaed;
          line-height: 0.5rem;
          font-size:0.26rem;
        }
        &-fle{
          width: 100%;
          border-radius: 0.2rem;
          margin:0.3rem auto;
          background-color:#fbfaed;
          display:flex;
          .prize-box{
            width: 2rem;
            height: 2rem;
            margin: 0.3rem 0.42rem 0.23rem 0.47rem;
            border: 2px solid rgb(255, 54, 51);
            border-radius: 0.2rem;
            position: relative;
            overflow: hidden;
            background: rgb(254, 242, 205);
            img{
              margin:0 auto;
              width:2rem;
              height: 2rem;
            }
          }
          .prize-info{
            font-size:0.2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            width: calc(100% - 2.85rem);
            box-sizing: border-box;
            height: 2rem;
            margin-top: 0.3rem;
            position: relative;
          }
        }
      }
    }
  }
  .goods-list-box {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.21rem 0.21rem 0.28rem;
    width: 6.89rem;
    min-height:5rem;
    background-color: #fef2cd;
    border-radius: 0.1rem;
    .content-box {
      display: flex;
      flex-wrap: wrap;
      width: 6.89rem;
      max-height: 9.28rem;
      overflow: auto;
    }
    .goods-box {
      margin: 0 0.13rem 0.1rem 0;
      padding: 0.16rem 0.25rem 0.2rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      width: 3.15rem;
      height: 4.58rem;
      background-color: #ffffff;
      border-radius: 0.2rem;
      .goods-pic {
        width: 2.24rem;
        height: 2.24rem;
      }
    }
    .goods-info-box {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      .goods-name {
        margin-bottom:0.1rem;
        width: 2.64rem;
        //height: 0.53rem;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        //line-height: 0.3rem;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp:2;
        -webkit-box-orient: vertical;
      }
      .goods-price {
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        line-height: 0.3rem;
        color: #e2231a;
        .price-unit {
          margin-right: 0.1rem;
        }
      }
      .bug-now-btn {
        margin-top: 0.14rem;
        width: 2.56rem;
        height: 0.61rem;
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/159534/16/44028/2981/66053852Fb0d37c9a/b379560d8fd8cbc4.png ");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
  }
  .to-buy-btn{
    width: 5.11rem;
    height: 0.9rem;
    //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/64447/8/23367/3758/63ac0870F0f838c7d/c92b8348342d6265.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0.33rem auto;
  }
  .title-order-sku{
    width: 4.75rem;
    height: 0.47rem;
    margin: 0.3rem auto;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/91459/28/46576/3901/664af79bF7e176080/8bdd52bf99d146a5.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .ranking-box{
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/200804/19/30091/16805/63aba12fF6c4ef72c/d7107ef1b2f69cb8.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 6.9rem;
    height: 6.97rem;
    text-align: center;
    overflow: hidden;
    margin: 0 auto;
    position:relative;
    &-tips{
      position: absolute;
      top: 0.95rem;
      left: 2.13rem;
      color: rgb(242, 39, 12);
      font-size: 0.28rem;
      text-align: left;
      span{
        font-size:0.2rem;
        color:#000;
      }
    }
    .rank-no-data{
      font-size: 0.24rem;
      width: 100%;
      color: #8c8c8c;
      height: calc(100% - 0.4rem);
      margin: 1.24rem auto;
      padding: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      img{
        border: 0;
        max-width: 100%;
      }
    }
    &-list{
      width: 6.6rem;
      height: 4.62rem;
      overflow-y: auto;
      margin: 2.2rem 0.15rem 0.15rem;
      border-radius: 0.1rem;
      font-size: 0.28rem;
      box-sizing: border-box;
      &-item{
        width: 6.6rem;
        height: 0.9rem;
        padding: 0rem 0.21rem 0px 0.15rem;
        border-radius: 0.08rem;
        background: rgb(255, 255, 255);
        font-size: 0.28rem;
        box-sizing: border-box;
        margin-bottom: 0.03rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .items{
          display: flex;
          align-items: center;
          &-num{
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 0.36rem;
            height: 0.56rem;
            margin-right: 0.19rem;
          }
          &-num2{
            width: 0.36rem;
            height: 0.56rem;
            margin-right: 0.19rem;
          }
          &-img{
            background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/214317/31/16906/3536/624f9d31E2e45d8cf/8363020f60f115ff.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 0.56rem;
            height: 0.56rem;
            margin-right: 0.19rem;
            border-radius: 100%;
            overflow: hidden;
            img{
              width: 0.56rem;
              height: 0.56rem;
            }
          }
          &-name{
            font-size: 0.23rem;
            margin-left: 0.19rem;
          }
        }
        &-amount{
          color: rgb(148, 148, 148);
          font-size: 0.22rem;
          span{
            color:red;
          }
        }
      }
    }
  }
  .bottom-text{
    margin:0.2rem auto;
    text-align:center;
  }
  .bottom-to-shop{
    width: 7.5rem;
    height: 0.88rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/83881/27/18609/2926/63a9754fE2337993e/175e4b467dc68a90.png);
    background-size: 100%;
    z-index: 100;
    position: absolute;
    bottom: 0;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
