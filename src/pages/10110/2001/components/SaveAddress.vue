<template>
  <div class="rule-bk">
    <div class="title"></div>
    <div class="content">
      <div class="form">
        <VanField :readonly="isLookEd" v-model="form.realName" required label="姓名：" maxlength="20"></VanField>
        <VanField :readonly="isLookEd" v-model="form.mobile" required label="电话：" maxlength="11" type="number"></VanField>
        <VanField  v-model="addressCode" required label="省市区：" readonly @click="addressSelects = !isLookEd"></VanField>
        <VanField :readonly="isLookEd" v-model="form.address" required label="详细地址：" maxlength="100"></VanField>
      </div>
      <div class="tip">请注意：【地址不详】或【手机号错误】将影响名单，导致您无法收到商品！ （<span>提交后不可修改请谨慎填写</span>）</div>
    </div>
    <div class="submit" @click="checkForm">提交</div>
  </div>

  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  addressId: {
    type: String,
    required: true,
  },
  activityPrizeId: {
    type: String,
    required: true,
  },
  echoData: {
    type: Object as PropType<FormType>,
    default: () => ({
      realName: '',
      mobile: '',
      province: '',
      city: '',
      county: '',
      address: '',
    }),
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

const isLookEd = ref(false);
onMounted(() => {
  // 回显地址
  Object.keys(form).forEach((key: string) => {
    form[key] = props.echoData[key];
  });
  if (props.echoData.realName) {
    isLookEd.value = true;
  }
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});
// const addressCode = ref('');

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10110/userAddressInfo', {
      addressId: props.addressId,
      activityPrizeId: props.activityPrizeId,
      realName: form.realName,
      mobile: form.mobile,
      province: form.province,
      city: form.city,
      county: form.county,
      address: form.address,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (isLookEd.value) {
    showToast('已提交过地址，不可修改');
    return;
  }
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/239498/13/1931/34568/659b5651F672663c8/41b34bf83a0661f4.png');
  background-size: 100%;
  background-repeat: no-repeat;
  height: 7.5rem;
  width: 100vw;
  padding-top: 1.12rem;

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.55rem;
    height: 0.56rem;
  }

  .content {
    border: 0.3rem solid transparent;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .form {
      .van-cell {
        border-radius: 0.08rem;
        margin-bottom: 0.1rem;

        &::after {
          display: none;
        }
      }
    }

    .tip {
      margin-top: 0.2rem;
      font-size: 0.18rem;
      color: #262626;
      span {
        color: #940606;
      }
    }
  }

  .submit {
    width: 6.9rem;
    height: 0.8rem;
    line-height: 0.8rem;
    background-color: #513c28;
    color: #fff;
    text-align: center;
    font-size: 0.28rem;
    margin: 0 auto;
  }
}
</style>
