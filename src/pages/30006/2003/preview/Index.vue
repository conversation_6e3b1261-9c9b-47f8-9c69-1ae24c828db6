<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="rulePopup = true">活动规则</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="recordPopup = true">锁权记录</div>
        </div>
      </div>
    </div>
    <!--进度条-->
    <div class="progress">
      <div class="progress-bar">
        <div class="progress-bar-inner" :style="{ width: progressWidth }">
        </div>
        <div class="step"/>
        <div class="step1">
          预留地址<br>
          积分锁权
        </div>
        <div class="step2">
          指定下单时间<br>
          消费金额满{{stepAmount}}元
        </div>
        <div class="step3">
          确认收货后<br>
          活动首页兑礼
        </div>
      </div>
    </div>
    <div class="blank">
      <div class="exchange-area select-hover" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)">
        <img :src="furnish.giftImg" alt="" class="gift-img" />
        <div class="btn" @click="toast">点击锁权<img src="../assets/clickIcon2.png" alt="" /></div>
      </div>
      <img src="../assets/segmentation.png" alt="" class="segmentation" />
      <div class="select-hover" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
        <div class="hotZoneBox">
          <img class="step-img hotZone" :src="furnish.step" alt="" />
          <div class="hotBtn" v-for="(item, index) in showData" :key="index" :style="item.style" @click="toast"></div>
        </div>
      </div>
    </div>
  </div>

  <van-popup v-model:show="rulePopup" :closeOnClickOverlay="false">
    <PopupBk @close="rulePopup = false">
      <Rule :ruleText="ruleText"></Rule>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show="recordPopup" :closeOnClickOverlay="false">
    <PopupBk v-if="recordPopup" @close="recordPopup = false">
      <Record :tab="1"></Record>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show="failPopup" :closeOnClickOverlay="false">
    <PopupBk @close="failPopup = false">
      <Fail></Fail>
    </PopupBk>
  </van-popup>
</template>

<script lang="ts" setup>
import { inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useSendMessage from '@/hooks/useSendMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import PopupBk from '../components/PopupBk.vue';
import Rule from '../components/Rule.vue';
import Record from '../components/Record.vue';
import Fail from '../components/Fail.vue';
import { progressWidth } from '../ts/logic';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);

const rulePopup = ref(false);
const ruleText = ref('');

const recordPopup = ref(false);
const failPopup = ref(false);

const powerRangeDate = ref([]);
const orderDataList = ref<any[]>([]);
const showData = ref([]);
const stepAmount = ref(0);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  recordPopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  console.log('furnish', furnish);
  // 热区部分样式赋值
  showData.value = furnish.hotZoneList;
  showData.value.forEach((item: any) => {
    const style:any = {};
    style.width = `${((item.width * 2) / 100) * 0.9186}rem`;
    style.height = `${((item.height * 2) / 100) * 0.9186}rem`;
    style.position = 'absolute';
    style.top = `${((item.top * 2) / 100) * 0.9186}rem`;
    style.left = `${((item.left * 2) / 100) * 0.9186}rem`;
    item.style = style;
  });
  console.log(furnish.hotZoneList);
  console.log(showData.value);
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  powerRangeDate.value = data.powerRangeDate;
  orderDataList.value = data.orderDataList;
  if (data.countOrderType === 0) {
    stepAmount.value = data.totalOrderAmount;
  }
  if (data.countOrderType === 1) {
    stepAmount.value = data.orderDataList?.[0].totalOrderAmount;
  }
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});
// 弹窗监听
registerHandler('popup', (data: any) => {
  failPopup.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    powerRangeDate.value = activityData.powerRangeDate;
    orderDataList.value = activityData.orderDataList;
    if (activityData.countOrderType === 0) {
      stepAmount.value = activityData.totalOrderAmount;
    }
    if (activityData.countOrderType === 1) {
      stepAmount.value = activityData.orderDataList?.[0].totalOrderAmount;
    }
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    showData.value = furnish.hotZoneList;
    console.log(furnish.hotZoneList, 'hotZoneList');
    showData.value.forEach((item: any) => {
      const style: any = {};
      style.width = `${((item.width * 2) / 100) * 0.9186}rem`;
      style.height = `${((item.height * 2) / 100) * 0.9186}rem`;
      style.position = 'absolute';
      style.top = `${((item.top * 2) / 100) * 0.9186}rem`;
      style.left = `${((item.left * 2) / 100) * 0.9186}rem`;
      item.style = style;
    });
    console.log(showData.value, 'hotZoneList');
    console.log(showData.value[0].style);
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>

<style>
@font-face {
  font-family: 'OPPOSansM';
  src: url('../assets/OPPOSans-M.ttf') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'fzcjljt';
  src: url('../assets/fzcjljt.TTF') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'fzcjljt',serif;
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.54rem 0rem 0.3rem 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.2rem;
    height: 0.3rem;
    line-height: 0.3rem;
    margin-bottom: 0.1rem;
    font-size: 0.19rem;
    text-align: center;
    border-radius: 0.23rem 0 0 0.23rem;
    border: 0.01rem;
    border-style: solid;
  }
}
.progress {
  position: relative;
  .progress-bar {
    width: 6.77rem;
    height: 0.25rem;
    margin: 0 auto;
    background: url('../assets/bar.png') no-repeat;
    background-size: 100%;
    position: absolute;
    bottom: 0.6rem;
    left: (7.5rem-6.77rem)/2;
    .progress-bar-inner {
      //width: 80%;
      height: 0.07rem;
      border-radius: 0.2rem;
      background-image: url('../assets/inner.png');
      background-repeat: no-repeat;
      background-size: 120% 100%;
      background-position: center;
      position:relative;
    }
    .step{
      background: url('../assets/stepPoints.png') no-repeat;
      background-size: 100%;
      height: 1.18rem;
      width: 5.67rem;
      position: relative;
      top: -0.45rem;
      left: (6.77rem-5.67rem)/ 2;
    }
    .step1{
      font-family: 'OPPOSansM',serif;
      width: 2.4rem;
      position: absolute;
      top: 0.28rem;
      left: -0.3rem;
      text-align: center;
      font-weight: 500;
      font-size: 0.24rem;
    }
    .step2{
      font-family: 'OPPOSansM',serif;
      width: 2.4rem;
      position: absolute;
      top: 0.28rem;
      left: 2.1rem;
      text-align: center;
      font-weight: 500;
      font-size: 0.24rem;
    }
    .step3{
      font-family: 'OPPOSansM',serif;
      width: 2.4rem;
      position: absolute;
      top: 0.28rem;
      left: 4.6rem;
      text-align: center;
      font-weight: 500;
      font-size: 0.24rem;
    }
  }
}
.blank {
  padding: 0.3rem 0.26rem;
  margin: 0 auto;
}
.exchange-area {
  padding: 0.2rem 0.25rem 0.27rem;
  border-radius: 0.1rem;
  margin-bottom: 0.5rem;
  background: url('../assets/showPrizeBg.png') no-repeat;
  background-size: 100%;
  //width: 7.24rem;
  .gift-img {
    width: 100%;
    margin-bottom: 0.2rem;
  }
  .no-point {
    text-align: center;
    font-size: 0.18rem;
    color: #e3101c;
    margin-bottom: 0.15rem;
  }
  .btn {
    background: url('../assets/btnRed.png') no-repeat;
    background-size: 100%;
    width: 3.7rem;
    height: 0.67rem;
    margin: 0 auto 0.2rem;
    font-size: 0.28rem;
    text-align: center;
    line-height: 0.65rem;
    color: #fff;
    img {
      display: inline-block;
      vertical-align: middle;
      width: 0.38rem;
      margin-left: 0.1rem;
    }
  }
  .tip {
    text-align: center;
    font-size: 0.14rem;
    color: #000;
  }
}
.hotZoneBox {
  width: 6.89rem;
  margin: 0 auto;
  position: relative;
}
.hotZone {
  width: 100%;
}

.hotBtn {
  //background-color: #000;
}
.segmentation {
  width: 100%;
  margin-bottom: 0.3rem;
}

.step-img {
  width: 100%;
}
</style>
