<template>
  <div class="bk">
    <div class="text">温馨提示：</div>
    <div class="text">您还未填写收货信息</div>
    <div class="btn" @click="toFillIn">去填写<img src="../assets/clickIcon2.png" alt="" /></div>
  </div>
</template>

<script lang="ts" setup>
const emits = defineEmits(['toFillIn']);

const toFillIn = () => {
  emits('toFillIn', 1);
};
</script>

<style scoped lang="scss">
.bk {
  width: 5.86rem;
  padding-top: 1.12rem;
  color: #990a08;
  .text {
    text-align: center;
    font-family: 'fzcjljt';
    font-size: 0.48rem;
    margin-bottom: 0.25rem;
    span {
      font-family: 'fzcjljt';
      color: #d7050e;
    }
  }
  .text2 {
    text-align: center;
    font-size: 0.36rem;
    opacity: 0.8;
    margin-top: 0.7rem;
  }
  .btn {
    width: 3.71rem;
    height: 0.64rem;
    background: url(../assets/btnRed.png) no-repeat;
    background-size: 100%;
    margin: 0.8rem auto 0.9rem;
    font-size: 0.366rem;
    color: #fff;
    text-align: center;
    line-height: 0.64rem;
    img {
      display: inline-block;
      width: 0.38rem;
      vertical-align: middle;
      margin-left: 0.2rem;
    }
  }
}
</style>
