<template>
  <div class="popup-bg">
    <div class="border">
      <div class="close"><img src="../assets/close.png" alt="" @click="close" /></div>
      <img v-if="hasLogo" src="../assets/logo.png" alt="" class="logo" />
      <slot></slot>
      <div class="footer"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{ hasLogo: boolean }>(), {
  hasLogo: true,
});
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.popup-bg {
  background-image: url(../assets/popupBg.png);
  background-repeat: no-repeat;
  background-size: 6.84rem 14rem;
  background-position: center;
  border-radius: 0.34rem;
  box-shadow: inset 0.093rem 0.076rem 0.076rem 0.014rem rgba(48, 48, 48, 0.24);
  // border: solid 0.02rem #7d7d7d;
  .border {
    background-image: url(../assets/border1.png), url(../assets/border2.png), url(../assets/border3.png);
    background-repeat: no-repeat, no-repeat, no-repeat;
    background-size: 100%, 100% calc(100% - 1.28rem), 100%;
    background-position-y: top, 0.64rem, bottom;
  }
  .close {
    padding: 0.3rem 0.3rem 0.2rem 0;
    text-align: right;
    img {
      display: inline-block;
      width: 0.3rem;
    }
  }
  .logo {
    width: 2.4rem;
    margin: 0 auto;
  }
  .footer {
    height: 0.78rem;
  }
}
</style>
