import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref } from 'vue';

interface ActData {
  address: boolean;
  backUp: boolean;
  exposeProducts: boolean;
  giftEndTime: number;
  giftStartTime: number;
  orderCnt: number;
  orderEndTime: number;
  orderStartTime: number;
  orderStatus: number;
  overDays: number;
  points: number;
  powerEndTime: number;
  powerStartTime: number;
  priceTypeFlag: number;
  prizeId: string;
  prizeImg: string;
  prizeName: string;
  status: number;
  totalOrderAmount: string;
  trackingNum: string;
  userPoints: number;
}

export const actData = ref<ActData>({
  address: false,
  backUp: false,
  exposeProducts: false,
  giftEndTime: 0,
  giftStartTime: 0,
  orderCnt: 1,
  orderEndTime: 0,
  orderStartTime: 0,
  orderStatus: 0,
  overDays: 7,
  points: 1,
  powerEndTime: 0,
  powerStartTime: 0,
  priceTypeFlag: 0,
  prizeId: '',
  prizeImg: '',
  prizeName: '',
  status: 3,
  totalOrderAmount: '',
  trackingNum: '',
  userPoints: 0,
});

export const getActData = async () => {
  try {
    const res = await httpRequest.post('/30006/activity');
    console.log(res);
    actData.value = res.data;
  } catch (error: any) {
    if (error.message) {
      showToast(error.message);
    }
  }
  return {};
};

export const stepAmount = ref('');
export const progressWidth = ref('0%');
export const getUserStep = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/30006/getUserStep');
    progressWidth.value = `${(res.data.currentStep * 33.33)}%`;
    stepAmount.value = res.data.stepAmount;
    console.log(progressWidth.value);
    closeToast();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
