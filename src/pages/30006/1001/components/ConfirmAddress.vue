<template>
  <div class="popup-bg">
    <div class="bk">
      <div class="title">
        <div>确认提交后不支持修改</div>
        <div class="bold">请再次确认填写的信息</div>
      </div>
      <div class="line"></div>
      <div class="title">
        <div>姓名：{{ form.realName }}</div>
        <div>手机号：{{ form.mobile }}</div>
        <div>省市区：{{ `${form.province}/${form.city}/${form.county}` }}</div>
        <div>详细地址：{{ form.address }}</div>
      </div>
      <div class="footer">
        <div class="btn btn-black" @click="close">返回修改</div>
        <div class="btn" @click="submit">确认提交并锁权</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { inject } from 'vue';
import { actData, getActData } from '../ts/logic';
import dayjs from 'dayjs';

interface Form {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
}
const props = withDefaults(defineProps<{ form: Form }>(), {});
const emits = defineEmits(['close', 'success']);

const locking = async () => {
  try {
    await httpRequest.post('/30006/lockRight');
    showLoadingToast('锁权成功');
    closeToast();
    emits('success');
    getActData();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

const submit = async () => {
  if (dayjs().isBefore(actData.value.powerStartTime)) {
    showToast('锁权未开始');
    return;
  }
  if (dayjs().isAfter(actData.value.powerEndTime)) {
    showToast('锁权已结束');
    return;
  }
  if (actData.value.userPoints < actData.value.points) {
    showToast('积分不足');
    return;
  }
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/30006/fallAddress', {
      activityPrizeId: actData.value.prizeId,
      ...props.form,
    });
    locking();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.popup-bg {
  width: 6.07rem;
  // height: 6.28rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/153623/24/24644/5704/664eb933Fc9993736/9efd7415226b2fbb.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: top;
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  position: relative;
}
.bk {
  padding-top: 0.43rem;
  .title {
    padding: 0 0.28rem 0;
    text-align: center;
    font-size: 0.3rem;
    line-height: 0.5rem;
    .bold {
      font-family: 'CenturyGothic', 'FZLTHJW';
    }
  }
  .line {
    width: 5.31rem;
    height: 0.01rem;
    border-bottom: dashed 0.01rem #000000;
    margin: 0.2rem auto;
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.54rem;
    margin-top: 0.5rem;
    .btn {
      width: 2.24rem;
      height: 0.54rem;
      border-radius: 0.33rem;
      color: white;
      background: #472f0d;
      font-size: 0.24rem;
      text-align: center;
      line-height: 0.54rem;
      color: #fff;
    }
    .btn-black {
      background: #472f0d;
    }
  }
}
</style>
