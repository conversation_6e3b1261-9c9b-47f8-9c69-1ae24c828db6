<template>
  <div class="popup-bg">
    <div class="bk">
      <div class="tabs">
        <div class="tab" v-for="item in tabs" :key="item.id" :class="{ 'tab-active': item.id === activeTab }" @click="changTab(item.id)">{{ item.title }}</div>
      </div>
      <div class="content">
        <div class="tab0" v-if="activeTab === 1">
          <div v-if="powerTime">{{ powerTime }}锁定权益</div>
          <div v-else>暂无锁权记录~</div>
        </div>
        <div class="tab1" v-else-if="activeTab === 2">
          <div class="address" v-if="addressResponse.realName">
            <p>姓名：{{ addressResponse.realName }}</p>
            <p>手机号：{{ addressResponse.mobile }}</p>
            <p>省市区：{{ addressResponse.province }} {{ addressResponse.city }} {{ addressResponse.county }}</p>
            <p>详细地址：{{ addressResponse.address }}</p>
            <p style="margin-top: 0.35rem">快递单号:</p>
            <p class="id">{{ addressResponse.trackingNum || '暂无快递单号' }}</p>
            <div class="btn copy-btn" v-if="addressResponse.trackingNum" :copy-text="addressResponse.trackingNum">复制单号</div>
          </div>
          <div class="no-data" v-else>
            <div class="tip">暂无收货信息~</div>
          </div>
        </div>
        <div class="tab0" v-else-if="activeTab === 3">
          <div style="line-height: 0.9rem" v-if="exchangeTime">
            亲爱的用户<br />
            您在{{ exchangeTime }}<br />
            成功兑换好礼
          </div>
          <div v-else>暂无好礼兑换记录~</div>
        </div>
        <div class="tab2" v-else-if="activeTab === 4">
            <div class="item" v-if="prizeContent.prizeName" >
              <div class="bk">
                <div style="text-align: center">{{prizeContent.prizeName}}</div>
                <div class="prize">
                  <img :src="prizeContent.prizeImg" alt="" />
                </div>
                <div v-if="prizeContent.prizeType === 7">
                  <div class="item" v-if="prizeContent.cardNumber" >
                    <div class="label">卡号：</div>
                    <div class="text">{{ prizeContent.cardNumber }}</div>
                    <div class="copy-btn" :copy-text="prizeContent.cardNumber">复制</div>
                  </div>
                  <div class="item" v-if="prizeContent.cardPassword" >
                    <div class="label">卡密：</div>
                    <div class="text">{{ prizeContent.cardPassword }}</div>
                    <div class="copy-btn" :copy-text="prizeContent.cardPassword">复制</div>
                  </div>
                </div>
                <div class="btn" v-else-if="[9,10].includes(actData.prizeType)"  @click="exchangePlusOrAiqiyi">立即兑换<img src="../assets/clickIcon2.png" alt="" /></div>
                <div class="btn" v-else  >已发放</div>
              </div>
            </div>
            <div class="no-data" v-else>
              <div class="tip">暂无奖品信息~</div>
            </div>
        </div>
      </div>
    </div>
  </div>
  <img src="//img10.360buyimg.com/imgzone/jfs/t1/222206/4/41536/1776/664eb0f3F63ac9a1d/889a4296ed579ddb.png" alt="" class="close" @click="close()" />
</template>

<script lang="ts" setup>
import { watch, ref } from 'vue';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';

import Clipboard from 'clipboard';
import { isPreview } from '@/utils';
import { actData } from '@/pages/30006/1001/ts/logic';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

const props = withDefaults(defineProps<{ tab: number }>(), {
  tab: 1,
});
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const tabs = ref([
  {
    id: 1,
    title: '锁权记录',
  },
  {
    id: 2,
    title: '收货信息',
  },
  {
    id: 3,
    title: '兑换记录',
  },
]);
const activeTab = ref(props.tab);

const powerTime = ref('');
const addressResponse = ref<any>('');
const exchangeTime = ref('');
const prizeContent = ref({});
const getMyRecord = async (type: number) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/30006/getMyRecord', {
      type,
    });
    closeToast();
    if (type === 1) {
      powerTime.value = res.data.powerTime ? dayjs(res.data.powerTime).format('YY年M月D日') : '';
    } else if (type === 2) {
      addressResponse.value = res.data.activity30006AddressResponse;
    } else if (type === 3) {
      exchangeTime.value = res.data.exchangeTime ? dayjs(res.data.exchangeTime).format('YY年M月D日') : '';
    } else if (type === 4 && actData.value.prizeType === 7) {
      prizeContent.value = {
        ...res.data,
        ...JSON.parse(res.data.prizeContent),
      };
    } else {
      prizeContent.value = res.data;
    }
  } catch (error: any) {
    closeToast();
    // if (error.message) {
    //   showToast(error.message);
    // }
  }
};

const changTab = (id: number) => {
  activeTab.value = id;
  !isPreview && getMyRecord(activeTab.value);
};

watch(
  () => actData.value.prizeType,
  (newVal) => {
    if (newVal === 3) {
      tabs.value = [
        { id: 1, title: '锁权记录' },
        { id: 2, title: '收货信息' },
        { id: 3, title: '兑换记录' },
      ];
    } else {
      tabs.value = [
        { id: 1, title: '锁权记录' },
        { id: 4, title: '奖品信息' },
        { id: 3, title: '兑换记录' },
      ];
    }
  },
  { immediate: true },
);

!isPreview && getMyRecord(activeTab.value);

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.popup-bg {
  width: 6.07rem;
  // height: 6.28rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/153623/24/24644/5704/664eb933Fc9993736/9efd7415226b2fbb.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: top;
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  position: relative;
}
.close {
  width: 0.7rem;
  height: 0.65rem;
  display: block;
  margin: 0 auto;
  margin-top: 0.2rem;
}
.bk {
  width: 5.86rem;
  margin: 0 auto;
}
.tabs {
  padding: 0.45rem 0.25rem 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tab {
    width: 1.6rem;
    height: 0.5rem;
    border-radius: 0.1rem;
    background: #4a4a4a;
    color: white;
    font-size: 0.24rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .tab-active {
    background: #472f0d;
  }
}
.content {
  .tab0 {
    padding: 1.15rem 0;
    div {
      text-align: center;
      font-size: 0.4rem;
    }
    .item {
      display: flex;
      align-items: center;
      font-size: 0.3rem;
      margin: 0.2rem 0;
      .label {
        color: #000;
        width: 1rem;
      }
      .text {
        flex: 1;
        color: #000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 0.1rem;
        line-height: 0.45rem;
        border-radius: 0.05rem 0 0 0.05rem;
      }
      .copy-btn {
        width: 1rem;
        line-height: 0.45rem;
        text-align: center;
        color: #000;
        font-size: 0.24rem;
        border-radius: 0 0.05rem 0.05rem 0;
        background-color: #fff;
      }
    }
  }
  .tab1 {
    .address {
      padding-top: 0.43rem;
      padding-bottom: 0.33rem;
      p {
        padding: 0 0.25rem;
        text-align: center;
        font-size: 0.24rem;
        line-height: 0.45rem;
      }
      .id {
        width: 4.95rem;
        margin: 0 auto 0.4rem;
        background-color: #dee3e9;
        border: solid 0.01rem #ffffff;
        height: 0.45rem;
      }
    }
    .no-data {
      padding: 1.15rem 0 1rem;
      .tip {
        text-align: center;
        font-size: 0.4rem;
        // margin-bottom: 1.5rem;
      }
    }
  }
  .tab2 {
    padding:0.2rem 0;
    .item {
      display: flex;
      align-items: center;
      font-size: 0.3rem;
      margin: 0.2rem auto;
      width: 5rem;
      justify-content: space-between;
      .label {
        color: #000;
        width: 1rem;
      }
      .text {
        flex: 1;
        color: #000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 0.1rem;
        line-height: 0.45rem;
        border-radius: 0.05rem 0 0 0.05rem;
      }
      .copy-btn {
        width: 1rem;
        line-height: 0.45rem;
        text-align: center;
        color: #000;
        font-size: 0.24rem;
        border-radius: 0 0.05rem 0.05rem 0;
        background-color: #fff;
      }
    }
    .bk {
      width: 5.86rem;
      margin: 0 auto;
      .prize {
        width: 4.91rem;
        height: 3.3rem;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          height: 2.8rem;
        }
      }
      .btn {
        width: 3.71rem;
        height: 0.64rem;
        background-color: #472f0d;
        border-radius: 0.32rem;
        margin: 0.35rem auto 0.3rem;
        font-size: 0.366rem;
        color: #fff;
        text-align: center;
        line-height: 0.64rem;
      }
    }
    .no-data {
      padding: 1.15rem 0 1rem;
      .tip {
        text-align: center;
        font-size: 0.4rem;
        // margin-bottom: 1.5rem;
      }
    }
  }
  .btn {
    width: 3.71rem;
    height: 0.64rem;
    border-radius: 0.32rem;
    color: white;
    background: #cb0304;
    margin: 0 auto;
    font-size: 0.366rem;
    color: #fff;
    text-align: center;
    line-height: 0.64rem;
    img {
      display: inline-block;
      width: 0.38rem;
      vertical-align: middle;
      margin-left: 0.2rem;
    }
  }
}

</style>
