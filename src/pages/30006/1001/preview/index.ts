import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/211658/13/34637/804868/664ea470F871b83ae/eea52e9ed2542a33.gif',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/158038/17/42442/760303/65dfee85F0fbeead3/9013accfa190218e.png',
  actBgColor: '#bfa07d',
  shopNameColor: '#03581b',
  prizeBtnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/134072/39/40196/2501/65dfe16fF31d7b83f/4ad703a40bea4bbd.png',
  ruleBtnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/134657/4/42195/2582/65dfe171F8df641bf/e9abc72f3a03a339.png',
  recordBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/195968/12/45639/5377/664e9d30Fb1823dfb/d1c80050ebf4d053.png',
  cutDownColor: '#78541f',
  cutDownBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/226139/35/13333/10140/65dfe171Ff3b4b703/608dc993fc2b3d03.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/206448/26/43837/28481/664db969Fd0f06130/eeb147395bc7fdda.png',
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/90315/2/41415/15405/664dd8c8Fc74c6371/eed212d5eeff761f.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/103916/5/45425/56414/664f1369F16571bef/172fcd06a305058f.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/112547/27/41024/47705/664f136aFdc025fbd/1ae8d4d550cde0a9.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/199867/38/43735/8509/664f136aF105e60f9/1f7776a22988328b.png',
};
document.title = '积分锁权';
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
