<template>
  <div class="bk">
    <div class="title">
      很抱歉，奖品已兑完<br />
      您可参与店铺内其他活动
    </div>
    <div class="btn">进店逛逛 ></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
import { inject } from 'vue';
import { isPreview } from '@/utils';

const baseInfo = inject('baseInfo') as BaseInfo;
</script>

<style scoped lang="scss">
.bk {
  width: 5.86rem;
  padding-top: 0.4rem;
  .title {
    text-align: center;
    padding: 0 0.27rem;
    font-size: 0.48rem;
    font-family: 'CenturyGothic', 'FZLTHJW';
    margin-bottom: 0.4rem;
  }
  .other {
    width: 4.91rem;
    margin: 0 auto;
  }
  .prize {
    width: 4.91rem;
    height: 3.3rem;
    background: url(../assets/prizeBg2.png) no-repeat;
    background-size: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      height: 2.8rem;
    }
  }
  .btn {
    width: 3.44rem;
    height: 0.66rem;
    border-radius: 0.33rem;
    color: white;
    background: #cb0304;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.15rem auto 0 auto;
    font-size: 0.38rem;
  }
}
</style>
