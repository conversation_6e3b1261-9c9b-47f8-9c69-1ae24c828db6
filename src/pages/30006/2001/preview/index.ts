import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/248113/9/3987/176068/65b4a066F7613bdef/ef26543697a65796.png',
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/225221/36/13098/28391/65b361e9F7bee8db6/1a3a8c8cccd61219.png', // 主页背景图
  actBgColor: '#e8e8e8', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '#000004', // 按钮字体颜色
  btnBg: 'rgba(0, 0, 0, 0)', // 按钮背景颜色
  btnBorderColor: '#000004', // 按钮边框颜色
  giftImg: '//img10.360buyimg.com/imgzone/jfs/t1/56323/10/23831/166191/65b361e9F6af84f5c/e8d1798d2c6c0c9c.png',
  step: '//img10.360buyimg.com/imgzone/jfs/t1/93691/25/46623/340100/65b361e9Faa161d9d/d0c5d119c8a76857.png',
  popupImg: '//img10.360buyimg.com/imgzone/jfs/t1/241243/33/4225/101729/65b7a06eF6db56827/0a66df2a6ab12576.png',
  popupLink: '',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/89883/24/37948/29417/650c07c9F8a5f2c33/07ee4745235da699.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/89883/24/37948/29417/650c07c9F8a5f2c33/07ee4745235da699.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/89883/24/37948/29417/650c07c9F8a5f2c33/07ee4745235da699.png',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
