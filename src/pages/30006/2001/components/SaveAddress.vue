<template>
  <div class="bk">
    <div>
      <div class="title">温馨提示</div>
      <div class="title2">请预留收货信息&nbsp;&nbsp;</div>
    </div>
    <div class="form">
      <VanField v-model="form.realName" label="姓 名：" labelWidth="1.44rem" class="field" labelClass="label" inputAlign="center" placeholder="请输入姓名" :maxlength="20"></VanField>
      <VanField v-model="form.mobile" label="手机号：" labelWidth="1.44rem" class="field" labelClass="label" inputAlign="center" placeholder="请输入手机号" type="number" :maxlength="11"></VanField>
      <VanField v-model="address" label="省市区：" class="field top-label" labelClass="label" labelAlign="top" placeholder="选择省市区" readonly @click="selectAddress = true"></VanField>
      <VanField v-model="form.address" label="详细地址：" class="field top-label" labelClass="label" labelAlign="top" placeholder="请输入" :maxlength="30"></VanField>
    </div>
    <div class="tip">请确认是否花费{{ actData.points }}积分进行权益锁定</div>
    <div class="user-points">
      您目前剩余<span>{{ actData.userPoints }}</span
      >积分
    </div>
    <div class="footer">
      <div class="btn btn-black" @click="close">取消锁权</div>
      <div class="btn" @click="checkForm">确认提交并锁权</div>
    </div>
  </div>
  <VanPopup v-model:show="selectAddress" position="bottom" teleport="body">
    <VanArea :area-list="areaList" @confirm="confirmArea" @cancel="selectAddress = false"></VanArea>
  </VanPopup>
  <VanPopup v-model:show="confirmPopup" teleport="body">
    <PopupBk @close="confirmPopup = false">
      <ConfirmAddress :form="form" @close="confirmPopup = false" @success="success"></ConfirmAddress>
    </PopupBk>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from 'vue';
import { areaList } from '@vant/area-data';
import { showToast } from 'vant';
import PopupBk from './PopupBk.vue';
import ConfirmAddress from './ConfirmAddress.vue';
import { actData } from '../ts/logic';

const emits = defineEmits(['close']);

const confirmPopup = ref(false);

const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

const address = computed(() => (form.province ? `${form.province}/${form.city}/${form.county}` : ''));
const selectAddress = ref(false);

const confirmArea = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  selectAddress.value = false;
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (reg.test(form.realName)) {
    showToast('姓名不能包含表情');
  } else if (!form.mobile) {
    showToast('请输入手机号');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的手机号');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else if (reg.test(form.address)) {
    showToast('详细地址不能包含表情');
  } else {
    // submit();
    confirmPopup.value = true;
  }
};

const close = () => {
  emits('close');
};

const success = () => {
  confirmPopup.value = false;
  emits('close');
};
</script>

<style scoped lang="scss">
.bk {
  width: 5.86rem;
  padding-top: 0.38rem;
  .title {
    text-align: center;
    font-size: 0.48rem;
    font-family: 'CenturyGothic', 'FZLTHJW';
    margin-bottom: 0.2rem;
    color: #d7050e;
  }
  .title2 {
    text-align: center;
    font-size: 0.3rem;
    margin-bottom: 0.3rem;
  }
  .title3 {
    text-align: center;
    font-size: 0.48rem;
    font-family: 'CenturyGothic', 'FZLTHJW';
    margin-bottom: 0.2rem;
  }
  .form {
    width: 4.55rem;
    margin: 0 auto;
    .field {
      padding: 0;
      background-color: transparent;
      margin-bottom: 0.3rem;
    }
  }
}
.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.54rem;
  .btn {
    width: 2.24rem;
    height: 0.54rem;
    background: url(../assets/btnRed2.png) no-repeat;
    background-size: 100% 100%;
    font-size: 0.26rem;
    text-align: center;
    line-height: 0.54rem;
    color: #fff;
  }
  .btn-black {
    background-image: url(../assets/btnBlack.png);
  }
}
.btn2 {
  width: 3.71rem;
  height: 0.64rem;
  background: url(../assets/btnRed.png) no-repeat;
  background-size: 100%;
  margin: 0 auto;
  font-size: 0.366rem;
  color: #fff;
  text-align: center;
  line-height: 0.64rem;
  img {
    display: inline-block;
    width: 0.38rem;
    vertical-align: middle;
    margin-left: 0.2rem;
  }
}
.tip {
  padding: 0 0.27rem;
  font-size: 0.18rem;
  margin-top: 0.2rem;
  margin-bottom: 0.2rem;
  text-align: center;
}
.user-points {
  margin-bottom: 0.2rem;
  font-size: 0.24rem;
  font-family: 'CenturyGothic', 'FZLTHJW';
  color: #d7050e;
  text-align: center;
  span {
    font-size: 0.34rem;
    font-weight: bold;
  }
}
</style>
<style lang="scss">
.label {
  margin-right: 0;
  font-size: 0.3rem;
  * {
    color: #000;
    font-family: 'CenturyGothic', 'FZLTHJW';
  }
}
.form {
  input {
    background-color: #dee3e9;
    border: solid 0.01rem #ffffff;
    font-size: 0.24rem;
    &::placeholder {
      color: #000;
    }
  }
  .top-label {
    .label {
      margin-bottom: 0.3rem;
      margin-right: 0.3rem;
    }
    input {
      background-color: transparent;
      border: transparent;
      border-bottom: dashed 0.01rem #000;
      font-size: 0.24rem;
      &::placeholder {
        color: #000;
      }
    }
  }
}
</style>
