<template>
  <div class="bk">
    <div class="title">
      很抱歉，奖品已兑完<br />
      您可参与店铺内其他活动
    </div>
    <img :src="furnish.popupImg" alt="" class="other" @click="goLink" />
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
import { inject } from 'vue';
import { furnish } from '../ts/furnishStyles';
import { isPreview } from '@/utils';

const baseInfo = inject('baseInfo') as BaseInfo;

const goLink = () => {
  if (isPreview) return;
  window.location.href = furnish.popupLink;
};
</script>

<style scoped lang="scss">
.bk {
  width: 5.86rem;
  padding-top: 0.4rem;
  .title {
    text-align: center;
    padding: 0 0.27rem;
    font-size: 0.48rem;
    font-family: 'CenturyGothic', 'FZLTHJW';
    margin-bottom: 0.4rem;
  }
  .other {
    width: 4.91rem;
    margin: 0 auto;
  }
  .prize {
    width: 4.91rem;
    height: 3.3rem;
    background: url(../assets/prizeBg2.png) no-repeat;
    background-size: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      height: 2.8rem;
    }
  }
  .btn {
    width: 3.71rem;
    height: 0.64rem;
    background: url(../assets/btnRed.png) no-repeat;
    background-size: 100%;
    margin: 0.35rem auto 0.3rem;
    font-size: 0.366rem;
    color: #fff;
    text-align: center;
    line-height: 0.64rem;
    img {
      display: inline-block;
      width: 0.38rem;
      vertical-align: middle;
      margin-left: 0.2rem;
    }
  }
}
</style>
