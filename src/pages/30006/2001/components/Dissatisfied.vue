<template>
  <div class="bk">
    <div class="title">很抱歉 您未满足活动条件！</div>
    <div class="text">
      感谢您对薇诺娜的喜爱和支持<br />
      您不满足活动条件的原因可能如下所示：<br />
      顾客在首次入会后：<br />
      1、在活动指定下单期间，累计购买正装产品金额不符合兑礼条件。<br />
      2、购买的订单完成时间未超过{{ actData.overDays }}天，请{{ actData.overDays }}天后再申领好礼。<br />
      *详见活动规则
    </div>
    <div class="content" v-if="skuList.length">
      <div class="sku-list">
        <div class="sku" v-for="item in skuList" :key="item.skuId">
          <img :src="item.skuMainPicture" alt="" class="img" />
          <div class="name">{{ item.skuName }}</div>
          <div class="btn">
            <div class="price"><span>￥</span>{{ item.jdPrice }}</div>
            <div class="buy" @click="gotoSkuPage(item.skuId)">立即抢购></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, ref } from 'vue';
import dayjs from 'dayjs';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { actData } from '../ts/logic';

interface Sku {
  jdPrice: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
}

const skuList = ref<Sku[]>([]);

const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/30006/orderSkuList');
    skuList.value = res.data;
    skuList.value.forEach((item) => {
      item.jdPrice /= 1000;
    });
    closeToast();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
getSkuList();
</script>

<style scoped lang="scss">
.bk {
  width: 5.86rem;
  padding-top: 0.46rem;
  color: #000004;
  .title {
    text-align: center;
    font-size: 0.3rem;
    font-weight: bold;
    font-family: 'CenturyGothic', 'FZLTHJW';
    margin-bottom: 0.2rem;
  }
  .text {
    padding: 0 0.27rem;
    text-align: center;
    font-size: 0.2rem;
    opacity: 0.8;
    line-height: 0.36rem;
    margin-bottom: 0.24rem;
  }
  .content {
    width: 5.2rem;
    height: 6.8rem;
    box-shadow: inset 0.031rem 0.025rem 0.017rem 0.003rem rgba(48, 48, 48, 0.23);
    border-radius: 0.2rem;
    border: solid 0.01rem #bbbbbb;
    margin: 0 auto;
    padding: 0.2rem 0;
    .sku-list {
      padding: 0 0.2rem;
      height: 100%;
      overflow-y: scroll;
      white-space: pre-wrap;
      font-size: 0.18rem;
      line-height: 0.28rem;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .sku {
        width: 2.3rem;
        margin-bottom: 0.2rem;
        .img {
          width: 2.3rem;
          height: 2.3rem;
          object-fit: cover;
          box-shadow: 0.016rem 0.013rem 0rem 0rem rgba(0, 0, 0, 0.26);
          border-radius: 0.05rem;
          border: solid 0.01rem #ffffff;
        }
        .name {
          font-size: 0.2rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: center;
          padding: 0.1rem 0;
          opacity: 0.8;
        }
        .btn {
          width: 2.11rem;
          height: 0.4rem;
          background: url(../assets/btnRed.png) no-repeat;
          background-size: 100% 100%;
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          .price {
            height: 0.25rem;
            font-size: 0.26rem;
            font-weight: bold;
            border-right: 0.01rem solid #fff;
            padding-right: 0.05rem;
            span {
              font-size: 0.19rem;
            }
          }
          .buy {
            padding-left: 0.05rem;
            font-size: 0.2rem;
          }
        }
      }
    }
  }
}
</style>
