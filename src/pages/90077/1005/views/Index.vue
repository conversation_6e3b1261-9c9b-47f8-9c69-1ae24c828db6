<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv ">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img"/>
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <!--{{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" v-click-track="'hdgz'" :style="furnishStyles.headerBtnRules.value" @click="showRulePopup"/>
          <div class="header-btn" v-click-track="'wdjp'" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop"/>
        </div>
      </div>
    </div>
    <div class="hotZoneBox">
      <!-- 背景图片 -->
      <img class="hotZone" :src="furnish.prizeBg" alt="" />
      <!-- 奖品剩余数量显示 -->
      <div
        v-for="(remain, index) in remainInfo"
        :key="index"
        :class="remain.boxClass"
        :style="furnishStyles.prizeNameColor.value"
      >
        <div>{{ remain.prizeName }}</div>
        <div>奖品剩余{{ remain.remainCount >= 0 ? remain.remainCount : 0 }}份</div>
      </div>
      <!-- 奖品领取按钮 -->
      <div
        v-for="(prize, index) in prizeButtons"
        :v-click-track="'ljlq'"
        :key="index"
        :class="prize.btnClass"
        :style="checkBtnStatus(prize.data)"
        @click="getPrize(prize.data)"
      />
      <!-- 动态生成的热区按钮 -->
      <div
        class="hotBtn"
        v-for="(item, index) in showData"
        :key="index"
        v-click-track="`btn${index + 1}`"
        :style="item.style"
        @click="toLink(item.url)"
      />
    </div>

    <div class="sku" v-if="skuList.length">
      <img class="sku-list-img" :src="furnish.showSkuBg" alt="">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in skuList" :key="index">
          <div class="sku-text" :style="furnishStyles.priceColor.value">
            <div class="go-sku-btn" @click="gotoSkuPage(item.skuId)"/>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-shop-share">
      <div class="to-shop" :style="furnishStyles.btnToShop.value" @click="toLink(furnish.moreActLink)"/>
    </div>
    <!-- 活动门槛 -->
    <Threshold :showPopup="showLimit" @closeDialog="showLimit = false" :canNotCloseJoin="furnish.canNotCloseJoinPopup" :data="baseInfo?.thresholdResponseList"/>
    <!-- 非会员拦截 -->
    <OpenCard :showPopup="showOpenCard" @closeDialog="showOpenCard = false" :canNotCloseJoin="furnish.canNotCloseJoinPopup"/>
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule" >
      <RulePopup :rule="ruleTest" @close="showRule = false"/>
    </VanPopup>
    <!--我的奖品-->
    <VanPopup teleport="body" v-model:show="showMyPrize" >
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"/>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup
      teleport="body"
      v-model:show="showSaveAddress"
      >
      <SaveAddress
        :addressId="addressId"
        @close="showSaveAddress = false" />
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="isShowConfirmPopup" >
      <GiftConfirm
        :giftInfo="giftItem"
        @close="isShowConfirmPopup = false"
        :multiplePrizeNum="multiplePrizeNum"
        :multiplePrizeCanReceiveNum="multiplePrizeCanReceiveNum"
        @drawSuccess="drawSuccessFun"
      />
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { computed, inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { callShare } from '@/utils/platforms/share';
import { gotoSkuPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import RulePopup from '../components/RulePopup.vue';
import Threshold from '../components/Threshold.vue';
import GiftConfirm from '../components/GiftConfirm.vue';
import OpenCard from '../components/OpenCard.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import dayjs from 'dayjs';
import MyPrize from '../components/MyPrize.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};
// 是否是第一次购买
const isFirstBuy = ref(false);
// 店铺名称
const shopName = ref(baseInfo.shopName);
// 门槛弹窗
const showOpenCard = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 我的奖品弹窗
const showMyPrize = ref(false);
// 单次领取弹窗
const isShowConfirmPopup = ref(false);
// 曝光商品
const showGoods = ref(false);

// 奖品列表
const multiplePrizeList = ref([{
  // 奖品状态 0 不满足条件 1 未领取  2 领取成功  3 剩余份数不足 4 领取数量已经达到上限
  status: 0,
  prizeId: 0,
  prizeImg: '',
  prizeName: '',
  prizeType: 0,
  remainCount: 0,
  sendTotalCount: 0,
  area: 3,
}, {
  // 奖品状态 0 不满足条件 1 未领取  2 领取成功  3 剩余份数不足 4 领取数量已经达到上限
  status: 0,
  prizeId: 0,
  prizeImg: '',
  prizeName: '',
  prizeType: 0,
  remainCount: 0,
  sendTotalCount: 0,
  area: 3,
}]);
const giftItem = ref({});
// 活动商品列表
type Sku = {
  skuId: string,
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuList = ref<Sku[]>([]);

// 计算属性用于渲染按钮
const prizeButtons = computed(() => [
  { btnClass: 'getLeftBtn', data: multiplePrizeList.value[0] },
  { btnClass: 'getRightBtn', data: multiplePrizeList.value[1] },
]);

// 计算属性用于渲染剩余数量/奖品信息
const remainInfo = computed(() => [
  {
    boxClass: 'remainBox2',
    prizeName: multiplePrizeList.value[0].prizeName,
    remainCount: multiplePrizeList.value[0].remainCount,
  },
  {
    boxClass: 'remainBox3',
    prizeName: multiplePrizeList.value[1].prizeName,
    remainCount: multiplePrizeList.value[1].remainCount,
  },
]);

// 展示门槛显示弹框
const showLimit = ref(false);

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const userReceiveRecordId = ref('');

// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
const showMyPrizePop = () => {
  showMyPrize.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  id: 1,
  prizeName: '',
  prizeImg: '',
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  exchangeImg: '',
});
// 展示卡密
const showCardNum = (distribute: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = distribute[item];
  });
  copyCardPopup.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);
const multiplePrizeCanReceiveNum = ref(0);
const multiplePrizeNum = ref(0);

// 主接口获取信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/90077/activity');
    isFirstBuy.value = data.isFirstBuy;
    if (!isFirstBuy.value) {
      if (furnish.isShowJump && furnish.jumpUrl) {
        window.location.href = furnish.jumpUrl;
        return;
      }
      showLimit.value = true;
    }
    multiplePrizeList.value = data.multiplePrize;
    multiplePrizeList.value[0].area = 3;
    multiplePrizeList.value[1].area = 3;
    multiplePrizeCanReceiveNum.value = data.multiplePrizeCanReceiveNum;
    multiplePrizeNum.value = data.multiplePrizeNum;
  } catch (error) {
    console.error(error);
  }
};
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/90077/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error) {
    console.error(error);
  }
};
// 热区数据
const showData = ref([]);
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showData.value = furnish.hotZoneList;
    showData.value.forEach((item: any) => {
      const style: any = {};
      style.width = `${(item.width * 2) / 100}rem`;
      style.height = `${(item.height * 2) / 100}rem`;
      style.position = 'absolute';
      style.top = `${(item.top * 2) / 100}rem`;
      style.left = `${(item.left * 2) / 100}rem`;
      item.style = style;
    });
    console.log(showData.value, 'hotZoneList');
    console.log(showData.value[0].style);
    await Promise.all([getActivityInfo(), getSkuList()]);
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
      showOpenCard.value = true;
      console.log('非会员');
      return;
    }
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 1
      || baseInfo.thresholdResponseList[0]?.thresholdCode === 2
      || baseInfo.thresholdResponseList[0]?.thresholdCode === 1501) {
      showLimit.value = true;
    }
    return;
  } catch (error) {
    console.log(error);
  }
};

// 领奖接口
const receivePrize = async (val) => {
  const { data } = await httpRequest.post('/90077/receivePrize', { prizeId: val.prizeId });
  isShowConfirmPopup.value = false;
  init();
  return data;
};

// 领奖接口
const receiveCoupon = async (val) => {
  const { data } = await httpRequest.post('/90077/receiveCoupon', { prizeId: val.prizeId });
  init();
  return data;
};

// 校验领奖资格
const checkQualification = (val) => {
  if (!isStart.value) {
    showToast('活动未开始~');
    return false;
  }
  if (isEnd.value) {
    showToast('活动已结束~');
    return false;
  }
  if (val.status === 0) {
    showToast('您不符合活动条件，无法领取~');
    return false;
  }
  if (val.status === 2) {
    showToast('您已领取过该奖品~');
    return false;
  }
  if (val.status === 3) {
    showToast('手慢了，奖品已领光~');
    return false;
  }
  if (val.status === 4) {
    showToast('领取数量已经达到上限~');
    return false;
  }
  return val.status === 1;
};

// 领取各个奖品判断
const getPrize = async (prize: any) => {
  try {
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 1501) {
      showLimit.value = true;
      return;
    }
    // 校验资格
    if (!checkQualification(prize)) {
      return;
    }
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    if (prize.area === 1) {
      if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
        showToast('非会员无法领取~');
        return;
      }
      const data = await receiveCoupon(prize);
      if (data.distribute.status === 1) {
        closeToast();
        showToast('领取成功~');
      } else if (data.distribute.status === 0) {
        showToast('您与大奖擦肩而过~');
      }
    }
    if (prize.area === 2) {
      const data = await receivePrize(prize);
      if (data.distribute.status === 1) {
        closeToast();
        showToast('领取成功~');
        // 实物
        if (prize.prizeType === 3) {
          addressId.value = data.addressId;
          showSaveAddress.value = true;
          return;
        }
        // 礼品卡
        if (prize.prizeType === 7) {
          const card = {
            id: data.distribute.id,
            prizeName: data.distribute.prizeName,
            prizeImg: data.distribute.prizeImg,
            cardDesc: data.distribute.result.cardDesc,
            cardNumber: data.distribute.result.cardNumber,
            cardPassword: data.distribute.result.cardPassword,
            exchangeImg: data.distribute.exchangeImg,
          };
          showCardNum(card);
          return;
        }
      } else if (data.distribute.status === 0) {
        showToast('您与大奖擦肩而过~');
      }
    }
    if (prize.area === 3 && multiplePrizeCanReceiveNum.value < multiplePrizeNum.value) {
      giftItem.value = prize;
      closeToast();
      isShowConfirmPopup.value = true;
    }
    if (prize.area === 3 && multiplePrizeCanReceiveNum.value === multiplePrizeNum.value) {
      const data = await receivePrize(prize);
      if (data.distribute.status === 1) {
        closeToast();
        showToast('领取成功~');
      } else if (data.distribute.status === 0) {
        showToast('您与大奖擦肩而过~');
      }
    }
  } catch (error) {
    closeToast();
    showToast(error);
    console.log(error);
  }
};

// 确认领取弹窗的回调
const drawSuccessFun = async () => {
  isShowConfirmPopup.value = false;
  await init();
};

const showSavePhone = (id: string, desc: string) => {
  userReceiveRecordId.value = id;
  planDesc.value = desc;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

const toLink = (item) => {
  window.location.href = `${item}`;
};

const checkBtnStatus = (obj) => {
  // 高亮
  if (obj.status === 1) {
    return furnishStyles.getPrizeBtn.value;
  }
  // 置灰
  if (obj.status === 0 || obj.status === 2 || obj.status === 3 || obj.status === 4) {
    return furnishStyles.getPrizeGrayBtn.value;
  }
  return '';
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
const toInit = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  await init();
  closeToast();
};
toInit();

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};

</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  //margin-bottom: 9rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 0.43rem;
    height: 1.42rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat:no-repeat;
    background-size: 100%;
  }
}

.hotZoneBox{
  width: 7.5rem;
  margin: 0 auto;
  position: relative;
}
.hotZone{
  width: 100%;
  margin: 0 0 0.2rem 0;
}

//.hotBtn {
//  background-color: #000;
//}

.getCouponBtn {
  position: absolute;
  bottom: 10.7rem;
  left: 3.9rem;
  width: 1.81rem;
  height: 0.51rem;
  background-repeat:no-repeat;
  background-size: 100%;
}

.getCardBtn{
  position: absolute;
  bottom: 8.12rem;
  left: 3rem;
  width: 1.43rem;
  height: 0.4rem;
  background-repeat:no-repeat;
  background-size: 100%;
}

.getLeftBtn{
  position: absolute;
  bottom: 5.54rem;
  left: 1.35rem;
  width: 1.43rem;
  height: 0.4rem;
  background-repeat:no-repeat;
  background-size: 100%;
}

.getRightBtn{
  position: absolute;
  bottom: 5.54rem;
  right: 1.35rem;
  width: 1.43rem;
  height: 0.4rem;
  background-repeat:no-repeat;
  background-size: 100%;
}

.remainBox1 {
  position: absolute;
  bottom: 8.7rem;
  left: 4.5rem;
  font-size: 0.18rem;
}
.remainBox2 {
  position: absolute;
  bottom: 6.15rem;
  left: 0.85rem;
  font-size: 0.18rem;
  //background-color: #000;
  width: 2.44rem;
  text-align: center;
  div{
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }
}
.remainBox3 {
  position: absolute;
  bottom: 6.15rem;
  left: 4.18rem;
  font-size: 0.18rem;
  //background-color: #000;
  width: 2.44rem;
  text-align: center;
  div{
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }
}

.sku{
  width: 7.5rem;
  padding: 0.2rem 0;
  position: relative;
  margin: 0.2rem auto 0.1rem auto;
  .sku-list-img {
    width: 7.5rem;
    height: auto;
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    width: 7.5rem;
    place-content: flex-start space-between;
    padding: 1.18rem 0 0;
    height: auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
  }
  .sku-item{
    width: 3.75rem;
    height: 4.34rem;
    margin: 0 0 0.12rem 0;
    padding-top: 3.35rem;
    overflow: hidden;
    //background-color: rgb(140 42 42 / 55%);
    .sku-text{
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 0.4rem auto 0;
      box-sizing: border-box;
      .go-sku-btn {
        margin-top: 0.23rem;
        width: 1.1rem;
        height: 0.3rem;
        //background-color: #694040;
      }
    }
  }
  .sku-item:nth-child(odd) {
    padding-left: 1.49rem;
  }
  .sku-item:nth-child(even) {
    padding-left: 1.16rem;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share{
  margin: 0 auto;
  .to-shop{
    margin: 0 auto;
    height: 1rem;
    width: 3.5rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.more-btn-all {
  width:6.9rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
