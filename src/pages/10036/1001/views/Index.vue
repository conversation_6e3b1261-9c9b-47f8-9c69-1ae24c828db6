<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg || 'https://img10.360buyimg.com/imgzone/jfs/t1/204090/6/17726/403466/61ae0340Ef37dcf98/088ad25a1d4f9017.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div class="header-btn-all">
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event" v-click-track="btn.clickCode">
            <div>{{ btn.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="countDownClass">
      <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart" />
    </div>
    <div class="prizeClassAll">
      <div class="prizeIntroClass" :style="furnishStyles.prizeIntroBg.value">
        <div class="cartStepContainerLine"></div>

        <div class="contentClass">
          <div class="itemClass">
            <div class="timeDateClass">{{ dayjs(addSkuStartTime).format('MM.DD') }}-{{ dayjs(addSkuEndTime).format('MM.DD') }}</div>
            <div class="stepClass"></div>
            <div class="itemTitleClass">加购商品</div>
          </div>
          <div class="itemClass">
            <div class="timeDateClass">{{ dayjs(priceTime).format('MM.DD HH:mm') }}</div>
            <div class="step2Class"></div>
            <div class="itemTitleClass">抽奖</div>
          </div>
          <div class="itemClass">
            <div class="timeDateClass">获奖者</div>
            <div class="step3Class"></div>
            <div class="itemTitleClass">领取丰厚大奖</div>
          </div>
        </div>
      </div>
      <div class="joinClassAll">
        <div class="logoClass" :style="furnishStyles.joinLogo.value"></div>
        <div class="joinClass" :style="furnishStyles.joinColor.value">
          已有<span class="joinNumClass" :style="furnishStyles.joinNumColor.value">{{ joinNum }}</span
          >人参与活动
        </div>
      </div>
      <div class="allCartBtnAll">
        <div class="btnClass" v-if="priceTime > dayjs().valueOf()" :style="furnishStyles.cartBtn.value" v-threshold-click="addCartAllClick"></div>
        <div class="drawBtnClass" v-else :style="furnishStyles.drawBtn.value" v-threshold-click="drawClick"></div>
      </div>
    </div>
    <PrizeList :prizeList="prizeInfo"></PrizeList>
    <div class="goodsClassAll">
      <div class="titleClass" :style="furnishStyles.skuTitleBg.value"></div>
      <div class="goodListClass">
        <div class="goodItemClass" v-for="(item, index) in orderSkuList" :key="index">
          <div class="unLockClass" v-if="index >= unlockSkuNumber">
            <div class="unlockImageAll1">
              <div class="unlockImageAll">
                <div class="unlockImage"></div>
              </div>
            </div>
            <div class="shareClassAll">
              <div class="shareClass" @click="shareFriendsClick()"></div>
            </div>
          </div>
          <div @click.stop="gotoSkuPage(item.skuId)">
            <div class="imgClass">
              <img :src="item.skuMainPicture" alt="" />
            </div>
            <div class="goodTitleClass">{{ item.skuName }}</div>
            <div class="detailClass" v-if="item.isAddCart === 0">
              <div class="priceClass">{{ item.jdPrice }}</div>
              <div class="btnClass" v-threshold-click="() => addCartClick(item)">加购</div>
            </div>
            <div class="detailGrayClass" v-else-if="item.isAddCart === 1">
              <div class="priceClass">{{ item.jdPrice }}</div>
              <div class="btnGrayClass">已加购</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>

  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showHelpDrawPopup" position="center">
    <HelpDraw v-if="showHelpDrawPopup" :helpResult="helpResult" :helpFailCause="helpFailCause" @close="showHelpDrawPopup = false"></HelpDraw>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType, FormType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import dayjs from 'dayjs';
import CountDown from '../components/CountDown.vue';
import HelpDraw from '../components/helpDraw.vue';
import { addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import PrizeList from '../components/PrizeList.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;

const shopName = ref(baseInfo.shopName);
const unlockSkuNumber = ref(0);
const addSkuStartTime = ref(0);
const addSkuEndTime = ref(0);
const priceTime = ref(0);
const endTime = ref(0);
const startTime = ref(0);
const isStart = ref(true);
const joinNum = ref(0);
const hasChance = ref(0); // 是否有抽奖机会
// 助力结果
const showHelpDrawPopup = ref(false);
const helpFailCause = ref('');
const helpResult = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const showMyPrize = ref(false);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  skuId: number;
  jdPrice: string;
  isAddCart: number;
};

const orderSkuList = ref<Sku[]>([]);
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});
// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  clickCode: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    clickCode: 'hdgz',
    event: () => {
      console.log('活动规则');
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    clickCode: 'wdjp',
    event: () => {
      console.log('我的奖品');
      showMyPrize.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

const myLucky = ref();
// 邀请好友助力
const shareFriendsClick = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
// 抽奖接口
const drawClick = async () => {
  if (hasChance.value <= 0) {
    showToast('您加购的商品数量不满足抽奖所需的数量');
    return;
  }
  if (hasChance.value === 2) {
    // 已经开奖
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post('/10036/userPrizes');
      closeToast();
      if (res.data && res.data.length > 0) {
        if (res.data[0].prizeType === 3) {
          Object.keys(echoData).forEach((key) => {
            echoData[key] = res.data[0][key];
          });
        }
        award.value = {
          prizeType: res.data[0].prizeType,
          prizeName: res.data[0].prizeName,
          showImg: res.data[0].prizeImg,
          result: res.data[0].prizeContent ? JSON.parse(res.data[0].prizeContent) : '',
          activityPrizeId: res.data[0].activityPrizeId ?? '',
          userPrizeId: res.data[0].userPrizeId,
        };
        showAward.value = true;
      } else {
        award.value = {
          prizeType: 0,
          prizeName: '谢谢参与',
          showImg: '',
          result: '',
          activityPrizeId: '',
          userPrizeId: '',
        };
        showAward.value = true;
      }
    } catch (error) {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };
      closeToast();
      showAward.value = true;
    }
  } else if (hasChance.value === 1) {
    // 有开奖机会并且未开过将
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      lzReportClick('kscj');
      const res = await httpRequest.post('/10036/lotteryDraw');
      hasChance.value = 2;
      closeToast();
      if (res.data.prizeType) {
        award.value = {
          prizeType: res.data.prizeType,
          prizeName: res.data.prizeName,
          showImg: res.data.prizeImg,
          result: res.data.result ?? '',
          activityPrizeId: res.data.activityPrizeId ?? '',
          userPrizeId: res.data.userPrizeId,
        };
        showAward.value = true;
      } else {
        award.value = {
          prizeType: 0,
          prizeName: '谢谢参与',
          showImg: '',
          result: '',
          activityPrizeId: '',
          userPrizeId: '',
        };
        showAward.value = true;
      }
    } catch (error) {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };
      hasChance.value = 2;
      closeToast();
      showAward.value = true;
    }
  }
};

// 主接口
const getActivityData = async () => {
  try {
    const { data } = await httpRequest.post('/10036/activity', {
      shareUserId: pathParams.shareId,
    });
    addSkuStartTime.value = data.addSkuStartTime;
    addSkuEndTime.value = data.addSkuEndTime;
    priceTime.value = data.priceTime;
    joinNum.value = data.joinNum;
    unlockSkuNumber.value = data.unlockSkuNumber;
    orderSkuList.value = data.skuInfoList;
    hasChance.value = data.hasChance;
    prizeInfo.splice(0);
    prizeInfo.push(...data.prizeList);
    // showHelpDrawPopup
    if (data.shareFlag) {
      // 是否点分享链接进入活动
      showHelpDrawPopup.value = true;
      helpFailCause.value = data.cause;
      helpResult.value = data.isSuccess;
    } else {
      showHelpDrawPopup.value = false;
    }
    // console.log(data, 'datadata');
  } catch (error) {
    console.error(error);
  }
};

// 一键加购
const addCartAllClick = async () => {
  if (addSkuStartTime.value > dayjs().valueOf() || addSkuEndTime.value < dayjs().valueOf()) {
    showToast('未在加购时间内，无法加购商品');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10036/addSku', {
      skuId: '999',
    });
    if (res.code === 200) {
      showToast('加购成功');
      pathParams.shareId = '';
      addSkuToCart(orderSkuList.value.map((item) => item.skuId));
      await getActivityData();
    } else {
      showToast('加购失败');
    }
  } catch (error) {
    console.error(error);
    showToast(error);
  }
};
// 逐件加购商品
const addCartClick = async (itemData: Sku) => {
  if (addSkuStartTime.value > dayjs().valueOf() || addSkuEndTime.value < dayjs().valueOf()) {
    showToast('未在加购时间内，无法加购商品');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10036/addSku', {
      skuId: itemData.skuId,
    });
    if (res.code === 200) {
      showToast('加购成功');
      pathParams.shareId = '';
      addSkuToCart(itemData.skuId);
      await getActivityData();
    } else {
      showToast('加购失败');
    }
  } catch (error) {
    console.error(error);
    showToast(error);
  }
};

// 活动倒计时
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityData()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
  if (baseInfo.status === 1) {
    const time = baseInfo.startTime - dayjs().valueOf();
    setTimeout(() => {
      window.location.reload();
    }, time);
  }
};
init();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
    width: 100%;
    margin-top: 0.3rem;
    margin-left: 0.3rem;
  }
  .header-btn-all {
    position: absolute;
    top: 0.3rem;
    right: 0;
    z-index: 10;
    .header-btn {
      width: 1.18rem;
      background-size: 100%;
      background-repeat: no-repeat;
      height: 0.44rem;
      margin-bottom: 0.1rem;
      font-size: 0.2rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}
.countDownClass {
  margin-top: 0.3rem;
  width: 100%;
  display: flex;
  justify-content: center;
}
.prizeClassAll {
  display: flex;
  align-items: center;
  width: 100%;
  flex-direction: column;
  margin: 0.3rem auto 0;
  .prizeIntroClass {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/216619/5/6765/149521/61ae040bE44547815/a89c4756f0049005.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 6.9rem;
    height: 3.54rem;
    box-sizing: border-box;
    position: relative;
    padding-top: 1.56rem;
    .cartStepContainerLine {
      width: 5.2rem;
      height: 0.08rem;
      background: rgb(255, 197, 113);
      border-radius: 0.04rem;
      position: absolute;
      left: 50%;
      transform: translate(-50%, 0.45rem);
      z-index: 0;
    }
    .contentClass {
      width: 5.2rem;
      margin: 0 auto;
      border-radius: 0.04rem;
      display: flex;
      justify-content: space-between;
      .itemClass {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .timeDateClass {
          font-size: 0.24rem;
          color: #262626;
          line-height: 0.24rem;
          text-align: center;
        }
        .stepClass {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/138265/27/22547/2779/61ae06e0E11257235/49ae1435707eb4fb.png');
          background-repeat: no-repeat;
          background-size: 100%;
          width: 0.52rem;
          height: 0.52rem;
          margin: 0 auto;
          position: relative;
        }
        .step2Class {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/205456/15/17571/2934/61ae06e0Ea1bc543c/adbb23a655da3d73.png');
          background-repeat: no-repeat;
          background-size: 100%;
          width: 0.52rem;
          height: 0.52rem;
          margin: 0 auto;
          position: relative;
        }
        .step3Class {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/212901/20/6908/2980/61ae06e0E4ab60495/1124268d88c487fa.png');
          background-repeat: no-repeat;
          background-size: 100%;
          width: 0.52rem;
          height: 0.52rem;
          margin: 0 auto;
          position: relative;
        }
        .itemTitleClass {
          font-size: 0.24rem;
          color: #f2270c;
          line-height: 0.24rem;
          text-align: center;
        }
      }
    }
  }
  .joinClassAll {
    display: flex;
    position: relative;
    margin-top: 0.3rem;
    align-items: center;
    justify-content: center;
    .logoClass {
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/222169/3/4948/633/61adbf1eE3fa59e89/b55e8470e07cfc1b.png');
      background-repeat: no-repeat;
      background-size: 100%;
      width: 0.35rem;
      height: 0.3rem;
      margin-right: 0.1rem;
    }
    .joinClass {
      font-size: 0.24rem;
      color: #ffffff;
      .joinNumClass {
        font-size: 0.24rem;
        color: #fff100;
      }
    }
  }
  .allCartBtnAll {
    .btnClass {
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/220340/40/6804/62076/61aed0d0Ef010cc65/4d146c5c4c17f7f6.png');
      background-repeat: no-repeat;
      background-size: 100%;
      width: 5.17rem;
      height: 0.97rem;
      margin: 0.3rem auto;
      position: relative;
      z-index: 3;
      display: block;
    }
    .drawBtnClass {
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/162008/36/28230/49167/61aecbcdEf1db3ae8/4b329c1ee17d4595.png');
      background-repeat: no-repeat;
      background-size: 100%;
      width: 5.17rem;
      height: 0.97rem;
      margin: 0.3rem auto;
      position: relative;
      z-index: 3;
      display: block;
    }
  }
}
.goodsClassAll {
  .titleClass {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/198727/40/18336/5096/619b80b9Eafb4de86/6e8012f498dd80d1.png');
    background-repeat: no-repeat;
    background-size: 100%;
    width: 2.82rem;
    height: 0.4rem;
    margin-left: 50%;
    transform: translate(-50%);
    margin-bottom: 0.3rem;
  }
  .goodListClass {
    margin: 0 auto;
    display: flex;
    place-content: flex-start space-between;
    flex-wrap: wrap;
    padding: 0 0.3rem;
    .goodItemClass {
      width: calc(50% - 0.1rem);
      margin-bottom: 0.2rem;
      background: #ffffff;
      border-radius: 0.2rem;
      overflow: hidden;
      padding-bottom: 0.2rem;
      position: relative;
      .imgClass {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
        img {
          width: 100%;
        }
      }
      .goodTitleClass {
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        font-size: 0.3rem;
        color: #262626;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.43rem;
        box-sizing: border-box;
      }
      .detailClass {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/214288/26/6921/1028/61aec77aEc08dad40/a0afc75d114d7956.png');
        background-repeat: no-repeat;
        background-size: 100%;
        width: 3rem;
        height: 0.62rem;
        display: flex;
        margin: 0 auto;
        .priceClass {
          width: 1.95rem;
          height: 0.62rem;
          line-height: 0.62rem;
          font-size: 0.3rem;
          color: #ff275a;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .btnClass {
          font-size: 0.3rem;
          width: 1.05rem;
          height: 0.62rem;
          line-height: 0.62rem;
          color: #ffffff;
          text-align: center;
        }
      }
      .detailGrayClass {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/158280/12/22544/1019/61aec61eE400b5bfe/6d9039133c2bb139.png');
        background-repeat: no-repeat;
        background-size: 100%;
        width: 3rem;
        height: 0.62rem;
        display: flex;
        margin: 0 auto;
        .priceClass {
          width: 1.95rem;
          height: 0.62rem;
          line-height: 0.62rem;
          font-size: 0.3rem;
          color: #ff275a;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .btnGrayClass {
          font-size: 0.3rem;
          width: 1.05rem;
          height: 0.62rem;
          line-height: 0.62rem;
          color: #ffffff;
          text-align: center;
        }
      }
      .unLockClass {
        position: absolute;
        width: 100%;
        height: 100%;
        right: 0;
        bottom: 0;
        z-index: 3;
        background: rgba(0, 0, 0, 0.4);
        .unlockImageAll1 {
          width: 3.4rem;
          height: 3.4rem;
          display: flex;
          justify-content: center;
          align-items: center;
          .unlockImageAll {
            width: 1.6rem;
            height: 1.6rem;
            background: #000000;
            opacity: 0.4;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            .unlockImage {
              background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/137944/8/22945/767/61aec6edE5153c4cd/1b9d8267e72a3169.png');
              background-repeat: no-repeat;
              background-size: 100%;
              width: 0.442rem;
              height: 0.53rem;
            }
          }
        }
        .shareClassAll {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 0.5rem;
          .shareClass {
            background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/173509/10/21564/13130/61aecf05E901527c7/706f77f1dde4c468.png');
            background-repeat: no-repeat;
            background-size: 100%;
            width: 2.1rem;
            height: 0.58rem;
          }
        }
      }
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
