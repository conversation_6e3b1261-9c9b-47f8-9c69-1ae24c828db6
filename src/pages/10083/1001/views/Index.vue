<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/110266/23/31842/65255/62fdf8f8E81efc796/2d3904d53fc091f8.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">{{ shopName }}</div>
        <div class="header-btn-all">
          <div class="header-btn"  :style="furnishStyles.headerBtn.value"  v-click-track="'hdgz'" @click="showRulePopup()"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-click-track="'wdjp'" @click="showMyPrize = true"><div>我的奖品</div></div>
        </div>
      </div>
    </div>
    <div>
      <van-notice-bar class="fd-top-tip" scrollable text="重要提示：每人只能以团长或团员的身份成功参与一次（开团数量不限制），每组奖励每人仅可获得一份"/>
    </div>
    <div class="prizeDivAll">
      <div class="prizeTitleDiv" :style="{'backgroundImage':'url(' + furnish.prizeTitle + ')'}"></div>
      <div class="prizeListDiv">
        <div class="prizeItemDiv" :style="{'backgroundImage':'url(' + furnish.prizeItemBg + ')'}" v-for="(item,index) in taskList" :key="index">
          <div class="captainDivAll">
            <div class="captainLeftDiv">
              <div class="priceInfoDiv"><div>￥<span>{{item.prizeList[0].couponDiscount}}</span></div></div>
              <div class="captainMessageDiv">
                <div class="prizeName">优惠券</div>
                <div class="discountDiv">满{{item.prizeList[0].couponQuota}}元使用</div>
                <div class="sharePeopleDiv">邀请{{item.peopleNum}}人可成团</div>
                <div class="progressDivAll">
                  <van-progress
                    class="van-progress"
                    :percentage="item.prizeList[0].percent * 100"
                    :stroke-width="10"
                    track-color="#ffb3a9"
                    :show-pivot="false"
                    pivot-color="#F2270C"
                    color="#f10400"
                    text-color="#fff"
                  />
                  <div class="progressTextDiv">已抢{{item.prizeList[0].percent * 100}}%</div>
                </div>
              </div>
            </div>
            <div class="captainRightDiv">
              <div class="shareFriendsDiv" v-if="item.status !== 0 || item.memberStatus !== 0" @click="showShareRecordClick(item)">团队组员&gt;</div>
              <div class="btnDivAll">
                <div class="btnDiv" v-if="item.status === 0 && item.memberStatus === 0" :style="furnishStyles.prizeBtn.value" @click="startTeamClick(item)">立即成团</div>
                <div class="btnDiv" v-else-if="item.status === 1 || item.memberStatus === 1" :style="furnishStyles.prizeBtn.value" @click="shareActClick(item)">邀请好友</div>
                <div class="btnDiv" v-else-if="item.status === 2 || item.memberStatus === 2" :style="furnishStyles.prizeBtn.value" @click="toUseCouClick()">去使用</div>
              </div>
              <div class="captainLimitDiv">
                <div v-if="item.status !== 0" class="captainImgDiv"><img :src="item.avatar" alt="" /></div>
                <div v-if="item.status === 0 && item.memberStatus === 0">不限制</div>
                <div class="limitOrange" v-else-if="item.status === 1 || item.memberStatus === 1">拼券中...</div>
                <div class="limitGreen" v-else-if="item.status === 2 || item.memberStatus === 2">拼券结束</div>
              </div>
            </div>
          </div>
          <div class="memberDivAll">
            <div class="memberLeftDiv">
              <div class="memberLeftLeftDiv">
                <div class="priceInfoDiv">
                  <div>￥<span>{{item.prizeList[1].couponDiscount}}</span></div>
                </div>
                <div class="memberLogoDiv">
                  <img src="//img10.360buyimg.com/imgzone/jfs/t1/38943/13/20873/4098/638ffd92E6d4e7598/1de57697865ffaba.png" alt="" />
                </div>
              </div>
              <div class="memberMessageDiv">
                <div class="memberPrizeName">优惠券</div>
                <div class="memberDiscountDiv">满{{item.prizeList[1].couponQuota}}元使用</div>
              </div>
            </div>
            <div class="memberRightDiv">
              <div v-if="item.memberStatus === 0 && item.status === 0">不限制</div>
              <div class="limitOrange" v-else-if="item.memberStatus === 1 || item.status === 1">团员组队中...</div>
              <div class="limitGreen" v-else-if="item.memberStatus === 2 || item.status === 2">团员拼券结束</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sku" v-if="skuList && skuList.length > 0">
      <img class="title-img" :src="furnish.winnersBg" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" v-click-track="'ljgm'"  @click="gotoSkuPage(item.skuId)">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns">
            <div class="price">￥{{ item.jdPrice }}</div>
            <div class="to-bug">抢购</div>
          </div>
        </div>
      </div>
      <div class="more-btn-all">
        <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @savePhone="showSavePhone" @showCardNum="showCardNum"></MyPrize>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="isShowTeamPop" position="center">
      <TeamPop :itemDataPeopleNum="itemDataPeopleNum" :groupId="groupId" :taskId="taskId" @close="isShowTeamPop = false"></TeamPop>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="isShowCreateCaptionPop" position="bottom">
      <CreateCaptainPop :teamDataArr="teamDataArr" :itemDataPeopleNum="itemDataPeopleNum"  :taskId="taskId" @close="isShowCreateCaptionPop = false" @createPopClick="createPopClick"></CreateCaptainPop>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showShareRecord" position="bottom">
      <ShareFriends v-if="showShareRecord" :taskId="taskId" :groupId="groupId" @close="showShareRecord = false"></ShareFriends>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="isShowHelpPop" position="center">
      <HelpPop :type="1" :itemDataPeopleNum="itemDataPeopleNum" @close="isShowHelpPop = false" @btnClick="btnClick"></HelpPop>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup @close="showAward = false" ></AwardPopup>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo, PrizeTypeName } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { PrizeInfo } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import Threshold2 from '@/components/Threshold2/index.vue';
import useThreshold from '@/hooks/useThreshold';
import TeamPop from '../components/Team.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import CreateCaptainPop from '../components/CreateCaptain.vue';
import ShareFriends from '../components/ShareFriends.vue';
import HelpPop from '../components/Help.vue';

const isShowCreateCaptionPop = ref(false);
const isShowTeamPop = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const shopName = ref(baseInfo.shopName);
const prizeList = ref<PrizeInfo[]>([]); // 奖品列表
const taskList = ref<PrizeInfo[]>(prizeInfo);
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
const isShowHelpPop = ref(false);
// 展示门槛显示弹框
const showLimit = ref(false);
// showLimit.value = useThreshold({
//   thresholdList: baseInfo.thresholdResponseList,
// });
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const showMyPrize = ref(false);
const showShareRecord = ref(false);
const itemDataPeopleNum = ref(0);
// 立即开团
const groupId = ref(); // 分享时候的groupId
const taskId = ref(); // 分享时候的taskId
const shareUrlOld = ref('');
const peopleNum1 = ref(0);
// 主接口
const getActivityData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10083/activity');
    closeToast();
    taskList.value = data;
    const locaIdsStr = localStorage.getItem(`lbsq${baseInfo.activityId}`)?.split(',') || [];
    console.log('locaIdsStr', locaIdsStr);
    const gropIds = data
      .filter((item: any) => item.memberStatus === 2)
      .map((item: any) => item.groupId.toString());
    // console.log('gropIds', gropIds);
    showAward.value = gropIds.some((item:string) => !locaIdsStr.includes(item));
    // console.log('isShow', showAward.value);
    localStorage.setItem(`lbsq${baseInfo.activityId}`, gropIds);
    // console.log(data, '主接口数据=======');
    if (pathParams.shareId && pathParams.groupId) {
      data.forEach((e) => {
        if (e.taskId === pathParams.taskId) {
          peopleNum1.value = e.peopleNum;
        }
      });
    }
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};
const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  console.log(`${shareUrlOld.value}&groupId=${groupId.value}&taskId=${taskId.value}`, '分享链接');
  callShare({
    shareUrl: `${shareUrlOld.value}&groupId=${groupId.value}&taskId=${taskId.value}`,
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
const shareActClick = async (itemData) => {
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  groupId.value = itemData.groupId;
  taskId.value = itemData.taskId;
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  shareUrlOld.value = `${window.location.href}&shareId=${itemData.shareId ? itemData.shareId : shareConfig.shareId}`;
  // console.log(shareUrlOld.value, '被邀请者分享活动链接3');
  await shareAct(itemData);
};
// 去使用优惠券
const toUseCouClick = () => {
  gotoShopPage(baseInfo.shopId);
};
const sureStartTeamClick = async (itemData) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10083/createTeam', {
      taskId: itemData.taskId,
    });
    closeToast();
    itemDataPeopleNum.value = itemData.peopleNum;
    groupId.value = data;
    isShowTeamPop.value = true;
    await getActivityData();
  } catch (e) {
    showToast({
      message: e.message,
      duration: 2000,
      onClose: (() => {
        getActivityData();
      }),
    });
  }
};
const teamDataArr = ref([]);
const startTeamClick = async (itemData) => {
  console.log(itemData, '立即开团');
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  taskId.value = itemData.taskId;
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10083/jumpQueueDetail', {
      taskId: itemData.taskId,
    });
    closeToast();
    if (data.length > 0) {
      teamDataArr.value = data;
      itemDataPeopleNum.value = itemData.peopleNum;
      isShowCreateCaptionPop.value = true;
    } else {
      await sureStartTeamClick(itemData);
    }
  } catch (e) {
    showToast(e.message);
  }
};

// 同一个优惠券有多个团的时候立即开团
const createPopClick = async (datas) => {
  if (baseInfo.thresholdResponseList.length) {
    isShowHelpPop.value = false;
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  isShowCreateCaptionPop.value = false;
  // isShowTeamPop.value = true;
  await getActivityData();
};
const getExposureData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10083/getExposureSku', {
      pageNum: pageNum.value,
      pageSize: 10,
      type: 0,
      x: 'exposure',
    });
    closeToast();
    if (res.data.records && res.data.records.length > 0) {
      skuList.value.push(...res.data.records);
    }
    pagesAll.value = res.data.pages;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
// 曝光商品加载更多
const loadMore = async () => {
  pageNum.value++;
  await getExposureData();
};
// 好友帮忙助力
const doTask = async () => {
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10083/doTask/doTask', {
      shareId: pathParams.shareId,
      groupId: pathParams.groupId,
    });
    showToast('加入团队成功');
    const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
    const urlObj = new URL(window.location.href);
    const searchParams = new URLSearchParams(urlObj.search);
    // 移除shareId参数
    searchParams.delete('shareId');
    searchParams.delete('groupId');
    searchParams.delete('taskId');
    // 构造新的URL，不包含shareId参数
    const newUrl = `${urlObj.origin + urlObj.pathname}?${searchParams.toString()}`;
    shareUrlOld.value = `${newUrl}`;
    console.log(shareUrlOld.value, '被邀请者分享活动链接1');
    await getActivityData();
  } catch (e) {
    showToast(e.message);
  }
};

// 助力者选择成为队长还是队员
const btnClick = async (data) => {
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  if (data === 'joinTeam') {
    await doTask();
  } else if (data === 'createCaptain') {
    const itemDataa = {
      taskId: pathParams.taskId,
      peopleNum: peopleNum1.value,
    };
    await sureStartTeamClick(itemDataa);
  }
  isShowHelpPop.value = false;
};
// 助力好友
const showShareRecordClick = (itemData) => {
  taskId.value = itemData.taskId;
  groupId.value = itemData.groupId;
  showShareRecord.value = true;
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    await Promise.all([getActivityData(), getExposureData()]);
    if (!baseInfo.thresholdResponseList || baseInfo.thresholdResponseList.length <= 0) {
      if (pathParams.shareId && pathParams.groupId) {
        // await doTask();
        isShowHelpPop.value = true;
      }
    }
    // closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color:#f2f2f2
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }
  .header-btn-all{
    .header-btn {
      width: 1.28rem;
      height:0.49rem;
      margin-bottom: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      text-align: center;
      border-radius: 0.22rem;
      border: 0.01rem;
      border-style: solid;
      font-size: 0.2rem;
    }
  }
}
.fd-top-tip{
  background: transparent;
  color:#ffffff;
  font-size: 0.24rem;
  line-height: 0.48rem;
  margin-top: 0.1rem;
}
.prizeDivAll{
  .prizeTitleDiv{
    width: 2.03rem;
    height: 0.34rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/182569/12/24349/3713/629dc0f4Ef273348e/35f98e943d3accdb.png);
    background-size: 100%;
    background-repeat: no-repeat;
    //margin-left: 50%;
    transform: translate(-50%);
    margin: 0.2rem 0 0.28rem 50%;;
  }
  .prizeListDiv{
    display: flex;
    flex-direction: column;
    //justify-content: center;
    align-items: center;
    .prizeItemDiv{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/64198/7/24229/11390/638ffd92E10e941a1/65bae1a04577e9a0.png);
      background-repeat: no-repeat;
      background-size: 100%;
      width: 6.9rem;
      height: 3.27rem;
      margin: 8px auto 0px;
      display: flex;
      flex-direction: column;
      position: relative;
      .captainDivAll{
        width: 100%;
        display: flex;
        justify-content: space-between;
        height: 1.82rem;
        position: relative;
        margin-top: 0.25rem;
        .captainLeftDiv{
          display: flex;
          flex-direction: row;
          position: relative;
          padding-left: 0.3rem;
          .priceInfoDiv{
            font-size: 0.3rem;
            color: #f2270c;
            width: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            margin-top: 0.3rem;
            justify-content: center;
            div{
              display: flex;
              align-items: flex-end;
              span{
                font-size: 0.5rem;
                line-height: 0.5rem;
                font-weight: bold;
                color: #f2270c;
              }
            }

          }
          .captainMessageDiv{
            margin-left: 0.3rem;
            display: flex;
            flex-direction: column;
            text-align: initial;
            margin-top: 0.24rem;
            .prizeName{
              font-size: 0.3rem;
              color: #262626;
              font-weight: bold;
              line-height: 0.3rem;
            }
            .discountDiv{
              font-size: 0.24rem;
              color: #262626;
              line-height: 0.24rem;
              margin-top: 0.08rem;
              margin-bottom: 0.18rem;
            }
            .sharePeopleDiv{
              font-size: 0.24rem;
              color: #f2270c;
              line-height: 0.24rem;
              font-weight: bold;
            }
            .progressDivAll{
              display: flex;
              align-items: center;
              margin-top: 0.12rem;

              .van-progress{
                width: 1.2rem;
                background: rgb(255, 179, 169);
                border-radius: 0.2rem;
                //width: 100%;
                border: 2px solid #ffb3a9;
                display: flex;
                justify-content: center;
                align-items: center;
              }
              //.van-progress__pivot{
              //  height: 100%;
              //  background-color: #F2270C;
              //  border-radius: 4px;
              //  min-width: auto !important;
              //}
              .progressTextDiv{
                margin-left: 0.23rem;
                color: rgb(153, 153, 153);
                font-size: 0.2rem;
              }
            }
          }
        }
        .captainRightDiv{
          margin-right: 0.62rem;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          .shareFriendsDiv{
            font-size: 0.24rem;
            line-height: 0.24rem;
            color: #3399ff;
          }
          .btnDivAll{
            margin: 0.18rem 0 0.14rem 0;
            .btnDiv{
              padding: 0.2rem 0rem;
              color: #ffffff;
              background-color: linear-gradient(to right, #f2270c, #fe621f);
              border-radius: 0.34rem;
              font-size: 0.24rem;
              line-height: 0.24rem;
              width: 1.5rem;
              text-align: center;
            }
          }
          .captainLimitDiv{
            font-size: 0.2rem;
            font-weight: 400;
            color: #f2270c;
            display: flex;
            align-items: center;
            .captainImgDiv{
              width: 0.4rem;
              height: 0.4rem;
              border-radius: 50%;
              border: 0.01rem solid #ffffff;
              margin-right:0.08rem;
              img{
                border-radius: 50%;
                width:100%;
              }
            }
          }
        }
      }
      .memberDivAll{
        display: flex;
        justify-content: space-between;
        height: 0.92rem;
        position: relative;
        margin-top: 0.05rem;
        .memberLeftDiv{
          display: flex;
          flex-direction: row;
          position: relative;
          padding: 0.17rem 0 0.15rem 0.3rem;
          .memberLeftLeftDiv{
            display: flex;
            flex-direction: column;
            align-items: center;
            .priceInfoDiv{
              display: flex;
              justify-content: center;
              font-size: 0.2rem;
              color: #f2270c;
              width: 1.5rem;
              font-weight: bold;
              div{
                display: flex;
                align-items: flex-end;
                span{
                  font-size: 0.36rem;
                  line-height: 0.36rem;
                  font-weight: bold;
                  color: #f2270c;
                }
              }
            }
            .memberLogoDiv{
              width: 1rem;
              height: 0.27rem;
              img{
                width:100%;
              }
            }
          }
          .memberMessageDiv{
            margin-left: 0.3rem;
            display: flex;
            flex-direction: column;
            text-align: initial;
            .memberPrizeName{
              font-size: 0.3rem;
              color: #262626;
              font-weight: bold;
              line-height: 0.3rem;
            }
            .memberDiscountDiv{
              font-size: 0.24rem;
              color: #262626;
              line-height: 0.24rem;
              margin-top: 0.08rem;
              margin-bottom: 0.18rem;
            }
          }
        }
        .memberRightDiv{
          margin-right: 0.62rem;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 0.2rem;
          font-weight: 400;
          color: #f2270c;
          display: flex;
          min-width: 1.5rem;
          font-size: 0.2rem;
          font-weight: 400;
          color: #f2270c;
        }
      }
      .limitOrange{
        font-size: 0.24rem;
        line-height: 0.24rem;
        color: rgb(255, 153, 0);
      }
      .limitGreen{
        font-size: 0.24rem;
        line-height: 0.24rem;
        color: rgb(0, 187, 102);
      }
    }
  }
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  margin-top:0.2rem;
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .more-btn-all {
    width:6.9rem;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 0.27rem;
        color: #262626;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.2rem 0;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug {
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

</style>
