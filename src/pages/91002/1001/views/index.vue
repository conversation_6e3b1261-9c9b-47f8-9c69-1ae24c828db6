<template>
  <div class="bg">
    <div v-if="!isBUser">
      <div class="header-kv">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/135716/40/49618/96738/6719eb2aFdf36b300/de1316a522073cc6.png" alt="" class="kv-img" />
        <div class="topPrizeNameDivAll">限量送{{prizeName}}</div>
        <div class="header-content">
          <div class="shop-name"></div>
          <div class="header-btn-all">
            <div class="header-btn ruleBtn" v-click-track="'hdgz'" @click="showRulePopup"><div>活动规则</div></div>
            <div class="header-btn myPrizeBtn" v-click-track="'wdjp'" @click="showMyPrizePop=true"><div>我的奖品</div></div>
          </div>
        </div>
      </div>
      <div class="shopMessageDivAll">
        <div class="leftDiv">
          <div class="shopLogo" :style="{'backgroundImage':'url(' + baseInfo.shopLogo + ')'}"></div>
          <div class="shopNameDiv">{{baseInfo.shopName}}</div>
        </div>
        <div class="rightDiv" @click="gotoShopPage(baseInfo.shopId)"></div>
      </div>
      <div class="prizeDivAll">
        <div class="prizeTitleDiv"></div>
        <div class="prizeDiv">
          <div class="topDiv">
            <div class="prizeMessDiv">
              <div class="prizeImageDiv">
                <img :src="prizeImg" alt="" />
              </div>
              <div class="prizeDetailDiv">
                <div class="prizeNameDiv">
                  <div v-html="prizeNameTwo"></div>
                </div>
                <div class="prizeRestDiv">奖品剩余：{{prizeNum}}份</div>
              </div>
              <div class="joinDiv" @click="joinClick()" v-if="status === 2 && prizeNum > 0">立即报名</div>
              <div class="joinDivGray" v-else>立即报名</div>
            </div>
          </div>
          <div class="bottomDiv">
            <div class="textDiv">
              <span style="color:#ff555d">注：</span>
              点击“立即报名“按钮成功填写地址提交后视为锁定领奖资格，需在活动结束前完成订单确认收货。实物奖品将于信息填写后次月开始陆续发放。若中途取消订单或发生退换货等情况则无法领取活动奖品。
            </div>
          </div>
        </div>
      </div>
      <div class="specialPerDivAll">
        <div class="specialPerTitle"></div>
        <div class="specialPerConDiv" @click="specialPerClick()"></div>
      </div>
      <div class="skuDivAll" v-if="skuList.length > 0">
        <div class="skuTitleDiv"></div>
        <div class="skuListDiv">
          <div class="skuItemDiv" v-for="(item,index) in skuList" :key="index" v-click-track="'ljgm'"  @click="gotoSkuPage(item.skuId)">
            <div class="skuImgDiv">
              <img :src="item.skuMainPicture" alt="" />
            </div>
            <div class="skuNameDiv">{{ item.skuName }}</div>
            <div class="skuPriceDiv">￥{{item.jdPrice}}</div>
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
        </div>
      </div>
      <div>
        <!-- 活动门槛（非会员） -->
        <VanPopup teleport="body" v-model:show="showLimitVip" position="center">
          <ThresholdKaSaDi v-model:show="showLimitVip" :data="baseInfo?.thresholdResponseList" @close="showLimitVip=false" />
        </VanPopup>
        <!-- 通用活动门槛（活动未开始/已结束） -->
        <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
        <VanPopup teleport="body" v-model:show="showRulePop" position="center">
          <RulePop v-if="showRulePop" @close="showRulePop = false" :rule="ruleText"></RulePop>
        </VanPopup>
        <VanPopup teleport="body" v-model:show="showMyPrizePop" position="center">
          <MyPrizePop v-if="showMyPrizePop" @close="showMyPrizePop = false"></MyPrizePop>
        </VanPopup>
        <VanPopup teleport="body" v-model:show="showAddress" position="center">
          <Address v-if="showAddress" :addressData="addressData" @close="showAddressClose"></Address>
        </VanPopup>
        <VanPopup teleport="body" v-model:show="showJoinResult" position="center">
          <JoinResult v-if="showJoinResult" :joinResultData="joinResultData" @close="showJoinResultClose"></JoinResult>
        </VanPopup>
      </div>
    </div>
    <div v-else class="bUserClass">
      <div class="buerTitleDiv"></div>
      <div class="buercontentDiv"></div>
      <div class="goToShopClass" @click="gotoShopPage(baseInfo.shopId)"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import { inject, ref } from 'vue';
import { httpRequest } from '@/utils/service';
import RulePop from '../components/Rule.vue';
import MyPrizePop from '../components/MyPrize.vue';
import ThresholdKaSaDi from '../components/ThresholdKaSaDi.vue';
import JoinResult from '../components/JoinResult.vue';
import Address from '../components/Address.vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Threshold2 from '@/components/Threshold2/index.vue';
import useThreshold from '@/hooks/useThreshold';
import '../Threshold2/KaSaDiStyle.scss';
import constant from '@/utils/constant';

const baseInfo = inject('baseInfo') as BaseInfo;
const ruleText = ref(''); // 活动规则
const showRulePop = ref(false); // 石佛显示活动规则弹窗
const showMyPrizePop = ref(false); // 是否显示我的奖品弹窗
const prizeImg = ref(''); // 奖品图片
const prizeName = ref(''); // 奖品名称
const prizeNameTwo = ref('');
const prizeNum = ref(''); // 奖品剩余数量
const prizeType = ref(0); // 奖品类型
const status = ref(0); // 1已报名 2未报名
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
const isBUser = ref(false); // 是否是黑名单 true 是，否，不是
const showAddress = ref(false); // 是否显示填写地址弹窗
const addressData = ref({
  realName: '',
  mobile: '',
  province: '',
  county: '',
  city: '',
  address: '',
}); // 地址信息
const showJoinResult = ref(false); // 是否显示报名结果弹窗
const joinResultData = ref({
  type: 1, // 1 报名成功 2 订单不符合 3 订单校验弹窗 4 订单校验报名不符合
}); // 报名结果数据
// 展示门槛显示弹框
// 门槛弹窗
const showLimit = ref(false);
const showLimitVip = ref(false);
// 不使用通用的门槛弹框，判断如果不满足活动门槛，使用自己的弹窗
if (baseInfo.thresholdResponseList.length > 0) {
  if (baseInfo.thresholdLevelsStatus === 3) {
    baseInfo.thresholdLevelsStatus = 3;
    // baseInfo.thresholdResponseList = baseInfo.thresholdResponseList.filter((item) => item.type !== 4);
    baseInfo.thresholdResponseList = [];
    const openData = {
      thresholdCode: 4,
      thresholdTitle: '您不是店铺会员',
      thresholdContent: '',
      thresholdStatus: 0,
      btnContent: '加入会员',
    };
    baseInfo.thresholdResponseList = baseInfo.thresholdResponseList.concat(openData);
    showLimit.value = false;
    showLimitVip.value = true;
  } else {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
      className: 'common-message-kasadi',
    });
  }
}
// 国补专场点击
const specialPerClick = () => {
  window.location.href = 'https://pro.m.jd.com/mall/active/2cajzjiQ3roXqH5nqa7EaTsgX1Wp/index.html';
};
// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    showRulePop.value = true;
  } catch (error) {
    console.error();
  }
};
const getActivity = async () => {
  try {
    const { data } = await httpRequest.post('/91002/activity');
    console.log(data, '主接口数据');
    prizeImg.value = data.prizeImg;
    const prizeName1 = data.prizeName;
    prizeNameTwo.value = prizeName1.replace(/\s+/g, '<br>');
    prizeName.value = data.prizeName;
    prizeNum.value = data.prizeNum;
    status.value = data.status;
    closeToast();
  } catch (e) {
    if (e.message === '9101') {
      isBUser.value = true;
      closeToast();
    } else {
      showToast(e.message);
    }
  }
};
const isClickJoin = ref(true); // 是否允许点击参与报名 false 不允许 true 允许
const signUpData = async () => {
  try {
    const { data } = await httpRequest.post('/91002/signUp');
    console.log(data, '报名数据');
    closeToast();
    // 直接显示报名结果
    joinResultData.value = {
      type: 1,
    };
    showJoinResult.value = true;
    isClickJoin.value = true;
    await getActivity();
  } catch (e) {
    if (e.message === '请先填写地址') {
      closeToast();
      showAddress.value = true;
    } else if (e.message === '9101') {
      isBUser.value = true;
      closeToast();
    } else {
      closeToast();
      joinResultData.value = {
        type: 2,
        errorMessage: e.message,
      };
      showJoinResult.value = true;
    }
    isClickJoin.value = true;
  }
};
// 报名参与活动
const joinClick = async () => {
  console.log('报名参与活动', baseInfo.thresholdResponseList, baseInfo.thresholdLevelsStatus);
  if (baseInfo.thresholdResponseList.length > 0) {
    if (baseInfo.thresholdLevelsStatus === 3) {
      baseInfo.thresholdResponseList = [];
      showLimit.value = false;
      // console.log(baseInfo.thresholdLevelsStatus, 'sssssssssssss');
      baseInfo.thresholdLevelsStatus = 3;
      const openData = {
        thresholdCode: 4,
        thresholdTitle: '您不是店铺会员',
        thresholdContent: '',
        thresholdStatus: 0,
        btnContent: '加入会员',
      };
      baseInfo.thresholdResponseList = baseInfo.thresholdResponseList.concat(openData);
      showLimitVip.value = true;
    } else {
      // console.log(baseInfo.thresholdLevelsStatus, 'aaaaaaaaaaaaa');
      showLimit.value = useThreshold({
        thresholdList: baseInfo.thresholdResponseList,
        className: 'common-message-kasadi',
      });
    }
    return;
  }
  if (!isClickJoin.value) {
    return;
  }
  isClickJoin.value = false;
  // 判断订单，订单校验成功 调用signUp判断是否填写地址，未填写填写地址，已填写直接判断是否符合，不符合弹窗可以进行订单校验重新报名；
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/91002/checkOrderInfo');
    await signUpData();
  } catch (e) {
    isClickJoin.value = true;
    if (e.message === '9101') {
      isBUser.value = true;
      closeToast();
    } else {
      closeToast();
      joinResultData.value = {
        type: 2,
        errorMessage: e.message,
      };
      showJoinResult.value = true;
    }
  }
};

// 不符合订单弹窗关闭
const showJoinResultClose = async (data) => {
  if (data === 'orderCheck') {
    showJoinResult.value = false;
  } else if (data === 3) {
    joinResultData.value = {
      type: 3,
    };
  } else {
    showJoinResult.value = false;
  }
};
// 填写地址关闭弹窗
const showAddressClose = async (data) => {
  showAddress.value = false;
  if (data) {
    // 填写地址后校验是否符合报名条件
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const { data } = await httpRequest.post('/91002/signUp');
      isClickJoin.value = true;
      joinResultData.value = {
        type: 1,
      };
      showJoinResult.value = true;
      await getActivity();
    } catch (e) {
      isClickJoin.value = true;
      if (e.message === '9101') {
        isBUser.value = true;
        closeToast();
      } else {
        closeToast();
        joinResultData.value = {
          type: 2,
          errorMessage: e.message,
        };
        showJoinResult.value = true;
      }
    }
  }
};

const getSkuPageData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/91002/getSkuPage', {
      pageNum: pageNum.value,
      pageSize: 10,
    });
    closeToast();
    if (res.data.records && res.data.records.length > 0) {
      skuList.value.push(...res.data.records);
    }
    pagesAll.value = res.data.pages;
  } catch (e) {
    showToast(e.message);
  }
};
// 曝光商品加载更多
const loadMore = async () => {
  pageNum.value++;
  await getSkuPageData();
};
const init = async () => {
  console.log('初始化活动');
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivity(), getSkuPageData()]);
    closeToast();
  } catch (e) {
    closeToast();
  }
};
init();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color:#615e92;
  .header-kv{
    position: relative;
   .kv-img {
      width: 100%;
    }
    .topPrizeNameDivAll{
      //width: 6.40rem;
      //height: 0.51rem;
      //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/94577/26/49138/6799/6719eaa4F68b644d5/2489484d70091edb.png);
      //background-repeat: no-repeat;
      //background-size: 100% 100%;
      position: absolute;
      top: 1.96rem;
      color: #fff;
      left: 50%;
      transform: translate(-50%);
      font-size:0.3rem;
      line-height:0.3rem;
      width: 100%;
      text-align: center;
    }
  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    //padding: 0.3rem 0.3rem 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0rem;
  }
  .header-btn-all{
    margin-top:2.58rem;
      .header-btn {
        cursor: pointer;
      }
      .ruleBtn{
        width: 1.17rem;
        height: 0.46rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/108484/34/54801/1761/6711cc05F88df3602/1f42be34d0e5d9d3.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        font-size:0;
        margin-bottom:0.08rem;
      }
    .myPrizeBtn{
      width: 1.17rem;
      height: 0.46rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/218762/24/45117/1772/6711cc05Fd437cd61/49fe7794912da6eb.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      font-size:0;
    }
    }
  }
  .shopMessageDivAll{
    position: relative;
    margin-top: -1.58rem;
    width: 6.84rem;
    height: 1.20rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/133728/38/43049/788/6711cc06Fd0da36e7/8f0e3c5a3a72d842.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: 50%;
    transform: translate(-50%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:0 0.17rem;
    .leftDiv{
      display: flex;
      align-items: center;
      .shopLogo{
        width: 1.77rem;
        height: 0.64rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/96007/8/52064/2082/6711cc06F4d263820/896b72cc9b30930b.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .shopNameDiv{
        color:#fbe3cb;
        font-size:0.23rem;
        margin-left:0.14rem;
      }
    }
    .rightDiv{
      width: 1.56rem;
      height: 0.76rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/199968/17/46455/2240/6711cc05Ff1998ffa/baf10fb0437ece4f.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .prizeDivAll{
    width:100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top:0.38rem;
    .prizeTitleDiv{
      width: 3.22rem;
      height: 0.35rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/105619/24/53091/1878/6711f86bF652680e3/87619b4d4ed6126b.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-bottom:0.32rem;
    }
    .prizeDiv{
      width: 7.06rem;
      height: 4.11rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/248295/7/21363/3061/6711cc05F63b29233/87807fd9140db9c6.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      .topDiv{
        height:2rem;
        padding:0 0.40rem;
        .prizeMessDiv{
          height:100%;
          display:flex;
          //justify-content: space-between;
          align-items: center;
          padding-top: 0.14rem;
          .prizeImageDiv{
            width:1.5rem;
            height:1.5rem;
            background-color:#2b1e5c;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            img{
              width:1.5rem;
              height:1.5rem;
              border-radius: 50%;
            }
          }
          .prizeDetailDiv{
            margin-left:0.4rem;
            .prizeNameDiv{
              color:#fde8d1;
              font-size:0.24rem;
              display: -webkit-box;
              //-webkit-box-orient: vertical;
              //-webkit-line-clamp: 2;
              overflow: hidden;
              width:2.4rem;
            }
            .prizeRestDiv{
              color:#fde8d1;
              font-size:0.22rem;
              margin-top:0.22rem;
            }
          }
          .joinDiv{
            position: absolute;
            right: 0.4rem;
            width: 2.04rem;
            height: 0.8rem;
            background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/190828/17/48591/3362/6711cc05Ff4fc9d16/a74a78941e2c64c6.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            font-size:0;
          }
          .joinDivGray{
            position: absolute;
            right: 0.4rem;
            width: 2.04rem;
            height: 0.8rem;
            background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/190828/17/48591/3362/6711cc05Ff4fc9d16/a74a78941e2c64c6.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            font-size:0;
            filter: grayscale(1);
          }
        }
      }
      .bottomDiv{
        height:2rem;
        color:#fbe3cb;
        font-size:0.22rem;
        padding:0 0.2rem;
        display: flex;
        align-items: center;
      }
    }
  }
  .specialPerDivAll{
    width:100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top:0.38rem;
    .specialPerTitle{
      width: 3.58rem;
      height: 0.35rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/166620/21/37560/3128/67209a5bFcf36825b/1f5bdaba68e5cb81.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-bottom:0.32rem;
    }
    .specialPerConDiv{
      width: 6.91rem;
      height: 8.40rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/241713/33/20294/174690/67209691F1ae6a2f3/197943f67a4833d0.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .skuDivAll{
    width:100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top:0.38rem;
    .skuTitleDiv{
      width: 3.58rem;
      height: 0.35rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/208418/28/45621/2956/67209aa8F604f441f/1a5ebb0f60064f70.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      margin-bottom:0.34rem;
    }
    .skuListDiv{
      width:100%;
      padding: 0 0.28rem;
      display:flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .skuItemDiv{
        width: 3.40rem;
        height: 4.60rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/189602/23/49546/3593/6711f94cF6cb7e756/b3d7f346652b0b65.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        position: relative;
        margin-bottom:0.1rem;
        padding-top:0.46rem;
        .skuImgDiv{
          width:100%;
          display:flex;
          justify-content: center;
          align-items: center;
          img{
            height:2.61rem;
          }
        }
        .skuNameDiv{
          color:#fbe3cb;
          font-size:0.23rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          padding:0 0.28rem;
          margin-top:0.28rem;
        }
        .skuPriceDiv{
          color:#ff5556;
          font-size:0.24rem;
          margin-top:0.1rem;
          padding:0 0.28rem;
        }
      }
    }
    .more-btn-all {
      width:6.9rem;
      margin-top:0.24rem;
      .more-btn {
        width: 2.04rem;
        height: 0.8rem;
        font-size: 0;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/242607/14/17411/8176/67177418Fed0b698c/4c4689692ce49080.png');
        margin: 0 auto 0.3rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .bUserClass{
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/230367/2/28030/11092/671b6bddF0c4bdcfb/4e731a027e9a7379.png);
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    padding-top:3.55rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .buerTitleDiv{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/221595/5/45477/2805/671b6bdfF55f1fec8/e1b3e7049c51ad5b.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 1.71rem;
      height: 0.82rem;
    }
    .buercontentDiv{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/249456/24/21982/20414/671b6bddF02aec589/c558bd7e91d0e0eb.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 6.36rem;
      height: 1.37rem;
      margin-top:0.9rem;
    }
    .goToShopClass{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141636/3/43470/11880/660d3930F8a38a67a/288d73790cee72b3.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 4.84rem;
      height: 1.07rem;
      position: absolute;
      top: 8.2rem;
      left: calc(50% - 4.84rem / 2);
    }
  }
}

</style>
