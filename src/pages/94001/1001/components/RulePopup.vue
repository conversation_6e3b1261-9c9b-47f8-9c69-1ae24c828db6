<template>
  <div class="rule-bk" :style="furnishStyles.rulePop.value">
    <div class="content">
      <div v-html="rule">
      </div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import furnishStyles from '../ts/furnishStyles';

const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.close {
  width: 0.45rem;
  height: 0.45rem;
  margin: 0.2rem auto;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/226770/28/11313/1145/65969c51Fb9dda6aa/31b3866cf538306e.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
.rule-bk {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.5rem;
  height: 8rem;
  padding:1rem 0 0 0;
  .content {
    width: 6rem;
    height: 6.4rem;
    margin: 0.2rem auto 0 auto;
    padding: 0 0.35rem 0.3rem 0.4rem;
    font-size: 0.24rem;
    white-space: pre-wrap;
    overflow-y: scroll;
    word-wrap: break-word;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
