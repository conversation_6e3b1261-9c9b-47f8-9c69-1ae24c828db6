<template>
  <div class="bg" :style="(success || status === 1) ? furnishStyles.successPageBg.value : furnishStyles.pageBg.value">
    <div :class="(success || status === 1) ? 'header-kv-success' : 'header-kv'">
      <img class="rule-btn" v-click-track="'hdgz'" :src="furnish.ruleBtn" @click="showRulePopup" alt="" />
    </div>
    <div class="blank">
      <!--申领前-->
      <div v-if="status === 0">
        <div><span class="text1">成功申领</span><span class="text2">{{furnish.mainPageText}}</span></div>
        <!-- 进度条 -->
        <div class="progress-bar">
          <div class="progress-bar-inner" :style="{ width: progressWidth }">
            <div class="bubble">{{progressWidth}}</div>
          </div>
        </div>
        <div class="content-text">
          {{ruleTestShort}}
        </div>
        <div class="join-btn" @click="getPrizes" v-click-track="'ljsl'"></div>
      </div>
      <!--申领后-->
      <div v-if="success || status === 1">
        <div class="success-btn"></div>
        <div class="success-center">成功申领</div>
        <div class="success-text">{{furnish.successPageText}}</div>
        <!-- 进度条 -->
        <div class="progress-bar">
          <div class="progress-bar-inner" :style="{ width: progressWidth }">
            <div class="bubble">{{progressWidth}}</div>
          </div>
        </div>
        <div class="step"></div>
      </div>
    </div>
    <!-- 曝光商品-->
    <div class="sku-bg">
      <div class="sku-list" >
        <div class="sku-item" @click="gotoSkuPage(item.skuId)" v-for="(item,index) in skuList" :key="index">
          <div class="img-box">
            <img :src="item.skuMainPicture" alt="">
          </div>
          <div class="sku-text">{{item.skuName}}</div>
          <div class="sku-btns">
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>
  </div>
  <!--非会员弹窗-->
  <VanPopup teleport="body" v-model:show="showNotMember">
    <NotMember :openCardLink="openCardLink" @close="showNotMember = false"></NotMember>
  </VanPopup>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!--不满足条件-->
  <VanPopup teleport="body" v-model:show="showLimit">
    <ThresholdPopup @close="showLimit = false"></ThresholdPopup>
  </VanPopup>
  <!--剩余奖品数量不足弹窗-->
  <VanPopup teleport="body" v-model:show="showNoPrize">
    <NoPrize @close="showNoPrize = false"></NoPrize>
  </VanPopup>
</template>
<script setup lang="ts">
import { inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import RulePopup from './components/RulePopup.vue';
import ThresholdPopup from './components/ThresholdPopup.vue';
import NoPrize from './components/NoPrize.vue';
import NotMember from './components/NotMember.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast, Toast } from 'vant';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import '@/components/Threshold2/CPBStyle.scss';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

// 进度条
const progressWidth = ref('80%');

const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};

// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');

// 活动规则相关
const showRulePopup = async () => {
  try {
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
// 获取规则
const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    ruleTest.value = data;
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取简短规则
const ruleTestShort = ref('');
const getRuleShort = async () => {
  try {
    const { data } = await httpRequest.get('/94001/getShortRule');
    ruleTestShort.value = data;
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

// 活动商品列表
type Sku = {
  skuId: string,
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
// 奖品信息
const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  prizeId: '',
  userReceiveRecordId: '',
});

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

// 奖品库存剩余数量
const stockNum = ref(0);
// 查询库存剩余
const checkRate = async () => {
  try {
    const { data } = await httpRequest.post('/94001/getStockInfo');
    progressWidth.value = data.rate;
    stockNum.value = data.stockNum;
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/94001/skuListPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error) {
    console.error(error);
  }
};

const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};

// 是否参与过活动 0 未参与 1 参与成功
const status = ref(0);
// 获取参与状态   0 未参与 1 参与成功
const getStatus = async () => {
  try {
    const { data } = await httpRequest.post('/94001/prize');
    status.value = data;
    console.log(status.value, 'status');
  } catch (error) {
    console.error(error);
  }
};

// 展示门槛显示弹框
const showLimit = ref(false);

// 是否领取成功
const success = ref(false);
// 是否展示奖品库存不足
const showNoPrize = ref(false);
// 开卡链接
const openCardLink = ref('');
// 非会员弹窗
const showNotMember = ref(false);

// 领取奖品
const getPrizes = async () => {
  if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
    openCardLink.value = baseInfo.openCardLink;
    showNotMember.value = true;
    console.log('您不是会员');
    return;
  }
  if (!isStart.value && !isEnd.value) {
    showToast('活动未开始');
    return;
  }
  if (isEnd.value) {
    showToast('活动已结束');
    return;
  }
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = true;
    return;
  }
  // 奖品库存不足
  if (stockNum.value <= 0) {
    showNoPrize.value = true;
    return;
  }
  try {
    const { data, code } = await httpRequest.post('/94001/sendUser');
    console.log(data, code, 'ackMessage');
    if (code === 200) {
      success.value = true;
      await getStatus();
    }
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

// 初始化
const init = async () => {
  if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
    openCardLink.value = baseInfo.openCardLink;
    showNotMember.value = true;
    console.log('您不是会员');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    if (!isStart.value && !isEnd.value) {
      showToast('活动未开始');
    }
    if (isEnd.value) {
      showToast('活动已结束');
    }
    await Promise.all([getRule(), getSkuList(), getStatus(), checkRate(), getRuleShort()]);
    // 两秒后关闭
    setTimeout(() => {
      closeToast();
    }, 2000);
  } catch (error) {
    closeToast();
  }
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>

<style>
@font-face {
  font-family: 'FZLTHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTHJW/FZLTHJW--GB1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTHJW/FZLTHJW--GB1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'FZLTHJW';
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom:0.2rem;
}

.header-kv {
  position: relative;
  height: 9.72rem;
  .rule-btn {
    position: absolute;
    top: 2.93rem;
    right: 0;
    width: 0.58rem;
  }
}
.header-kv-success{
  position: relative;
  height: 8.68rem;
  .rule-btn {
    position: absolute;
    top: 2.93rem;
    right: 0;
    width: 0.58rem;
  }
}

.blank {
  width: 7rem;
  height: 4.5rem;
  margin: 0 auto;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/129687/2/43368/34134/661d1f29Fff33956e/de7250d6031ec069.png') no-repeat;
  background-size: 100%;
  padding: 0.3rem 0.4rem 0.4rem;
  .text1 {
    font-size: 0.36rem;
    color: #b80818;
    margin-right: 0.2rem;
  }
  .text2 {
    font-size: 0.22rem;
  }
  .progress-bar {
    width: 6.12rem;
    height: 0.25rem;
    margin: 0.62rem auto 0;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/249757/33/7464/1060/661d1f29F930257f9/6fa6f03a86ac956f.png') no-repeat;
    background-size: 100%;
    padding: 0.025rem 0.07rem 0;
    .progress-bar-inner {
      //width: 80%;
      height: 0.2rem;
      border-radius: 0.2rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/110292/35/26393/1306/661d1f29Fac0da7ef/2c185f9944cf29f7.png');
      background-repeat: no-repeat;
      background-size: 120% 100%;
      background-position: center;
      position:relative;
      .bubble{
        position: absolute;
        top: -0.45rem;
        right: -0.25rem;
        width: 0.56rem;
        height: 0.38rem;
        line-height: 0.32rem;
        text-align: center;
        font-size: 0.2rem;
        color: #fff;
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/194286/21/44054/769/661dea5fFfb74b522/223c5ba4f740f71c.png') no-repeat;
        background-size: 100%;
      }
    }
  }
  .content-text{
    font-size: 0.18rem;
    line-height: 0.32rem;
    color: #494944;
    height: 1.6rem;
    overflow: hidden;
    overflow-y: scroll;
    margin: 0.2rem 0;
    white-space: pre-wrap;
  }
  .join-btn {
    margin:0.2rem auto 0;
    background: url("//img10.360buyimg.com/imgzone/jfs/t1/229105/10/16843/11096/661de91eF3ac289b1/a2e34261151fe256.png") no-repeat;
    background-size: 100%;
    width:2.13rem;
    height:0.49rem;
  }
  .success-btn{
    margin:0 auto;
    background: url("//img10.360buyimg.com/imgzone/jfs/t1/191483/27/44066/3836/661e12dcF22da95b9/8e6849b7bac9dff6.png") no-repeat;
    background-size: 100%;
    width:2.43rem;
    height:0.67rem;
  }
  .success-center {
    color: #b8952b;
    margin:0.15rem auto 0;
    text-align: center;
    font-size: 0.26rem;
  }
  .success-text {
    margin:0 auto;
    font-size: 0.22rem;
    text-align: center;
  }
  .step{
    margin:0.3rem auto 0;
    background: url("//img10.360buyimg.com/imgzone/jfs/t1/230190/16/14023/13090/661e12dcF04c67d89/2f2632f11243d586.png") no-repeat;
    background-size: 100%;
    width:6.05rem;
    height:0.76rem;
  }
}
.sku-bg{
  width: 6.8rem;
  max-height: 9.54rem;
  background-color: #ffffff;
  border-radius: 0.1rem;
  margin: 0.4rem auto;
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    width: 6.8rem;
    max-height: 9.3rem;
    margin: -0.43rem auto 0px;
    place-content: flex-start space-between;
    padding: 0.2rem;
    border-radius: 0.4rem 0 0.05rem 0.05rem;
    //height: 5.9rem;
    position: relative;
    overflow-y: scroll;
  }
  .more-btn-all {
    width:6.9rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #494944;
      background: -webkit-gradient(linear, left top, right top, from(#e5d4a3), to(#d5c091));
      background: linear-gradient(90deg, #e5d4a3 0%, #d5c091 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-item{
    width: 3.05rem;
    height:4.5rem;
    margin-bottom: 0.1rem;
    overflow: hidden;
    background:url("//img10.360buyimg.com/imgzone/jfs/t1/96213/34/43168/3404/661df26cF65f8a208/7eb7a71aa5945c91.png") no-repeat;
    background-size: 100%;
    .img-box{
      margin: 0.16rem auto 0;
      width: 2.8rem;
      height: 2.8rem;
      border-radius: 0.1rem;
      //background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/115779/26/43052/4243/65a5ef3eF130b15a6/a7f6c61bf8fc34c6.png");
      background-size: 100%;
      background-repeat: no-repeat;
      overflow: hidden;
      //padding: 0.1rem;
    }
    img{
      display: block;
      height: 2.8rem;
      margin:  auto;
      border-radius: 0.2rem;
    }
    .sku-text{
      width: 2.7rem;
      font-size: 0.23rem;
      margin: 0.1rem auto 0.1rem auto;
      box-sizing: border-box;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .sku-btns{
      margin:0 auto;
      width: 1.53rem;
      height: 0.42rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/216057/12/38056/3059/661df26dF563f980a/c55c4abba6dcdb45.png);
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
