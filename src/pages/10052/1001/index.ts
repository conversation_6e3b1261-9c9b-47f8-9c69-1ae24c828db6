import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import '@/style';
import thresholdPlugin from '@/plugins/ThresholdChecker';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  backActRefresh: false,
};

init(config)
  .then(({
    baseInfo,
    pathParams,
    decoData,
  }) => {
    // 设置页面title
    document.title = baseInfo?.activityName || '助力抢红包';
    app.provide('baseInfo', baseInfo);
    app.provide('decoData', decoData);
    app.provide('pathParams', pathParams);
    app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
    app.use(thresholdPlugin);
    app.mount('#app');
  })
  .catch((e) => {
    console.error(e);
  });
