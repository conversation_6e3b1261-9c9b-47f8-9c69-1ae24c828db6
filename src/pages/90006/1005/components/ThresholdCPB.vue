<template>
  <VanPopup v-model:show="limitPopup" :closeOnClickOverlay="false">
    <div class="common-limit">
      <div class="common-limit-content">
        <div v-for="(item, index) in data" :key="index" class="common-limit-item">
          <div class="common-limit-item-text" v-if="item.thresholdCode === 4">很抱歉，您还不是CPB品牌会员，<br />请先加入品牌会员，再参与活动。</div>
          <div class="common-limit-item-text" v-else>{{ item.thresholdContent }}</div>
          <div v-if="item.btnContent && item.thresholdStatus === 0" @click="eventClick(item.type)" class="common-limit-btn">
            <span v-if="item.thresholdCode === 4">立即入会 ></span>
            <span v-else>{{ item.btnContent }}</span>
          </div>
        </div>
      </div>
    </div>
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/19991/28/20752/1150/659bdc16Fb8182d8d/bd77364aab8a284a.png" alt="" class="common-limit-close-btn" @click="close" />
  </VanPopup>
</template>
<script setup lang="ts">
import { defineEmits, defineProps, inject, ref, watch } from 'vue';
import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const baseInfo: any = inject('baseInfo') as BaseInfo;

const props = defineProps(['data', 'show']);
const emits = defineEmits(['update:show']);

const limitPopup = ref(props.show);
const close = () => {
  limitPopup.value = false;
  emits('update:show', false);
};

watch(
  () => props.show,
  (val) => {
    limitPopup.value = val;
  },
);

const fellowShop = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    await httpRequest.post('/common/followShop');
    showToast({
      message: '关注成功',
      forbidClick: true,
    });
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};
const eventClick = async (type: number) => {
  switch (type) {
    case 1:
      lzReportClick('ljrh');
      // 去开卡
      window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
      break;
    case 2:
      // 关注店铺
      await fellowShop();
      setTimeout(() => {
        window.location.reload();
      }, 1500);
      break;
    case 3:
      // 进店铺
      await gotoShopPage(baseInfo.shopId);
      break;
    case 4:
      // 关注店铺并立即入会
      lzReportClick('ljrh');
      await fellowShop();
      window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
      break;
    default:
      console.log('~');
  }
};
</script>
<style lang="scss" scoped>
.common-limit {
  position: relative;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/184968/27/43948/10109/6612017fFaec51791/c85d7468d1d5d07a.png) no-repeat;
  width: 5.17rem;
  background-size: 100% 100%;
  padding: 1rem 0.2rem 0.2rem;
  display: flex;
  align-items: center;
  flex-direction: column;
  text-align: center;
  .common-limit-content {
    width: 100%;
    min-height: 2.5rem;
    .common-limit-item {
      align-items: center;
      justify-content: space-between;
      padding: 0.3rem;
      border-radius: 0.2rem;
      &:not(:last-child) {
        margin-bottom: 0.1rem;
      }

      .icon {
        width: 0.8rem;
        height: 0.8rem;
        object-fit: contain;
        margin-right: 0.2rem;
      }
      .common-limit-item-text {
        flex: 1;
        font-size: 0.25rem;
        line-height: 0.5rem;
        color: #000000;
      }
      .common-limit-btn {
        margin: 0.4rem auto;
        background-image: linear-gradient(270deg, #785334 0%, #a17d59 27%, #caa67d 54%, #785334 100%);
        background-size: 100%;
        width: 1.6rem;
        height: 0.5rem;
        text-align: center;
        line-height: 0.5rem;
        color: #fff;
        font-size: 0.24rem;
        border-radius: 0.25rem;
      }
    }
  }
}
.common-limit-close-btn {
  width: 0.4rem;
  margin: 0.2rem auto 0;
}
</style>
