<template>
  <div class="bk">
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
import { inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const close = () => {
  console.log(111);

  emits('close');
};
</script>

<style scoped lang="scss">
.bk {
  width: 5.5rem;
  height: 6.36rem;
  background: url(../assets/noPay.png) no-repeat;
  background-size: 100%;
  position: relative;
  .btn {
    position: absolute;
    top: 3.94rem;
    left: 1.35rem;
    right: 1.35rem;
    height: 0.7rem;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 2.43rem;
    right: 2.43rem;
    height: 0.6rem;
  }
}
</style>
