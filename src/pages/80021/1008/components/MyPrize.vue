<template>
  <div class="rule-bk">
    <div class="close" @click="close"></div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="flex">
          <div class="type">{{ prizeType[item.prizeType] }}</div>
          <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
        </div>
        <div class="info">
          <div class="prizeImgClass">
            <img :src="item.prizeImg" alt="" class="show-img" />
          </div>
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
          </div>
          <div class="status" v-if="item.prizeType === 3">
            <div class="blue" v-if="!item.deliveryStatus">待发货</div>
            <div class="gray" v-else>已发货</div>

            <div class="blue" @click="changAddress(item)">
              <span v-if="!item.deliveryStatus && item.realName" class="pink">查看地址</span>
              <span v-else class="orange">填写地址</span>
            </div>
          </div>
          <div class="status" v-else-if="item.prizeType === 7">
            <!-- <div class="orange">待发货</div> -->
            <div class="green" @click="showCardNum(item)">如何兑换</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
            <!-- <div class="orange">待发货</div> -->
            <div class="green" @click="exchangePlusOrAiqiyi">立即兑换</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 12">
            <div class="orange" v-if="item.isFuLuWaitingReceive">待领取</div>
            <div class="gray" v-else>已发放</div>
            <div class="green" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive">点击领取</div>
          </div>
          <div class="status" v-else>
            <div class="gray">已发放</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  realName: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80021/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  addressId.value = item.addressId;
  activityPrizeId.value = item.activityPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userPrizeId, prizeContent.result.planDesc);
};
</script>
<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/248382/18/5770/19182/65f254ffFb6a3ccbf/6083cd50b3898681.png) no-repeat;
  background-size: 100%;
  width: 100vw;
  padding-top:1.5rem;
  .close {
    width: 0.55rem;
    height: 0.55rem;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/231478/25/14444/708/65f2528aFe61fcaff/c36db82ec074b4f8.png");
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top:1.04rem;
    right:0.22rem;
    z-index: 10;
  }

  .content {
    height: 10rem;
    width: 7rem;
    margin: 0.3rem auto 0;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    //padding-top:1.8rem;
    //padding-bottom:0.32rem;
    .prize {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/180829/6/42723/1146/65f254feF65df5aa2/e5379effc43bc5d4.png) no-repeat;
      background-size: 100%;
      margin-bottom: 0.1rem;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.08rem 0.22rem;
      }

      .type {
        color: #444444;
        font-size: 0.2rem;
        text-align: left;
      }
      .time {
        color: #444444;
        font-size: 0.2rem;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 0.9rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        .prizeImgClass{
          border-radius: 50%;
          background-color: #95c548;
          width: 0.75rem;
          height: 0.75rem;
          display:flex;
          align-items:center;
          justify-content:center;
        }

        .show-img {
          //width: 0.6rem;
          height: 0.5rem;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #000000;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;
          .gray {
            color: #333333;
          }
          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #0083ff;
          }
          .purple{
            color:#b24ff1;
          }
          .blue1 {
            color: #3458f5;
          }
          .pink{
            color: #ff75a9;
          }
        }
      }
    }

    .no-data {
      height: 10rem;
      font-size: 0.24rem;
      color: #8c8c8c;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/123923/19/42852/3087/65f254ffF50599f3a/83d34e1120381f05.png) no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
