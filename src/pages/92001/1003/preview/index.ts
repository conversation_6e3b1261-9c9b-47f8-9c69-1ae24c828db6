import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  actBgSuccess: '//img10.360buyimg.com/imgzone/jfs/t1/255064/28/17873/311037/67a2fc82F7699c459/95f4a42e685c4c39.png',
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/262758/4/14556/347824/6792eddcF80ae8bde/fab177e163542610.png',
  actBgColor: '#cce0f2',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/253519/9/15518/6109/6792ec6eFe7a9aaa8/0d36548992f0f6f8.png',
  prizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/257227/14/15349/6610/6792ec6eF9c4a9228/46a6e8bf706b8958.png',
  prizeTextColor: '#0049b8',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/260597/9/14987/5056/6793111cF2377b48c/6923279b45282b25.png',
  moreBg: '//img10.360buyimg.com/imgzone/jfs/t1/252374/31/16343/10478/6792ee8fFd9c7cfb6/0618540102dca82d.png',
  ruleBox: '//img10.360buyimg.com/imgzone/jfs/t1/266698/33/15092/19206/6792ec6fF3ecd193a/65386f21ccc3a73e.png',
  ruleTextColor: '#0049b8',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/255904/9/16234/31385/6792f2ceF0bc2e505/0969083f29f7e717.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/258148/34/15107/7720/6792f2cfF484939c5/fa07bf7fc0034cc6.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/263926/13/15181/45336/6792f2cfF3ea95237/cd87e5a1a71d5066.png',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '美素跨境新客券';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
