<template>
  <div class="rule-bk">
    <div class="content">
      <div v-if="myPrizeList.length>0">
        <div v-for="item in myPrizeList" :key="item.prizeName">
          <div class="item">
            <img alt="" :src="item.prizeImg">
            <div class="main">
              <div>{{ item.prizeName }}</div>
              <div>数量:1</div>
              <div>失效时间：<span>{{ dayjs(item.expireTime).format('YYYY-MM-DD HH:mm:ss') }}</span></div>
            </div>
          </div>
          <div class="prizeTime">领取时间：{{dayjs(item.prizeTime).format('YYYY-MM-DD HH:mm:ss')}}</div>
          <img @click="gotoSkuPage(item.skuId)" class="buy" src="//img10.360buyimg.com/imgzone/jfs/t1/249993/32/24836/7880/67935097Fcd9dc0b5/2c16f8025482b5a4.png" alt="">
        </div>
      </div>
      <div class="nodata" v-else>
        暂无权益
      </div>
    </div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { httpRequest } from '@/utils/service';
import { showToast } from 'vant';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';

const isPreview = window.location.pathname.includes('preview');
// 我的奖品列表
const myPrizeList = ref([]);

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
const getRule = async () => {
  try {
    const { data } = await httpRequest.post('/92001/myCoupon');
    myPrizeList.value = data;
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};
!isPreview && getRule();
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('../assets/myPrize.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.5rem;
  height: 8rem;
  padding: 2.7rem 0 0 0;
  .content {
    width: 6rem;
    height: 4.5rem;
    margin: 0 auto;
    padding: 0 0.1rem 0 0.1rem;
    font-size: 0.32rem;
    white-space: pre-wrap;
    overflow-y: scroll;
    word-wrap: break-word;
    color: #024fb4;
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
      img {
        width: 1.6rem;
        height: 1.6rem;
        margin-right: 0.2rem;
      }
      .main {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: 1.4rem;
        font-size: 0.26rem;
      }
    }
    .prizeTime {
      text-align: center;
      font-size: 0.32rem;
    }
    .buy {
      width: 2.35rem;
      height: 0.74rem;
      margin: 0.2rem auto 0;
    }
    .nodata {
      text-align: center;
      font-size: 0.32rem;
      color: #024fb4;
      height: 4rem;
      line-height: 4rem;
    }
  }
  .close {
    width: 0.41rem;
    height: 0.42rem;
    position: absolute;
    top: 0.25rem;
    right: 0.36rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
