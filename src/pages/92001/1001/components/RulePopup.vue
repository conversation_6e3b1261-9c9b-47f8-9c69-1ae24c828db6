<template>
  <div class="rule-bk">
    <div class="content">
      <div v-html="rule"></div>
    </div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('../assets/rule.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.5rem;
  height: 8rem;
  padding: 1rem 0 0 0;
  .content {
    width: 6rem;
    height: 6.6rem;
    margin: 0 auto;
    padding: 0 0.35rem 0 0.4rem;
    font-size: 0.24rem;
    white-space: pre-wrap;
    overflow-y: scroll;
    word-wrap: break-word;
    color: #024fb4;
  }
  .close {
    width: 0.41rem;
    height: 0.42rem;
    position: absolute;
    top: 0.25rem;
    right: 0.36rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
