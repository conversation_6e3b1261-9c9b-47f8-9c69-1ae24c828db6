<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>抽奖记录</div>
      <div class="rightLineDiv"></div>
      <img data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" alt=""/>
    </div>
    <div class="contentAll">
      <div class="my-friend-container" v-if="drawRecordList.length > 0">
        <div class="friendInfo" v-for="(it, index) in drawRecordList" :key="index">
          <div class="topItemDiv">
            <div class="item itemDate">抽奖时间</div>
            <div class="item">中奖状态</div>
            <div class="item">获取奖品</div>
          </div>
          <div class="bottomItemDiv">
            <div class="item itemDate">{{ dayjs(it.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div :class="['item',it.state === 0 ? 'itemNo':'itemYes']">{{it.state === 0 ? '未中奖' : '中奖'}}</div>
            <div :class="['item',it.prizeName ? 'itemYes' : 'itemNo']">{{it.prizeName ? it.prizeName : '--'}}</div>
          </div>
        </div>
      </div>
      <div v-else class="noFriends" >暂无抽奖记录哦</div>
      <div class="closeDiv" @click="close()"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

interface DrawData {
  prizeName: string;
  state: number;
  createTime: number;
}
const drawRecordList = reactive([] as DrawData[]);

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const dataAll = await httpRequest.post('/10094/draw');
    // const dataAll = {
    //   data: [
    //     {
    //       przieName: '',
    //       state: 0,
    //       createTime: 1709793775000,
    //     },
    //   ],
    //   resp_code: 0,
    //   resp_msg: '',
    // };
    drawRecordList.splice(0);
    drawRecordList.push(...dataAll.data);
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }
  .contentAll{
    position: relative;
    z-index: 300;
    margin-top: 0.32rem;
    height: 9.14rem;
    overflow-y: scroll;
    .noFriends {
      height: 100%;
      font-size: 0.24rem;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #8c8c8c;
    }
    .my-friend-container {
      display: flex;
      flex-wrap: wrap;
      .noMoreRecordDiv{
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        margin-top: 1.06rem;
      }
    }
    .friendInfo {
      width: 6.84rem;
      background: #FFFFFF;
      border-radius: 0.08rem;
      align-items: center;
      margin-bottom: 0.2rem;
      margin-left: calc(50% - 6.84rem / 2);
      padding: 0.24rem 0rem 0.3rem 0.12rem;
      .topItemDiv{
        display: flex;
        flex-direction: row;
        color: #999999;
        font-size: 0.22rem;
      }
      .bottomItemDiv{
        display: flex;
        flex-direction: row;
        color: #000000;
        font-size: 0.22rem;
        margin-top: 0.28rem;
        .itemNo{
          color: #2faa0e;
        }
        .itemYes{
          color: #e92c46;
        }

      }
      .item{
        flex: 1;
        text-align: center;
      }
      .itemDate{
        white-space:nowrap;
        // flex: 2 !important;
      }
    }
  }
}
</style>
