import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const a = {
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/244577/9/1209/594074/658e3cf4F620babf8/f8218b43cdaba52b.png',
  pageBg: '',
  actBgColor: '#fecc65',
  shopNameColor: '#000000',
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/232527/23/9554/4655/658e3ce8Fde756f38/7819a645b5c7da10.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/222332/7/36369/4618/658e3ce8F85e56dba/fe953a34bcd70722.png',
  queryOrderBg: '//img10.360buyimg.com/imgzone/jfs/t1/108359/19/49436/4748/658e3d62Fcac87685/c3f022532853c073.png',
  orderSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/245777/27/1285/4813/658e3d63Ff454cf00/b4748c2df9e27cbe.png',
  wheelBg: '//img10.360buyimg.com/imgzone/jfs/t1/235440/24/10275/127549/658e3c3dF6dad03f8/4a054360bc39fbc3.png',
  drawBtn: '//img10.360buyimg.com/imgzone/jfs/t1/240203/38/1484/27395/658e3c3cF801f9e62/7b7b33669860e685.png',
  wheelTextColor: '#000000',
  drawsNum: '#ff0000',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/229373/15/10377/10317/658e3c3cF37855518/f848bc7b524295f2.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/248514/3/1139/288438/658e3c45F19ec07c4/0d3bd2a48a84b706.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/248514/3/1139/288438/658e3c45F19ec07c4/0d3bd2a48a84b706.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/248514/3/1139/288438/658e3c45F19ec07c4/0d3bd2a48a84b706.png',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '首购抽奖';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
