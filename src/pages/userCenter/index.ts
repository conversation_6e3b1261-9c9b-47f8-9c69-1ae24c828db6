export interface FormType {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
}

export const getParams = (val: string): string | null => {
  try {
    const url: string = window.location.href;
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    const id = params.get(val);
    console.log(id, val, params.get(val), params, urlObj);
    return id || null;
  } catch (error) {
    // 如果 URL 格式不正确，捕获错误并返回 null
    console.error(error);
    return null;
  }
};

export const getHashParams = (paramName: string): string | null => {
  const hash = window.location.hash.substring(1); // 去掉开头的#
  const hashParams = new URLSearchParams(hash.split('?')[1] || ''); // 尝试解析?后面的部分
  return hashParams.get(paramName) || null;
};
