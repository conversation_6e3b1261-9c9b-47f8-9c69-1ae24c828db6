<template>
  <div class="rule-bk">
<!--    <div class="title">-->
<!--      <span>获奖时间</span>-->
<!--      <span>京豆数量</span>-->
<!--      <span>发放状态</span>-->
<!--    </div>-->
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="time">{{ item.createTime }}</div>
        <div class="name">{{ item.prizeNum }}京豆</div>
        <div class="name">{{ item.status }}</div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
    <div class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  winStatus: number;
}

const prizes = ref([
  // {
  //   createTime: '2025-11-20',
  //   prizeNum: '100',
  //   status: '已发放',
  // },
]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/92003/getUserPrizes');
    res.data.forEach((item: Prize) => {
      item.createTime = dayjs(item.createTime).format('YYYY-MM-DD');
    });
    closeToast();
    prizes.value = res.data;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/281162/31/21398/65281/6801edf4Fdb412565/9ea12c2e5fe259bf.png');
  background-size: 100% 100%;
  width: 7.12rem;
  height: 8.29rem;
  background-repeat: no-repeat;
  position: relative;

  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: .8rem;
    height: .8rem;
    border-radius: 50%;
  }

  .content {
    width: 6rem;
    height: 4.5rem;
    overflow-y: scroll;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 2.5rem;
    font-size: 0.24rem;
    color: #836953;
    white-space: pre-wrap;
    padding: 0 0.2rem;
    .title{
      display: flex;
      justify-content: space-between;
      color: #836953;
      font-size: 0.26rem;
      span {
        flex: 1;
        text-align: center;
      }
    }
    .prize {
      padding: 0.2rem 0;
      color: #836953;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .time,
      .name {
        width: 50%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .no-data {
      text-align: center;
      line-height: 4rem;
      font-size: 0.24rem;
      color: #836953;
    }
  }
}
</style>
