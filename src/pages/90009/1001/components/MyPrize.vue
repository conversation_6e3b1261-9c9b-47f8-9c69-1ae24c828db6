<template>
  <div class="rule-bk">
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="info">
          <div class="detail">
            <div class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
          </div>
          <div class="name">{{ item.prizeName }}</div>
          <div class="status">
            <div class="orange" v-if="item.status === 1">待发放</div>
<!--            <div class="green" v-else-if="item.status === 3">已发放</div>-->
            <div class="red" v-else-if="item.status === 2">已取消</div>
            <div class="blue" @click="showCardNum(item)" v-if="item.status === 3">如何兑换</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无记录~</div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import Clipboard from 'clipboard';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  userReceiveRecordId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  // 状态 0允许报名  1 报名成功 2 取消报名 3 已发奖
  status: number;
  showOrder: boolean;
  orderList: {
    orderId: string;
    orderStatus: string;
    orderPrice: string;
  }[];
  deliverName: string;
  deliverNo: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90009/getReceive');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
    prizes.forEach((item) => {
      item.showOrder = false;
    });
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const showSaveAddress = ref(false);
const userReceiveRecordId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userReceiveRecordId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/229803/20/23757/20476/66b9701fF2d14e746/7d3b88fec08fd578.png) no-repeat;
  background-size: 100%;
  width: 5.89rem;
  height: 8.5rem;

  .content {
    height: 8.2rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    padding: 1.8rem 0 0;
    .prize {
      border-radius: 0.16rem;

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.16rem;
        padding-bottom: 0.16rem;
        border-bottom: 0.02rem dashed #eee;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        line-height: 0.37rem;

        .show-img {
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1.2;
          text-align: center;

          .time {
            font-size: 0.2rem;
          }
        }
        .name {
          flex: 1.3;
          text-align: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 0.2rem;
        }

        .status {
          text-align: center;
          font-size: 0.2rem;
          flex: 1;
          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #0083ff;
          }

          .red {
            color: #ff3333;
          }
        }
      }

      .deliver{
        margin-left: 1.3rem;
        margin-right: 0.22rem;
        color: #999999;
        font-size: 0.2rem;
        display: flex;
        justify-content: space-between;
        .copy-btn {
          color: #0083ff;
          margin-top:0.24rem;
          font-size: 0.24rem;
        }
      }
      .order-list {
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        margin-top: 0.2rem;
        border-top: 0.02rem dashed #eee;
      }
      .order-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.2rem;
        font-size: 0.2rem;
        color: #999999;

        .order-id {
          flex: 1.5;
        }
        .order-status {
          flex: 1;
        }
        .order-price {
          flex: 1.1;
        }
      }
    }

    .no-data {
      text-align: center;
      line-height: 35vh;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}
.close {
  width: 0.55rem;
  height: 0.55rem;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/6960/9/37106/1796/66b5fc2dF8b2bc60b/ca14d8d21b5ab2c8.png");
  background-repeat: no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
