<template>
  <div class="card-bk">
    <div class="content">
      <img :src="detail.prizeImg" alt="" class="card-img" />
      <div class="prize-name">{{ detail.prizeName }}</div>
      <div class="item" v-show="detail.cardNumber">
        <div class="label">礼品卡券号：</div>
        <div class="text">{{ detail.cardNumber }}</div>
      </div>
      <div class="item" v-show="detail.cardPassword">
        <div class="label">卡密：</div>
        <div class="text">{{ detail.cardPassword }}</div>
      </div>
      <div class="copy-btn" :copy-text="copyTxt"/>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import { computed, PropType } from 'vue';
import { CardType } from '../ts/type';
import Clipboard from 'clipboard';
import { showToast } from 'vant';

const props = defineProps({
  detail: {
    type: Object as PropType<CardType>,
    required: true,
  },
});

const copyTxt = computed(() => {
  if (props.detail.cardNumber && props.detail.cardPassword) {
    return `卡号：${props.detail.cardNumber}
卡密：${props.detail.cardPassword}`;
  }
  if (props.detail?.cardNumber) {
    return props.detail?.cardNumber;
  }
  if (props.detail?.cardDesc) {
    return props.detail?.cardDesc;
  }
  return '';
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.card-bk {
  position: relative;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/19420/18/34264/15638/66b981fdFb5caab4a/823f2f5e3653ea29.png) no-repeat;
  background-size: 100%;
  width: 5.08rem;
  height: 7.05rem;
  padding: 0.72rem 0.32rem 0.32rem;

  .content {
    //padding: 0.35rem;
    .card-img {
      margin: 0 auto;
      height: 1.5rem;
    }
    .prize-name {
      width: 100%;
      text-align: center;
      font-weight: bolder;
      margin-top: 0.2rem;
      margin-bottom: 0.2rem;
      font-size: 0.36rem;
    }
    .item {
      width: 3.84rem;
      align-items: center;
      font-size: 0.3rem;
      margin: 0 auto;
      text-align: center;
      .label {
        width: 3.84rem;
      }
      .text {
        width: 3.84rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background-color: #fff;
        padding: 0.1rem 0.3125rem;
        line-height: 0.45rem;
        border-radius: 0.05rem;
      }
    }
    .copy-btn {
      width: 3.89rem;
      height: 0.85rem;
      margin: 0.42rem auto;
      border-radius: 0.42rem;
    }
    .tip {
      font-size: 0.2rem; // 0.2rem / 0.32
      color: #a3a3a3;
      white-space: pre-line;
      max-height: 3rem; // 3rem / 0.32
      overflow-y: scroll;
    }
    .tip-small {
      max-height: 1.5rem; // 2.3rem / 0.32
    }
  }
}
.close {
  width: 0.55rem;
  height: 0.55rem;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/6960/9/37106/1796/66b5fc2dF8b2bc60b/ca14d8d21b5ab2c8.png");
  background-repeat: no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
