<template>
  <div class="bg bg91005" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/253270/36/28670/152097/67c69a4fF9c9bc1e2/73be69cf85c037a0.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRulePopup" v-click-track="'hdgz'"></div>
        </div>
      </div>
    </div>
    <div class="messageDivAll">
      <div class="leftDiv">
        <div class="logoDiv" :style="{'backgroundImage': 'url(' + userInfo.avatar+')'}"></div>
        <div class="nickNameDiv">{{userInfo.nickname}}</div>
      </div>
      <div v-if="isShowPhone" class="phoneDiv">手机号：132****234</div>
    </div>
    <div class="queryActMessDivAll">
      <div class="titleDivAll">
        <div v-if="sortName" :class="[currentTab === 1 ? 'selectDiv' : 'noSelectDiv']" @click="selectQueryClick(1)">
          <div class="selectImgDiv"></div>
          <div class="selectTextDiv">{{sortName}}</div>
        </div>
        <div v-if="importSortName"  :class="[currentTab === 2 ? 'selectDiv' : 'noSelectDiv']" @click="selectQueryClick(2)">
          <div class="selectImgDiv"></div>
          <div class="selectTextDiv">{{importSortName}}</div>
        </div>
      </div>
      <div class="queryContentDivAll">
        <div class="promptDiv" v-if="currentTab === 1">{{moduleDesc}}</div>
        <div class="promptDiv" v-else-if="currentTab === 2">{{importModuleDesc}}</div>
        <div v-if="currentTab === 2" class="queryConditionDivAll">
          <div class="selectNameDiv" @click="showQueryConditionPicker=true">
            {{queryConditionName}}<van-icon class="vanIconClass" name="arrow-down" color="#000000" />
          </div>
          <div>
            <VanField v-if="queryConditionNameValue === 'mobile'" class="inputClass" placeholder="请输入" v-model="queryContent" maxlength="11" type="number"></VanField>
            <VanField v-else class="inputClass" placeholder="请输入" v-model.trim="queryContent"></VanField>
          </div>
        </div>
        <div v-if="currentTab === 2" class="queryBtnDiv" @click="queryActClick()"></div>
        <div class="queryResultDivAll">
          <div class="queryItemDivAll" v-for="(item,index) in activityListData" :key="index">
            <div>
              <div class="topDiv">
                <div class="actNameDiv">
                  <div class="actNameDiv1">{{item.activityName}}</div>
                </div>
                <div class="prizeStatusDiv" v-if="!item.isShowAllMessage">
                  <div v-if="currentTab === 1 || (currentTab === 2 && queryContent)">
                    <div class="origin" v-if="item.status === '已中奖'">{{item.status}}</div>
                    <div class="gray" v-if="item.status === '未中奖'">{{item.status}}</div>
                  </div>
                </div>
                <div class="prizeStatusDiv" v-else>
                  <div class="blue" @click="showClick(item)">收起<van-icon class="vanIconClass" name="arrow-up" color="#1890ff" /></div>
                </div>
              </div>
              <div class="showMessageTextDiv" v-if="!item.isShowAllMessage">
                <div class="blue" @click="showClick(item)">展开<van-icon class="vanIconClass" name="arrow-down" color="#1890ff" /></div>
              </div>
              <div class="activityMessageDiv" v-if="item.isShowAllMessage">
                <div v-if="currentTab === 1 || (currentTab === 2 && queryContent)">
                  <div v-if="item.status === '未中奖'" class="black noDrawDiv">很遗憾，您未中奖~</div>
                  <div class="itemActPrizeListDiv" v-if="item.status === '已中奖'">
                    <div class="itemActPrizeItemDiv" v-for="(item1,index1) in item.prizeResponses" :key="index1">
                      <div class="dateDiv black">{{dayjs(item1.grantTime).format('YYYY/MM/DD')}}</div>
                      <div class="prizeName black">{{item1.prizeName}}</div>
                      <div class="prizeStatus black">{{item1.prizeStatus}}</div>
                    </div>
                  </div>
                </div>
                <div class="itemBtnDiv">
                  <div class="winnerRecordDiv" v-if="item.show" @click="queryWinnerClick(item)">查看中奖名单</div>
                  <div class="lookActDiv" @click="goActClick(item)">查看活动</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sku">
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="gotoSkuPage(item.skuId)" v-click-track="'qg'">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns">
            <div class="price">￥{{ item.jdPrice }}</div>
          </div>
        </div>
      </div>
      <div class="more-btn-all">
        <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
      </div>
    </div>
  </div>
 <div>
   <!-- 活动门槛 -->
   <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
   <!-- 规则弹窗 -->
   <VanPopup teleport="body" v-model:show="showRule" position="center">
     <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
   </VanPopup>
   <van-popup v-model:show="showQueryConditionPicker" position="bottom">
     <van-picker :columns="multipleOptions" @confirm="confirmGender" @cancel="showQueryConditionPicker = false" />
   </van-popup>
   <VanPopup teleport="body" v-model:show="isShowActivityDetail" position="center">
     <ActivityDetail v-if="isShowActivityDetail" :itemActData="itemActData" @close="isShowActivityDetail = false"></ActivityDetail>
   </VanPopup>
   <VanPopup teleport="body" v-model:show="isShowWinnerRecord" position="center">
     <WinnerRecord v-if="isShowWinnerRecord" :showArr="showArr" :currentTab="currentTab" :subActivityId="subActivityId" :subActivityName="subActivityName" @close="isShowWinnerRecord = false"></WinnerRecord>
   </VanPopup>
 </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject, onMounted } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import ActivityDetail from '../components/ActivityDetail.vue';
import WinnerRecord from '../components/WinnerRecord.vue';
import Threshold2 from '@/components/Threshold2/index.vue';
import useThreshold from '@/hooks/useThreshold';
import Swiper, { Autoplay, Pagination, Navigation } from 'swiper';
import 'swiper/swiper.min.css';

Swiper.use([Autoplay, Pagination, Navigation]);
const currentTab1 = ref(0);
const isShowWinnerRecord = ref(false);
const isShowActivityDetail = ref(false);
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const userInfo = inject('userInfo') as any;
const shopName = ref(baseInfo.shopName);
const showRule = ref(false);
const ruleTest = ref('');
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};

const showLimit = ref(false);
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
const currentTab = ref(2); // 当前是选怎活动还是导入活动的tab项
const sortName = ref(''); // 选择活动的活动名称
const importSortName = ref(''); // 导入活动的活动名称
const moduleDesc = ref('以当前登录的京东用户身份信息作为查询依据中奖结果');
const importModuleDesc = ref('以当前登录的京东用户身份信息作为查询依据中奖结果');
// 条件查询
const showQueryConditionPicker = ref(false);
const multipleOptions = ref([{}]); // 被选择的查询条件数据
const showArr = ref([]); // 显示中奖名单的条件
const queryConditionName = ref(multipleOptions.value[0].text); // 选中的查询条件的显示只
const queryConditionNameValue = ref(); // 选中的查询条件的value
const activityListData = ref([]); // 活动列表数据
const itemActData = ref(null); // 查看活动的活动数据
const subActivityId = ref(0); // 查看中奖名单的子活动的id
const subActivityName = ref(''); // 查看中奖名单的子活动的活动名称
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});
const mySwiper2 = ref(null);
nextTick(() => {
  if (mySwiper2.value) {
    mySwiper2.value.destroy(false);
  }
  mySwiper2.value = new Swiper('.swiper', {
    autoplay: false,
    loop: false,
    slidesPerView: 1,
    loopedSlides: 1,
    allowTouchMove: true,
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
    // pagination: {
    //   el: '.swiper-pagination',
    //   type: 'bullets',
    //   clickable: true, // 如果需要分页器是可点击的
    // },
  });
  mySwiper2.value.navigation.update();
  console.log(mySwiper2.value, 'mySwiper2.value');
});
const paginationClick = (index) => {
  console.log('点击分页器');
  currentTab1.value = index;
};
const confirmGender = ({ selectedOptions }: { selectedOptions: { text: string } }) => {
  queryConditionName.value = selectedOptions[0]?.text;
  queryConditionNameValue.value = selectedOptions[0]?.value;
  showQueryConditionPicker.value = false;
};
// 展开和收起的点击
const showClick = (itemData) => {
  itemData.isShowAllMessage = !itemData.isShowAllMessage;
};
const queryContent = ref(''); // 查询条件

// 查看中奖名单
const queryWinnerClick = (itemData) => {
  console.log('查看中奖名单');
  subActivityId.value = itemData.activityId;
  subActivityName.value = itemData.activityName;
  isShowWinnerRecord.value = true;
};
// 前往活动
const goActClick = (itemData:any) => {
  console.log(itemData, '前往活动');
  isShowActivityDetail.value = true;
  itemActData.value = itemData;
};
const timeOutName = ref(null);
// 校验活动有效
const checkTime = () => {
  // console.log('校验有效期');
  activityListData.value.forEach((itemData) => {
    const currentDate = dayjs().valueOf();
    if (itemData.validityStartTime) {
      if (dayjs(currentDate).isBefore(dayjs(itemData.validityStartTime))) {
        itemData.showAct = false; // 是否显示
      }
    }
    if (itemData.validityEndTime) {
      if (dayjs(currentDate).isAfter(dayjs(itemData.validityEndTime))) {
        itemData.showAct = false; // 是否显示
      }
    }
  });
  const aa2 = activityListData.value.filter((e) => !e.showAct);
  if (aa2.length === activityListData.value.length) {
    clearTimeout(timeOutName);
  }
};
// 查询活动
const getExportActivity = async () => {

  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/91005/exportActivity', {
      status: queryContent.value ? 0 : 1,
      [queryConditionNameValue.value]: queryContent.value,
    });
    console.log(data, '中奖列表查询');
    closeToast();
    // closeToast();
    data.activityResponses.forEach((itemData) => {
      itemData.showAct = true; // 默认全部在有效期内
      if (!itemData.validityStartTime && !itemData.validityEndTime) {
        itemData.noCheckDate = true; // 不需要校验有效期
        itemData.showAct = true; // 根据有效期判断是否展示活动判断
      } else {
        itemData.noCheckDate = false; // 需要校验有效期
      }
      itemData.showJoinAct = true;
      if (itemData.linkUrl) {
        const reg = new RegExp(/^(http:\/\/|https:\/\/)(.*\.)?(isvjcloud\.com|isvjd\.com|jd\.com)(\/.*)?(\?.*)?$/);
        if (!itemData.linkUrl || !reg.test(itemData.linkUrl)) {
          itemData.showJoinAct = false; // 不显示参与活动
        }
      }
      itemData.isShowAllMessage = false; // 是否展开
    });
    const arr1 = data.activityResponses.filter((itemData) => itemData.noCheckDate);
    if (arr1.length < data.activityResponses.length) {
      data.activityResponses.forEach((itemData) => {
        const currentDate = dayjs().valueOf();
        if (itemData.validityStartTime || itemData.validityEndTime) {
          if (dayjs(currentDate).isBefore(dayjs(itemData.validityStartTime)) || dayjs(currentDate).isAfter(dayjs(itemData.validityEndTime))) {
            // 未到有效期
            itemData.showAct = false; // 是否显示
          }
        }
      });
      const aa1 = activityListData.value.filter((e) => !e.showAct);
      if (aa1.length !== activityListData.value.length) {
        timeOutName.value = setInterval(() => {
          checkTime();
        }, 1000);
      }
    }
    activityListData.value = data.activityResponses.filter((item) => item.showAct);
    // console.log(activityListData.value, 'activityListData.value');
  } catch (error) {
    console.error(error);
    closeToast(error.message);
  }
};
// 查询导入活动的查询条件
const getTemplateList = async () => {
  try {
    const { data } = await httpRequest.post('/91005/templateList');
    const templateQueryNew = data.filter((item) => item.query);
    templateQueryNew.forEach((itemData) => {
      itemData.text = itemData.aliasName;
      itemData.value = itemData.columnValue;
    });
    queryConditionName.value = templateQueryNew[0].text;
    queryConditionNameValue.value = templateQueryNew[0].value;
    console.log(templateQueryNew, queryConditionName.value, '查询导入活动的查询条件1111');
    multipleOptions.value = templateQueryNew;
    showArr.value = data.filter((item) => item.show);
    // console.log(data, '查询导入活动的查询条件');
  } catch (error) {
    console.error(error);
  }
};

// 查询导入的活动
const queryActClick = async () => {
  console.log('查询导入的活动');
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  if (!queryContent.value || !queryContent.value.trim()) {
    showToast('请输入查询条件');
    return;
  }
  await getExportActivity();
};
const getActivity = async () => {
  if (baseInfo.thresholdResponseList.length) {
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/91005/activity', {});
    closeToast();
    console.log(data, '选择中奖列表查询');
    data.activityResponses.forEach((itemData) => {
      itemData.showAct = true; // 默认全部在有效期内
      if (!itemData.validityStartTime && !itemData.validityEndTime) {
        itemData.noCheckDate = true; // 不需要校验有效期 及时刷新 可以不用
        itemData.showAct = true; // 判断有效期
      } else {
        itemData.noCheckDate = false; // 需要校验有效期
      }
      itemData.showJoinAct = true;
      if (itemData.linkUrl) {
        const reg = new RegExp(/^(http:\/\/|https:\/\/)(.*\.)?(isvjcloud\.com|isvjd\.com|jd\.com)(\/.*)?(\?.*)?$/);
        if (!itemData.linkUrl || !reg.test(itemData.linkUrl)) {
          itemData.showJoinAct = false; // 不显示参与活动
        }
      }
      itemData.isShowAllMessage = false; // 是否展开
    });
    const arr1 = data.activityResponses.filter((itemData) => itemData.noCheckDate);
    if (arr1.length < data.activityResponses.length) {
      data.activityResponses.forEach((itemData) => {
        const currentDate = dayjs().valueOf();
        if (itemData.validityStartTime || itemData.validityEndTime) {
          // console.log(itemData.actvityName, dayjs(currentDate), dayjs(itemData.validityStartTime), dayjs(itemData.validityEndTime), 'sadddddddd');
          if (dayjs(currentDate).isBefore(dayjs(itemData.validityStartTime)) || dayjs(currentDate).isAfter(dayjs(itemData.validityEndTime))) {
            // 未到有效期
            itemData.showAct = false; // 是否显示
          }
        }
      });
      const aa1 = activityListData.value.filter((e) => !e.showAct);
      // console.log(aa1, 'activityListData.value1111111111');
      if (aa1.length < activityListData.value.length) {
        timeOutName.value = setInterval(() => {
          checkTime();
        }, 1000);
      }
    }
    activityListData.value = data.activityResponses.filter((item) => item.showAct);
    // console.log(arr1, activityListData.value, 'activityListData.value');
  } catch (error) {
    console.error(error);
    closeToast(error.message);
  }
};
// 活动tab切换
const selectQueryClick = async (type) => {
  currentTab.value = type;
  queryContent.value = '';
  activityListData.value = [];
  if (type === 1) {
    if (baseInfo.thresholdResponseList.length) {
      showLimit.value = useThreshold({
        thresholdList: baseInfo.thresholdResponseList,
      });
      return;
    }
    await getActivity();
  } else {
    await getTemplateList();
    await getExportActivity();
  }
};
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
const isShowPhone = ref(false);
// 查询有几个tab
const getMainInfo = async () => {
  try {
    const { data } = await httpRequest.post('/91005/mainInfo', {});
    // console.log(data, '查询有几个tab');
    if (data.temInfoList.length === 2) {
      currentTab.value = 1;
      sortName.value = data.temInfoList[0].moduleName;
      importSortName.value = data.temInfoList[1].moduleName;
      moduleDesc.value = data.temInfoList[0].moduleDesc;
      importModuleDesc.value = data.temInfoList[1].moduleDesc;
      await getActivity();
      await getTemplateList();
    } else if (data.temInfoList[0].moduleType === '导入活动') {
      currentTab.value = 2;
      importSortName.value = data.temInfoList[0].moduleName;
      importModuleDesc.value = data.temInfoList[0].moduleDesc;
      await getTemplateList();
      await getExportActivity();
    } else {
      currentTab.value = 1;
      sortName.value = data.temInfoList[0].moduleName;
      moduleDesc.value = data.temInfoList[0].moduleDesc;
      await getActivity();
    }
  } catch (error) {
    console.error(error);
  }
};
const getExposureSkuPage = async () => {
  try {
    const { data } = await httpRequest.post('/91005/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error) {
    console.error(error);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getExposureSkuPage();
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getMainInfo(), getExposureSkuPage()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();

</script>

<style lang="scss">
.swiperAll{
  background-color:red;
  .swiper{
    //background:red;
    --swiper-theme-color: #ff6600;
    --swiper-pagination-color: #00ff33;/* 两种都可以 */
    padding-bottom:0.24rem;
    --swiper-navigation-color: #00ff33;/* 单独设置按钮颜色 */
    --swiper-navigation-size: 30px;/* 设置按钮大小 */

  }
  //.swiper-wrapper{
  //  display:flex;
  //}
  .swiper-slide{
    background-color:green;
    border-radius: 0.35rem;
    //width:1rem !important;
    //flex:1 !important;
  }
  .swiper-pagination {
    position: initial;
    /* bottom: 0; */
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-top: 0.2rem;
  }
  .swiper-pagination-bullet {
    width: 0.1rem;
    height: 0.1rem;
    background: #ffffff;
    border-radius: 50%;
    opacity: 1;
    margin:0 4px;
  }

  .swiper-pagination-bullet-active {
    width: 1rem;
    height: 0.1rem;
    background: #ffffff;
    border-radius: 0.05rem;
    margin:0 4px;
  }
}
#footer{
  display:none !important;
}
.van-field__control{
  text-align: right;
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color: #f7f7f7;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.44rem;
    height: 0.47rem;
    cursor: pointer;
    margin-top:2.14rem;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
.messageDivAll{
  padding:0.26rem 0.48rem;
  display:flex;
  align-items: center;
  justify-content: space-between;
  .leftDiv{
    display:flex;
    align-items: center;
    .logoDiv{
      width:0.81rem;
      height:0.81rem;
      border-radius: 50%;
      background-color: #7cdcf6;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .nickNameDiv{
      font-size:0.3rem;
      color: #000;
      margin-left:0.24rem;
    }
  }
  .phoneDiv{
    font-size:0.24rem;
    color: #000;
  }
}
.queryActMessDivAll{
  .titleDivAll{
    display:flex;
    margin: 0 0.2rem;
    align-items: flex-end;
    //justify-content: center;
    box-sizing: border-box;
    .selectDiv{
      width:3.8rem;
      height:0.92rem;
      border-radius: 0.35rem 0.35rem 0 0;
      border: 0.04rem solid #fffe94;
      background-color: #fff;
      border-bottom-color: transparent;
      font-size:0.36rem;
      color:#000;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      .selectImgDiv{
        width:1.67rem;
        height:0.36rem;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/267884/30/27656/3384/67c69d9fF38bbc9cb/1b2c0a5f946bcbfa.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        position: absolute;
        bottom: 0;
      }
      .selectTextDiv{
        position: relative;
        z-index: 10;
        font-weight: bold;
      }
    }
    .noSelectDiv{
      width:3.31rem;
      height:0.71rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/255704/1/28563/3690/67c69da0F2c9599a5/1b37af7fec1daa60.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      font-size:0.36rem;
      color:#000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .queryContentDivAll{
    border-radius: 0 0 0.35rem 0.35rem;
    border: 0.04rem solid #fffe94;
    background-color: #fff;
    border-top-color: transparent;
    padding-top:0.3rem;
    padding-bottom:0.24rem;
    margin:0 0.2rem;
    box-sizing: border-box;
    .promptDiv{
      font-size:0.24rem;
      color:#000;
      text-align: center;
      margin-bottom:0.24rem;
    }
    .queryConditionDivAll{
      display: flex;
      align-items: center;
      justify-content: center;
      width:100%;
      padding: 0 0.26rem 0 0.18rem;
      margin-top:0.24rem;
      .selectNameDiv{
        height: 0.87rem;
        border-radius: 0.24rem;
        border: none;
        width:2rem;
        background-color:#fff;
        box-shadow: -0.007rem 0.059rem 0.098rem 0.002rem rgba(222, 222, 222, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size:0.2rem;
        color:#000;
        .vanIconClass{
          margin-left:0.2rem;
        }
      }
      .inputClass{
        width:4.62rem;
        background-color:#f3f3f3;
        height: 0.87rem;
        border-radius: 0.24rem;
        border: none;
        text-align: right;
        padding-right: 0.24rem;
        font-size:0.2rem;
        color:#999999;
      }
    }
    .queryBtnDiv{
      width:6.47rem;
      height:1.09rem;
      background-image:url('//img10.360buyimg.com/imgzone/jfs/t1/259860/18/27784/2156/67c6927dF927816f5/36e1ff03d252ac83.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-left: calc(50% - 6.47rem / 2);
      margin-top:0.46rem;
      margin-bottom:0.24rem;
    }
  }
  .queryResultDivAll{
    //padding-bottom:0 0 0.24rem 0;
    .queryItemDivAll{
      background-color:#f4f4f4;
      border-radius: 0.24rem;
      margin:0 0.24rem 0.1rem 0.24rem;
      min-height:1.07rem;
      padding-bottom:0.24rem;
      .topDiv{
        display:flex;
        justify-content: space-between;
        align-items: center;
        padding-right:0.24rem;
        .actNameDiv{
          width:3.12rem;
          height:0.63rem;
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/264582/24/27732/3737/67c6ae59F8f99a8ea/7b4647f7bcbe011b.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          font-size:0.24rem;
          color:#000;
          font-weight: bold;
          display: flex;
          align-items: center;
          padding-left: 0.2rem;
          .actNameDiv1{
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 2.5rem;
          }
        }
        .prizeStatusDiv{
          font-size:0.24rem;
        }
      }
      .showMessageTextDiv{
        text-align: center;
        font-size:0.24rem;
        font-weight: bold;
      }
      .activityMessageDiv{
        .noDrawDiv{
          font-size:0.28rem;
          text-align: center;
          margin-top:0.48rem;
          margin-bottom:0.4rem;
        }
        .itemActPrizeListDiv{
          display:flex;
          flex-direction: column;
          align-items: center;
          padding:0 0.2rem 0 0.2rem;
          max-height: 5rem;
          overflow-y: scroll;
          .itemActPrizeItemDiv{
            margin: 0 0 0.15rem 0;
            padding: 0.3rem 0.28rem;
            background-color:#fff;
            width:100%;
            border-radius: 0.24rem;
            display:flex;
            justify-content: space-between;
            align-items: center;
            .dateDiv{
              font-size:0.24rem;
            }
            .prizeStatus{
              font-size:0.24rem;
              text-decoration: underline;
              text-underline-offset: 0.1rem;
            }
            .prizeName{
              max-width:2rem;
              white-space: nowrap; /* 禁止文本换行 */
              overflow: hidden;    /* 隐藏超出容器的内容 */
              text-overflow: ellipsis; /* 当内容超出时，使用省略号表示 */
            }
          }
        }
        .itemBtnDiv{
          display:flex;
          font-size:0.28rem;
          color:#ffffff;
          justify-content: flex-end;
          padding-right:0.2rem;
          margin-top:0.18rem;
          .winnerRecordDiv{
            background-color:#f5790a;
            border-radius: 0.4rem;
            width:2.16rem;
            height: 0.64rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right:0.13rem;
          }
          .lookActDiv{
            background-color:#f5790a;
            border-radius: 0.4rem;
            width:2.16rem;
            height: 0.64rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}
.origin{
  color:#f5790a;
}
.gray{
  color:#616161;
}
.blue{
  color:#1890ff;
}
.black{
  color:#000;
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .more-btn-all {
    width:6.9rem;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#1890ff), to(#1890ff));
      background: linear-gradient(90deg, #1890ff 0%, #1890ff 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    max-height: 12rem;
    overflow-y: scroll;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 0.27rem;
        color: #262626;
        //height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0 0;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
        background-size: 100%;
        margin: 0 auto 0;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.24rem;
          color: #e2231a;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
