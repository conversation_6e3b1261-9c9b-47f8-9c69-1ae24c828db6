<template>
  <div class="count-down-time" :style="furnishStyles.cutDownColor.value">
    <span class="title" v-if="props.wishPeriod === 1">距离许愿开始还有：</span>
    <span class="title" v-else-if="props.wishPeriod === 2">距离开奖开始还有：</span>
    <span class="title" v-else-if="props.wishPeriod === 3">距离开奖结束还有：</span>
    <van-count-down v-if="props.wishPeriod === 1" :time="props.startTime - dayjs().valueOf()" format="DD:HH:mm:ss" @finish="countDownFinish">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.days }}</div>
          <span :style="{'color':furnish.cutDownColor}">天</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.hours }}</div>
          <span :style="{'color':furnish.cutDownColor}">时</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.minutes }}</div>
          <span :style="{'color':furnish.cutDownColor}">分</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.seconds }}</div>
          <span :style="{'color':furnish.cutDownColor}">秒</span>
        </div>
      </template>
    </van-count-down>
    <van-count-down v-else-if="props.wishPeriod === 2" :time="props.startTime - dayjs().valueOf()" format="DD:HH:mm:ss">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.days }}</div>
          <span :style="{'color':furnish.cutDownColor}">天</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.hours }}</div>
          <span :style="{'color':furnish.cutDownColor}">时</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.minutes }}</div>
          <span :style="{'color':furnish.cutDownColor}">分</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.seconds }}</div>
          <span :style="{'color':furnish.cutDownColor}">秒</span>
        </div>
      </template>
    </van-count-down>
    <van-count-down v-else-if="props.wishPeriod === 3" :time="props.startTime - dayjs().valueOf()" format="DD:HH:mm:ss">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.days }}</div>
          <span :style="{'color':furnish.cutDownColor}">天</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.hours }}</div>
          <span :style="{'color':furnish.cutDownColor}">时</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.minutes }}</div>
          <span :style="{'color':furnish.cutDownColor}">分</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.seconds }}</div>
          <span :style="{'color':furnish.cutDownColor}">秒</span>
        </div>
      </template>
    </van-count-down>
<!--    <span class="title" v-if="props.isStart">距离开奖开始还有：</span>-->
<!--    <span class="title" v-else>距开奖结束还有：</span>-->
<!--    <van-count-down :time="!props.isStart ? props.endTime - dayjs().valueOf() : props.startTime - dayjs().valueOf()" format="DD:HH:mm:ss">-->
<!--      <template #default="timeData">-->
<!--        <div class="contentSpan">-->
<!--          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.days }}</div>-->
<!--          <span :style="{'color':furnish.cutDownColor}">天</span>-->
<!--          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.hours }}</div>-->
<!--          <span :style="{'color':furnish.cutDownColor}">时</span>-->
<!--          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.minutes }}</div>-->
<!--          <span :style="{'color':furnish.cutDownColor}">分</span>-->
<!--          <div class="acblockStyleStyle" :style="furnishStyles.cutDownNumColor.value">{{ timeData.seconds }}</div>-->
<!--          <span :style="{'color':furnish.cutDownColor}">秒</span>-->
<!--        </div>-->
<!--      </template>-->
<!--    </van-count-down>-->
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';
import dayjs from 'dayjs';

const props = defineProps({
  isStart: {
    type: Boolean,
    default: false,
    required: true,
  },
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
  wishPeriod: {
    type: Number,
    default: 0,
  },
});
const countDownFinish = () => {
  window.location.reload();
};
</script>

<style scoped lang="scss">
.count-down-time {
  width: 6.90rem;
  height: 0.78rem;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  padding-left: 0.89rem;
  margin-left:calc(50% - 6.9rem / 2);
  //margin-top:0.3rem;
  line-height: 0.78rem;
  justify-content: center;
  //color: #f2270c;
  .title {
    font-size: 0.22rem;
    line-height: 0.22rem;
  }
  .contentSpan {
    //margin-left: 0.89rem;
    display: flex;
    //color: #fff;
    .acblockStyleStyle {
      width: 0.4rem;
      height: 0.4rem;
      border-radius: 0.05rem;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
      background-color: #fdefb5;
    }
    span {
      width: 0.4rem;
      height: 0.4rem;
      color: #fff;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
