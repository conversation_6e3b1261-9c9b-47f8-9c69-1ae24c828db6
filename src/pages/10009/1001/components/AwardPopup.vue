<template>
  <div class="bk" v-if="prize.prizeType > 0">
<!--    <img :src="prize.prizeImg" alt="" class="prize-img" />-->
    <div class="content">
      <div class="wishCardDiv">许愿卡编号：{{prize.cardId}}</div>
      <div class="prizeLevelDiv">中了{{prize.prizeLevel}}</div>
      <div class="prize-name">奖品为{{ prize.prizeName }}</div>
      <div>
        <div class="p3" v-if="prize.prizeType === 3">奖品为实物奖品，请尽快填写地址</div>
        <div class="p3" v-else-if="prize.prizeType === 7">奖品为实礼品卡，请在我的奖品查看详情</div>
        <div class="p3" v-else>已经领取成功</div>
      </div>
      <div class="prizeImgDiv">
        <img :src="prize.prizeImg" alt="" />
      </div>
      <div class="btn-list">
        <div class="btnDiv" v-if="prize.prizeType === 3" @click="saveAddress"></div>
        <div class="btnDiv1" v-else @click="close"></div>
      </div>
    </div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
  prizeContent: string;
  userReceiveRecordId: string;
  cardId: string;
  prizeLevel: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.userReceiveRecordId, props.prize.userPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, prizeImg: props.prize.prizeImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  const prizeContent = JSON.parse(props.prize.prizeContent);
  emits('savePhone', props.prize.userReceiveRecordId, prizeContent.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  width: 5.19rem;
  height: 7.59rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/176394/7/4247/265537/6077a717Ecf6b2947/2a09c49aee9e4d26.png);
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 1.2rem;
  .prize-img {
    height: 2rem;
    width: 2rem;
    margin: 0 auto;
  }
  .content {
    .wishCardDiv{
      color: #fff;
      text-align: center;
      font-size:0.24rem;
    }
    .prizeLevelDiv{
      color: #fff;
      text-align: center;
      font-size:0.36rem;
    }
    .prize-name {
      font-size: 0.24rem;
      margin: 0.2rem 0 0;
      text-align: center;
      color: #fff;
    }
    .p3 {
      font-size: 0.24rem;
      color: #fff;
      text-align: center;
      //height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0.1rem;
      span {
        color: #f2270c;
      }
    }
    .prizeImgDiv{
      width:2rem;
      height:2rem;
      margin:auto;
      margin-top:0.6rem;
      img{
        width:100%;
        height:100%;
      }
    }
    .btn-list{
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%);
      .btnDiv{
        width: 3.5rem;
        height: 0.8rem;
        //background:red;
      }
      .btnDiv1{
        width: 3.84rem;
        height: 1.02rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/174096/27/5646/19899/607e8a2aE59a2be98/b3cb54bbe1fedc19.png);
        background-size: 100%;
        background-repeat: no-repeat;
      }
    }
  }
}
.thanks-join {
  width: 6rem;
  height: 6.3rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/192914/3/22012/24033/623985b9E8508c48b/019e54628504b2dc.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 4.5rem;
  .close {
    height: 0.24rem;
    width: 0.24rem;
    position: absolute;
    right: 0.34rem;
    top: 0.34rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/143521/26/18654/387/5fd9e706E8f1594e3/ae5cc06440559585.png) no-repeat;
    background-size: 100% 100%;
  }
  .btn {
    display: block;
    margin: 0 auto;
    width: 4rem;
    height: 0.76rem;
    line-height: 0.76rem;
    color: #fff;
    font-size: 0.3rem;
    border-radius: 0.38rem;
    text-align: center;
    background-color: rgb(201, 0, 26);
  }
}
</style>
