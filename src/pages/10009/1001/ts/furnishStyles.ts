import { computed, reactive } from 'vue';

export const furnish = reactive({
// 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  // 活动规则
  ruleBg: '',
  // 我的奖品
  myPrizeBg: '',
  // 我的订单
  myOrderBg: '',
  // 倒计时背景
  cutDownBg: '',
  cutDownColor: '',
  cutDownNumBg: '',
  cutDownNumColor: '',
  cutDownUnitColor: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  myWishCardBtnBg: '', // 我的许愿卡按钮
  getWishCardTypeBg: '', // 获取许愿卡方式背景
  // 奖励背景图
  prizeBg: '', // 奖品列表背景
  hasGetWishCardBg: '', // 已获取的许愿卡背景
  backBtnBg: '', // 返回首页按钮
  prizeShowBg: '', // 奖品展示区背景
  winnerBg: '', // 获奖名单
  drawWishCardBg: '', // 中奖卡片图片
  noDrawWishCardBg: '', // 未中奖卡片图片
  wishCardBg: '', // 许愿卡默认图片
  wishCardBtnBg: '', // 许愿卡操作按钮背景
  wishCardBtnBgColor: '', // 许愿卡操作按钮文字颜色
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const cutDownColor = computed(() => ({
  color: furnish.cutDownColor ?? '',
  backgroundImage: furnish.cutDownBg ? `url("${furnish.cutDownBg}")` : '',
}));
const cutDownUnitColor = computed(() => ({
  color: furnish.cutDownUnitColor ?? '',
}));
const cutDownNumColor = computed(() => ({
  color: furnish.cutDownNumColor ?? '',
  backgroundColor: furnish.cutDownNumBg ?? '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const btnStyle = computed(() => ({
  color: furnish.wishCardBtnBgColor ?? '',
  backgroundColor: furnish.wishCardBtnBg ?? '',
}));

export default {
  pageBg,
  shopNameColor,
  cutDownColor,
  cutDownNumColor,
  cutDownUnitColor,
  btnStyle,
};
