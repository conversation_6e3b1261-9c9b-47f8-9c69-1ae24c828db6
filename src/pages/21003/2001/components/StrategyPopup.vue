<template>
  <div class="rule-bk">
    <div class="title">领取攻略</div>
    <div class="content">
      <div class="strategyPopupImg" :style="furnishStyles.strategyPopupImg.value"></div>
      <div class="i-know"  @click="close"></div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue';
import furnishStyles from '../ts/furnishStyles';

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.close {
  width: 0.45rem;
  height: 0.45rem;
  margin: 0 auto;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/226770/28/11313/1145/65969c51Fb9dda6aa/31b3866cf538306e.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/236810/34/9907/10566/65969c00F90424d43/759a43f2c99a47cb.png) no-repeat;
  background-size: 100% 100%;
  width: 6.52rem;
  height: 9.42rem;
  border-radius: 0.4rem;

  .title{
    font-size: 0.46rem;
    line-height: 0.61rem;
    padding-top: 0.4rem;
    text-align: center;
    margin: 0 auto;
    width: 2rem;
    background: linear-gradient(to right, #513c28, #8d6a35, #513c28);
    -webkit-background-clip: text;
    color: transparent; /* 隐藏文字本身的颜色 */
  }

  .content {
    height: 8rem;
    margin: 0 auto;
    padding: 0.2rem 0.35rem 0.3rem 0.4rem;
    font-size: 0.24rem;
    color: #262626;
    white-space: pre-wrap;
    .strategyPopupImg{
      background-repeat: no-repeat;
      margin: 1rem auto 0 auto;
      height: 4.1rem;
      background-size: 100%;
    }
  }
  .i-know{
    margin: 0.3rem auto 0 auto;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/249882/24/1924/2613/65969859Fb08240ac/83a6281546a611cc.png");
    background-size: 100%;
    background-repeat: no-repeat;
    width: 1.88rem;
    height: 0.5rem;
    position: relative;
    bottom: -1.3rem;
  }
}
</style>
