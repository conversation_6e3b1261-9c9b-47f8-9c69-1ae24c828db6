<template>
  <!--  申领前-->
  <div class="bg" v-if="!isSuccess" :style="furnishStyles.pageBg.value">
    <div class="header-kv" :style="{ backgroundImage: `url(${furnish.actBg})` }">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value">
        <span v-if="furnish.disableShopName === 1">{{ shopName }}</span>
      </div>
    </div>
    <div class="main">
      <div class="get-btn">即刻领取</div>
    </div>
  </div>
  <!--申领成功-->
  <div class="bg" v-if="isSuccess" :style="furnishStyles.successPageBg.value">
    <div class="header-kv" :style="{ backgroundImage: `url(${furnish.detailsKvImg})` }">
      <img :src="furnish.detailsKvImg" alt="" class="kv-img" />
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value">
        <span v-if="furnish.disableShopName === 1">{{ shopName }}</span>
      </div>
    </div>
    <div class="main">
      <div class="get-btn">返回首页</div>
    </div>
  </div>
  <!--活动规则-->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!--我的订单-->
  <VanPopup teleport="body" v-model:show="showOrderRecord">
    <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
  </VanPopup>
  <!--领取攻略-->
  <VanPopup teleport="body" v-model:show="showStrategyPopup">
    <StrategyPopup @close="showStrategyPopup = false"></StrategyPopup>
  </VanPopup>
</template>
<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import StrategyPopup from '../components/StrategyPopup.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const shopName = ref('');

const isLoadingFinish = ref(true);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
};
const prizeList = ref<Prize>(defaultStateList);

type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
const nextStateAmount = ref(0);

const showRule = ref(false);
const ruleTest = ref('');
const showAward = ref(false);
const showOrderRecord = ref(false);
const showStrategyPopup = ref(false);

// 活动规则相关
const showRulePopup = async () => {
  showRule.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 装修时选择框
const showSelect = ref(false);
// 装修展示页面  1主页 2成功页 3领取攻略
const activeKey = ref('1');
const isSuccess = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

// 页面截图
const isCreateImg = ref(false);
const tabNum = ref('0');
const createImg = async () => {
  showRule.value = false;
  showOrderRecord.value = false;
  isCreateImg.value = true;
  tabNum.value = activeKey.value;
  const interactC = document.getElementById('interact-c') as HTMLElement;
  useHtmlToCanvas(interactC).then(() => {
    activeKey.value = tabNum.value;
  });
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuList) {
    orderSkuList.value = data.orderSkuList;
  }
  ruleTest.value = data.rules;
  shopName.value = data.shopName;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

registerHandler('activeKey', (data: any) => {
  activeKey.value = data;
  if (activeKey.value === '3') {
    showStrategyPopup.value = true;
  }
  if (activeKey.value === '2') {
    isSuccess.value = true;
    showStrategyPopup.value = false;
  }
  if (activeKey.value === '1') {
    isSuccess.value = false;
    showStrategyPopup.value = false;
  }
});

onMounted(() => {
  if (activityData) {
    prizeList.value = activityData.prizeList;
    ruleTest.value = activityData.rules;
    orderSkuList.value = activityData.orderSkuList;
    // endTime.value = dayjs(activityData.endTime).valueOf();
    nextStateAmount.value = prizeList.value[0].stepAmount || 0;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss"></style>
<style lang="scss" scoped>
.bg {
  position: relative;
  background-size: 100%;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  background-size: 100%;
  background-repeat: no-repeat;
  .kv-img {
    width: 100%;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .btn-list {
    position: absolute;
    top: 0.7rem;
    right: 0;
    img {
      width: 0.47rem;
      margin-bottom: 0.18rem;
    }
  }
  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.main {
  position: absolute;
  left: 0;
  right: 0;
  top: 12.6rem;
  .get-btn {
    background-color: #444444;
    margin: 0 auto;
    width: 2.1rem;
    height: 0.66rem;
    border-radius: 0.1rem;
    font-size: 0.27rem;
    color: #fff;
    line-height: 0.66rem;
    text-align: center;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
<style>
#footer,
.notice-marquee-context {
  display: none !important;
}
</style>
