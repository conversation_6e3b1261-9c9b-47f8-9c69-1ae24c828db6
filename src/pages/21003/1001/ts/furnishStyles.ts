import { computed, reactive } from 'vue';

export const furnish = reactive({
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 申领成功页面背景图
  successPageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 礼品介绍图
  introductionImg: '',
  // 申领成功介绍图
  giftIntroductionImg: '',
  // 活动规则图
  ruleImg: '',
  // 我的订单图标
  recordImg: '',
  // 领取攻略图标
  strategyImg: '',
  // 活动详情页主图
  detailsKvImg: '',
  // 活动详情页颜色
  detailsBgColor: '',
  // 攻略图
  strategyPopupImg: '',
  // 店铺名称颜色
  shopNameColor: '',
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const successPageBg = computed(() => ({
  backgroundColor: furnish.detailsBgColor ?? '',
  backgroundImage: furnish.successPageBg ? `url("${furnish.successPageBg}")` : '',
}));

const introductionImg = computed(() => ({
  backgroundImage: furnish.introductionImg ? `url(${furnish.introductionImg})` : '',
}));

const giftIntroductionImg = computed(() => ({
  backgroundImage: furnish.giftIntroductionImg ? `url(${furnish.giftIntroductionImg})` : '',
}));

const strategyPopupImg = computed(() => ({
  backgroundImage: furnish.strategyPopupImg ? `url(${furnish.strategyPopupImg})` : '',
}));

const detailsBgColor = computed(() => ({
  backgroundColor: furnish.detailsBgColor ?? '',
}));
const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));
export default {
  pageBg,
  successPageBg,
  introductionImg,
  giftIntroductionImg,
  detailsBgColor,
  strategyPopupImg,
  shopNameColor,
};
