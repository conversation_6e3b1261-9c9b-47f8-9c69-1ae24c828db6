<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="!showGameInit">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>

        <div>
          <div class="header-btn" :style="furnishStyles.ruleBtn.value" @click="showRulePopup" v-click-track="'hdgz'"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.prizeBtn.value" @click="showMyPrize = true" v-click-track="'wdjp'"><div>我的奖品</div></div>
          <div v-if="tasks.length > 0 && tasks.some((item) => item.taskType == 15)" class="header-btn" :style="furnishStyles.shareFriendBtn.value" @click="showShareFriendPop = true" v-click-track="'yqhy'">
            <div>邀请好友</div>
          </div>
          <div class="header-btn" :style="furnishStyles.drawRecordBtn.value" @click="showDrawRecordPop = true" v-click-track="'cjju'"><div>抽奖记录</div></div>
        </div>
      </div>
    </div>
    <div class="gameAllDiv" :style="furnishStyles.gameBg.value">
      <div class="topRightStyle" :style="furnishStyles.moreGameColor.value" v-threshold-click="getMoreGameClick">获取更多游戏机会>></div>
      <div class="bottomBtnAll">
        <div class="startGameDiv" :style="furnishStyles.gameBtn.value" v-threshold-click="startGame">
          开始游戏
          <div :class="[gameChanceNum >= 100 ? 'gameCountDiv1' : 'gameCountDiv']">{{ gameChanceNum }}</div>
        </div>
        <div class="startDrawDiv" :style="furnishStyles.gameDrawBtn.value" v-threshold-click="startDraw">
          立即抽奖
          <div :class="[chanceNum >= 100 ? 'drawCountDiv1' : 'drawCountDiv']">{{ chanceNum }}</div>
        </div>
      </div>
    </div>
    <div class="prizeListDiv">
      <div class="prizeTitleDiv" :style="furnishStyles.prizeShowTitle.value"></div>
      <div class="prizeAll" v-if="prizeList.length > 0">
        <div class="itemDiv" v-for="(item, index) in prizeList" :key="index">
          <div class="prizeImage">
            <img :src="item.prizeImg" alt="" />
            <div class="backDiv">{{ PRIZE_TYPE[item.prizeType] }}</div>
          </div>
          <div class="prizeMessage">
            <div class="prizeName">{{ item.prizeName }}</div>
            <div class="prizeRestDiv">
              奖品剩余<span>{{ item.surplusNum }}份</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sku" v-if="skuList.length > 0">
      <img class="title-img" :src="furnish.winnersBg" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns">
            <div class="price">￥{{ item.jdPrice }}</div>
            <div class="to-bug" @click="gotoSkuPage(item.skuId)">抢购</div>
          </div>
        </div>
      </div>
    </div>
    <div class="load-more" @click="handleLoadMore" v-if="skuList.length">加载更多</div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <!-- 游戏弹窗 -->
  <!--  <VanPopup teleport="body" v-model:show="showGameInit" position="bottom">-->
  <GameInit v-if="showGameInit" :gameChanceNum="gameChanceNum"></GameInit>
  <!--  </VanPopup>-->
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"> </MyPrize>
  </VanPopup>
  <!--  抽奖记录弹窗-->
  <VanPopup teleport="body" v-model:show="showDrawRecordPop" position="bottom">
    <DrawRecordPop v-if="showDrawRecordPop" @close="showDrawRecordPop = false"> </DrawRecordPop>
  </VanPopup>
  <!--  邀请好友记录-->
  <VanPopup teleport="body" v-model:show="showShareFriendPop" position="bottom">
    <ShareFriends v-if="showShareFriendPop" @close="showShareFriendPop = false"> </ShareFriends>
  </VanPopup>
  <!-- 做任务弹窗  -->
  <VanPopup teleport="body" v-model:show="showTask" position="bottom">
    <DoTask :times="gameChanceNum" :tasks="tasks" @close="showTask = false" :shopId="baseInfo.shopId" @refreshTask="refreshTask" @openShowGoShop="showGoShop = true"></DoTask>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup @openShowGoShop="showGoShop = true" :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"> </SavePhone>
  </VanPopup>

  <VanPopup teleport="body" position="center" close-on-click-overlay v-model:show="showWheelPop" @click-overlay="wheelClick">
    <lz-lucky-wheel ref="myLucky" width="90vw" height="90vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
  </VanPopup>
  <!-- 进店逛逛 -->
  <VanPopup teleport="body" v-model:show="showGoShop" position="bottom" z-index="10000">
    <GoShopPop v-if="showGoShop" @close="showGoShop = false"></GoShopPop>
  </VanPopup>
  <!--  <VanPopup teleport="body" v-model:show="showWheelPop" >-->
  <!--    <LuckWheel @close="showWheelPop = false" @startCallback="startCallback" @endCallback="endCallback"></LuckWheel>-->
  <!--  </VanPopup>-->
</template>

<script setup lang="ts">
import { ref, reactive, provide, computed, nextTick, inject, onMounted } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import DoTask from '../components/DoTask.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import GoShopPop from '../components/GoShopPop.vue';
import GameInit from '../components/GameInit.vue';
import LuckWheel from '../components/LuckWheel.vue';
import CopyCard from '../components/CopyCard.vue';
import DrawRecordPop from '../components/drawRecord.vue';
import ShareFriends from '../components/ShareFriends.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { Task, CardType, PRIZE_TYPE, Prize } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { LuckyWheel } from 'iamor-lottery-vue';
import { gotoSkuPage } from '@/utils/platforms/jump';

import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);
const showGameInit = ref(false); // 是否显示游戏初始页面

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;

const shopName = ref(baseInfo.shopName);
const showDrawRecordPop = ref(false);
const showShareFriendPop = ref(false);

const showRule = ref(false);
const ruleTest = ref('');
const delayToast = async (e: string) => {
  setTimeout(() => {
    showToast(e);
  }, 1000);
};
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
const pageNum = ref(1);
// 抽奖次数
const chanceNum = ref(0);
// 进店逛逛
const showGoShop = ref(false);
// 游戏次数
const gameChanceNum = ref(0);

const showMyPrize = ref(false);

const tasks = reactive([] as Task[]);
const showTask = ref(false);

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}
const handler = Handler.getInstance();
onMounted(() => {
  handler.on('onGoShopOpen', () => {
    showGoShop.value = true;
  });
});
const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/80059/chanceNum');
    chanceNum.value = data;
  } catch (error) {
    console.error(error);
  }
};
// 获取游戏次数
const getGameChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/80059/gameChanceNum');
    gameChanceNum.value = data;
  } catch (error) {
    console.error(error);
  }
};

const myLucky = ref();
// 抽奖接口
const lotteryDraw = async () => {
  try {
    const res = await httpRequest.post('/80059/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      myLucky.value.stop(index);
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      myLucky.value.stop(index);
    }
  } catch (error) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    myLucky.value.stop(index);
    console.error(error);
  }
  getChanceNum();
};
const startCallback = async () => {
  lzReportClick('kscj');
  if (chanceNum.value <= 0) {
    showToast('您的抽奖次数已用完');
    return;
  }
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  lotteryDraw();
};
const showWheelPop = ref(false); // 大转盘弹窗
// 关闭大转盘弹窗
const wheelClick = () => {
  showWheelPop.value = false;
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
  showWheelPop.value = false; // 关闭大转盘弹窗
};

// 奖品展示
const prizeList = ref([] as Prize[]);

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/80059/getPrizes');
    data.forEach((items: any) => {
      if (items.prizeImg) {
        prizeList.value.push(items);
      }
    });
    prizeInfo.splice(0);
    prizeInfo.push(...data);
  } catch (error) {
    console.error(error);
  }
};

// 获取任务列表
const getTask = async () => {
  try {
    const { data } = await httpRequest.post('/80059/getTask');
    tasks.splice(0);
    tasks.push(...data);
  } catch (error) {
    console.error(error);
  }
};
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
  skuId: string;
};
const skuList = ref<Sku[]>([]);

// // 获取曝光商品
// const getSkuList = async () => {
//   try {
//     const { data } = await httpRequest.post('/80059/getExpore', { type: 0 });
//     skuList.value = data as any[];
//   } catch (error) {
//     console.error(error);
//   }
// };
// 获取曝光商品
const getSkuList = async () => {
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
    type: 0,
  };
  try {
    const { data } = await httpRequest.post('/80059/getExpore', params);
    console.log(data);
    skuList.value = data.records as any[];
  } catch (error) {
    console.error(error);
  }
};
// 加载更多
const handleLoadMore = async () => {
  pageNum.value++;
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
    type: 0,
  };
  // console.log(params);
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 2000,
    overlay: true,
  });
  try {
    const { data } = await httpRequest.post('/80059/getExpore', params);
    console.log(data);
    if (data.records.length === 0) {
      showToast({
        message: '没有更多数据了',
        duration: 2000,
      });
      return;
    }
    skuList.value.push(...data.records);
    closeToast();
  } catch (error) {
    console.error(error);
    closeToast();
  }
};
// 判断任务完成情况 邀请助力和会员开卡
const checkTask = async () => {
  if (pathParams.shareId && pathParams.taskType === '15') {
    // 邀请助力
    try {
      const res = await httpRequest.post('/80059/invitationHelp/excute', { shareId: pathParams.shareId });
      if (res.code === 200) {
        await getTask();
        getGameChanceNum();
        delayToast('助力成功');
        lzReportClick('help');
      }
    } catch (error) {
      delayToast(error.message);
    }
  }
  if (pathParams.isTask) {
    // 会员有点击开卡任务判断是否是会员
    try {
      const res = await httpRequest.post('/80059/beMember/isMember', { shareId: pathParams.shareId });
      if (res.code === 200 && res.data) {
        // 是会员并且参与开卡任务则任务成功
        const res = await httpRequest.post('/80059/beMember/execute', { shareId: pathParams.shareId });
        if (res.code === 200) {
          await getTask();
          getGameChanceNum();
          delayToast('开卡成功');
        }
      }
    } catch (error) {
      delayToast(error.message);
    }
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getChanceNum(), getGameChanceNum(), getPrizes(), getSkuList()]);
    await getTask();
    await checkTask();
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();

// 做任务后刷新信息
const refreshTask = async () => {
  getGameChanceNum();
  getTask();
};
// 开始抽奖
const startDraw = () => {
  if (chanceNum.value <= 0) {
    showToast('没有抽奖次数,无法抽奖~');
    return;
  }
  showWheelPop.value = true;
};
// 获取更多抽奖机会
const getMoreGameClick = () => {
  showTask.value = true;
};
// 开始游戏按钮
const startGame = () => {
  if (gameChanceNum.value <= 0) {
    showToast('游戏机会已用完，做任务可获得更多游戏机会哦~');
    return;
  }
  showGameInit.value = true;
};
</script>

<style scoped lang="scss">
.van-popup--center {
  max-width: 100%;
}
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }
  .header-btn {
    background-repeat: no-repeat;
    background-size: 100% 100%;
    div {
      margin-top: -0.18rem;
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.18rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.gameAllDiv {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/145637/16/35945/51830/64494652F1dc1cc76/ca7503efaacf33a6.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.44rem;
  margin-left: calc(50% - 6.9rem / 2);
  margin-top: 0.32rem;
  position: relative;
  .topRightStyle {
    position: absolute;
    top: 0.8rem;
    right: 0.54rem;
    font-size: 0.24rem;
    color: rgb(0, 100, 16);
  }
  .bottomBtnAll {
    position: absolute;
    bottom: 0.8rem;
    font-size: 0.24rem;
    color: rgb(0, 100, 16);
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 0.54rem;
    .startGameDiv {
      position: relative;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/194463/10/24954/4588/62972077Ec82e1af3/e0762748518d3e12.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 2.8rem;
      height: 0.88rem;
      font-size: 0;
      .gameCountDiv {
        position: absolute;
        right: 0.3rem;
        top: -0.2rem;
        background: rgb(224, 31, 53);
        border: 2px solid rgb(255, 255, 255);
        border-radius: 50%;
        color: rgb(255, 242, 185);
        width: 0.42rem;
        height: 0.42rem;
        font-size: 0.24rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .gameCountDiv1 {
        position: absolute;
        right: 0.3rem;
        top: -0.2rem;
        background: rgb(224, 31, 53);
        border: 2px solid rgb(255, 255, 255);
        border-radius: 25%;
        color: rgb(255, 242, 185);
        //width:0.42rem;
        height: 0.42rem;
        font-size: 0.24rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .startDrawDiv {
      position: relative;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/4656/10/17854/20089/62972077E04eb7e42/bbd4e8c5088810b6.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 2.8rem;
      height: 0.88rem;
      font-size: 0;
      .drawCountDiv {
        position: absolute;
        right: 0.3rem;
        top: -0.2rem;
        background: rgb(224, 31, 53);
        border: 2px solid rgb(255, 255, 255);
        border-radius: 50%;
        color: rgb(255, 242, 185);
        width: 0.42rem;
        height: 0.42rem;
        font-size: 0.24rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .drawCountDiv1 {
        position: absolute;
        right: 0.3rem;
        top: -0.2rem;
        background: rgb(224, 31, 53);
        border: 2px solid rgb(255, 255, 255);
        border-radius: 25%;
        color: rgb(255, 242, 185);
        //width:0.42rem;
        height: 0.42rem;
        font-size: 0.24rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
.prizeListDiv {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: 0.4rem;
  flex-direction: column;
  align-items: center;
  .prizeTitleDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/52552/24/20820/57607/63f31a25F37254a4c/bab3564976b07479.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.9rem;
    height: 0.88rem;
  }
  .prizeAll {
    max-height: 7.32rem;
    overflow-y: scroll;
    .itemDiv {
      display: flex;
      position: relative;
      height: 2.4rem;
      width: 6.9rem;
      background: rgb(255, 255, 255);
      margin-bottom: 0.04rem;
      .prizeImage {
        width: 2rem;
        height: 2rem;
        margin: 0.2rem 0.32rem 0.2rem 0.2rem;
        border: 2px solid rgb(255, 54, 51);
        border-radius: 0.1rem;
        position: relative;
        overflow: hidden;
        img {
          width: 100%;
        }
        .backDiv {
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png');
          width: 2rem;
          height: 0.7rem;
          background-size: 100%;
          background-repeat: no-repeat;
          position: absolute;
          bottom: -2px;
          left: -2px;
          text-align: center;
          padding-top: 0.32rem;
          font-size: 0.24rem;
          color: #fff;
          box-sizing: border-box;
        }
      }
      .prizeMessage {
        .prizeName {
          text-align: left;
          color: rgb(38, 38, 38);
          font-size: 0.3rem;
          width: 3.4rem;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-top: 0.37rem;
        }
        .prizeRestDiv {
          text-align: left;
          color: rgb(140, 140, 140);
          font-size: 0.2rem;
          margin-top: 0.45rem;
        }
      }
    }
  }
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 0.27rem;
        color: #262626;
        //height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.2rem 0;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug {
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}
.load-more {
  width: 3rem;
  height: 0.6rem;
  line-height: 0.6rem;
  text-align: center;
  background: #fb6500;
  border-radius: 0.2rem;
  color: white;
  font-weight: 600;
  margin: 0 auto;
  font-size: 0.28rem;
}
.wheel {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}

.draws-num {
  text-align: center;
  font-size: 0.3rem;
  margin-bottom: 0.2rem;
}

.draw-btn {
  width: 4rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1.1rem;

  .winners-content {
    width: 6.6rem;
    height: 4.7rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem 0;
  border-bottom: 1px dashed rgb(230, 230, 230);

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
