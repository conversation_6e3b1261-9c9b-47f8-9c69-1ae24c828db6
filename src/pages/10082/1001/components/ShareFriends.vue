<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>邀请好友记录</div>
      <div class="rightLineDiv"></div>
      <img data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" alt=""/>
    </div>
    <div class="contentAll">
      <div class="formTitle"></div>
      <div class="formInfo" v-if="shareFriendList.length > 0">
        <div
            class="listAll"
            v-for="(item, index) in shareFriendList"
            :key="index"
        >
          <div class="infoLeft">
            <img :src="item.shareImg" alt="" />
            <span>{{ item.shareName }}</span>
          </div>
          <div class="infoRight">
              <span>
               {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
              </span>
          </div>
        </div>
      </div>
      <div class="formInfo1" v-if="shareFriendList.length == 0">
        暂无邀请记录~
      </div>
      <div class="closeDiv" @click="close()"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

interface ShreFriend {
  shareName: string;
  shareImg: string;
  createTime: number;
}
const shareFriendList = reactive([] as ShreFriend[]);

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const dataAll = await httpRequest.post('/10082/share');
    shareFriendList.splice(0);
    shareFriendList.push(...dataAll.data);
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }
  .contentAll{
    height:8rem;
    position: relative;
    z-index: 300;
    margin-top: 0.32rem;
    display:flex;
    flex-direction:column;
    align-items:center;
    .formTitle {
      width: 6.9rem;
      height: 0.7rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/150051/8/20972/3370/61e50505E2849291a/68c6072a645af1cf.png);
      background-size: 100%;
    }
    .formInfo {
      width: 6.9rem;
      height: 7rem;
      overflow-y: scroll;
      padding-bottom:0.32rem;
      .listAll {
        width: 6.9rem;
        height: 0.7rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/149468/31/21113/713/61e50764E68452264/93185c4bb006cda8.png);
        background-size: 100%;
        margin-top: 0.05rem;
        display: flex;

        .infoLeft {
          display: flex;
          align-items: center;
          height: 100%;
          width: 50%;
          font-size:0.24rem;
          img {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            margin-left: 0.2rem;
            margin-right: 0.2rem;
          }
        }
        .infoRight {
          display: flex;
          align-items: center;
          height: 100%;
          width: 50%;
          padding-left: 0.9rem;
          font-size:0.24rem;
        }
      }
    }
    .formInfo1{
      width: 100%;
      text-align: center;
      margin-top: 3rem;
    }
  }
}
</style>
