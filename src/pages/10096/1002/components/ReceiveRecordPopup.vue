<template>
  <div>
    <div class="myPrizePopStyle">
        <div class="closeDiv" @click="close"></div>
        <span class="tips">活动奖励将于确认收货后进行发放。</span>
        <div class="listContentDiv"
             v-if="prizeList.length > 0">
          <div class="itemDiv"
               v-for="(item, index) in prizeList" :key="index">
            <div class="prizeType">
              <div v-if="item.prizeType" class="spanName">
              <span v-if="item.prizeType == 2">京豆</span>
              <span v-else-if="item.prizeType == 1">优惠券</span>
              <span v-else-if="item.prizeType == 3">实物</span>

              <span v-else-if="item.prizeType == 4">积分</span>
              <span v-else-if="item.prizeType == 5">专享价</span>
              <span v-else-if="item.prizeType == 6">红包</span>
              <span v-else-if="item.prizeType == 7">礼品卡</span>
              <span v-else-if="item.prizeType == 8">京东E卡</span>
              <span v-else-if="item.prizeType == 9">京东plus会员卡</span>
              <span v-else-if="item.prizeType == 10">爱奇艺会员卡</span>
              <span v-else-if="item.prizeType == 12">京元宝权益</span>
              <span>{{ item.userReceiveRecordId ? item.userReceiveRecordId : '' }}</span>
            </div>
              <div v-if="item.prizeType == 3 && item.realName">
                 <span v-if="item.realName">收货人：{{item.realName }}</span>
              </div>
            </div>
            <div class="prizeBottomStyle">
              <div class="prizeLeftStyle">
                <div v-if="item.prizeImg" class="imgsbox">
                  <img class="imgBox" :src="item.prizeImg" alt=""/>
                </div>
                <div class="prizeDetail">
                  <ul class="prizeName">
                    {{ item.prizeName }}
                  </ul>
                  <div v-if="item.createTime" class="prizeDateOrder">领取时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                </div>
              </div>
<!--              &lt;!&ndash;实物&&已发放&ndash;&gt;&ndash;&gt;-->
              <div class="prizeRight">
                <div class="rightDivAll" v-if="item.prizeType === ASSETS.PHYSICAL">
                     <div class="text-yellow-400 text-right" v-if="!item.deliveryStatus && item.status!== 2">待发货</div>
                      <div class="text-red-400 text-right" v-if="item.status === 2">已取消</div>
                      <div class="text-green-400 text-right" v-if="item.deliveryStatus && item.status !==2">已发货</div>
                      <div class="text-right" v-if="!item.deliveryStatus && item.status !== 2" @click="changAddress(item)">
                        <div class="text-blue-400" v-if="item.realName">查看地址</div>
                        <div class="text-purple-400" v-if="!item.realName">填写地址</div>
                      </div>
                 </div>
                 <!--礼品卡&&已发放-->
                 <div class="rightDivAll" v-else-if="item.prizeType === ASSETS.GIFT_CARD && item.status === 3">
                   <div class="text-right">
                     <span class="text-yellow-400 text-right" v-if="item.status ===1">待发放</span>
                     <span class="text-black text-right" v-if="item.status ===2">已取消</span>
                     <span class="text-green-400 text-right" v-if="item.status ===3">已发放</span>
                   </div>
                   <div class="text-blue-400 text-right" @click="showCardNum(item)" v-if="item.status !== 2">立即兑换</div>
                 </div>
                <!--已发放-->
                 <div class="rightDivAll" v-else-if="(item.prizeType === ASSETS.PLUS_MEMBER_CARD || item.prizeType === ASSETS.IQYI_MEMBER_CARD) && item.status === 3">
                    <div class="text-green-400 text-right">
                      <span class="text-yellow-400 text-right" v-if="item.status ===1">待发放</span>
                      <span class="text-black text-right" v-if="item.status ===2">已取消</span>
                      <span class="text-green-400 text-right" v-if="item.status ===3">已发放</span>
                    </div>
                    <div class="text-blue-400 text-right" @click="exchangePlusOrAiqiyi" v-if="item.status !== 2">立即兑换</div>
                 </div>
                  <!-- 京元宝&&已发放-->
                 <div class="rightDivAll" v-else-if="item.prizeType === ASSETS.JINGYUANBAO">
                      <div class="text-yellow-400 text-right" :class="item.status === 2 && 'text-red-400'">
                        <span class="text-yellow-400 text-right" v-if="item.status ===1">待发放</span>
                        <span class="text-black text-right" v-if="item.status ===2">已取消</span>
                        <span class="text-green-400 text-right" v-if="item.status ===3">已发放</span>
                      </div>
                      <div class="text-blue-400 text-right" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive && item.status !== 2">点击领取</div>
                 </div>
                <!-- 积分 京豆 红包 E卡 优惠券-->
                <!-- 直接发放 没有领奖过程-->
                  <div class="rightDivAll" v-else>
                       <div :class="item.status === 2 ? 'text-red-400' : 'text-green-400'">
                         <span class="text-yellow-400 text-right" v-if="item.status ===1">待发放</span>
                         <span class="text-black text-right" v-if="item.status ===2">已取消</span>
                         <span class="text-green-400 text-right" v-if="item.status ===3">已发放</span>
                       </div>
                  </div>
              </div>
            </div>
            <div class="deliver" v-if="item.prizeType === 3 && item.deliveryStatus">
              <div>
                <div class="deliverText">快递公司：{{item.deliverName}}</div>
                <div class="deliverText">快递单号：{{item.deliverNo}}</div>
              </div>
              <div class="copy-btn" :copy-text="item.deliverNo">复制单号</div>
            </div>
          </div>
        </div>
        <div class="listDiv" v-else>
          <div class="noPrizeDataStyle">暂无中奖记录
          </div>
        </div>
    </div>
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :echoData="echoData" :userReceiveRecordId="userReceiveRecordId" :activityPrizeId="activityPrizeId" @close="closeSaveAddress"></SaveAddress>
    </VanPopup>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import SaveAddress from './SaveAddress.vue';
import { isPreview } from '@/utils';
import { ASSETS } from '@/utils/enum';
import Clipboard from 'clipboard';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
// 将数组翻译
// eslint-disable-next-line no-shadow
const emits = defineEmits(['close', 'showCardNum', 'savePhone']);
const statusMap = {
  1: '待发放',
  2: '已取消',
  3: '已发放',
};

interface Prize {
  prizeImg: string;
  prizeName: string;
  createTime: string;
  status: number;
  prizeType: number;
  deliveryStatus: number;
  prizeContent: string;
  isFuLuWaitingReceive: boolean;
  userReceiveRecordId: string;
  realName: string;
  deliverName: string;
  deliverNo: string;
}

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const prizeList = ref<Prize[]>([]);

const showSaveAddress = ref(false);
const userReceiveRecordId = ref('');
const activityPrizeId = ref('');

const echoData: any = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});
const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10096/getReceive');
    prizeList.value = data;
    closeToast();
  } catch (error) {
    closeToast();
  }
};

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  userReceiveRecordId.value = item.userReceiveRecordId;
  activityPrizeId.value = item.activityPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getRecord();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = item.prizeContent ? JSON.parse(item.prizeContent) : { result: { planDesc: '-' } };
  emits('savePhone', item.userReceiveRecordId, prizeContent.result.planDesc);
};

const close = () => {
  emits('close');
};
!isPreview && getRecord();
</script>
<style lang="scss" scoped>
.myPrizePopStyle {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/228945/19/1483/129282/654217e1F0c34f077/b60b622df69ca423.png");
  background-size: 100%;
  width: 7.5rem;
  height: 9.48rem;
  padding-top: 1.1rem;
  font-size: 0.24rem;
  overflow-y: scroll;
  background-repeat: no-repeat;
  background-color: transparent;
  .text-purple-400{
    color: rgb(167,139,250);
  }
  .closeDiv {
    width: 0.6rem;
    height: 0.6rem;
    position: absolute;
    left: 6.6rem;
    top: 0.38rem;
    z-index: 9999;
    border-radius: 100%;
  }
  .tips{
    color: #FFFFFF;
    font-size: 0.24rem;
    margin-left: 0.32rem;
  }

  .listContentDiv {
    position: relative;
    z-index: 300;
    height: 8rem;
    overflow-y: scroll;
    margin-top: 0.2rem;
    .itemDiv {
      background: #ffffff;
      margin: 0 0.28rem 0.1rem 0.28rem;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .prizeType {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        display: flex;
        justify-content: space-between;
        height: 0.6rem;
        line-height: 0.6rem;
        width: 100px;
        padding: 0 0.2rem;
        width: 100%;
      }

      .prizeBottomStyle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #ffffff;
        height: auto;
        padding: 0.2rem 0.2rem 0 0.2rem;
        border-top: 1px dotted;

        .prizeLeftStyle {
          display: flex;
          align-items: center;

          .prizeName {
            padding: 0 0.2rem;
          }
        }

        .prizeRight {
          margin: 0 0.2rem 0 0;

          .prizeStatus1Style {
            color: #ffb86b;
            font-size: 0.24rem;
            text-align: right;
          }

          .prizeStatus2Style {
            color: #6BCE98;
            font-size: 0.24rem;
            text-align: right;
          }

          .prizeStatus3Style {
            color: #999999;
            font-size: 0.24rem;
            text-align: right;
          }

          .prizeStatusStyle12 {
            color: #0083ff;
            font-size: 0.24rem;
            padding: 0.2rem 0 0 0;
            text-align: right;
          }

          .prizeEdit {
            .prizeStatusStyle12Edit {
              color: #3458f5;
              text-align: right;
              padding: 0.2rem 0 0;
            }

            .prizeStatusStyle12Write {
              color: #b24ff1;
              text-align: right;
              padding: 0.2rem 0 0;
            }
          }

          .prizeExchangeStyle {
            color: #00bb66;
            text-align: right;
            padding: 0.2rem 0 0;
          }
        }
      }

      .orderPrizeBottom {
        font-size: 0.2rem;
        color: rgb(153, 153, 153);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.2rem;
        margin-top: 0.1rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        border-top: 0.02rem dashed rgb(238, 238, 238);
      }
    }
  }

  .deliver{
    margin-left: 1.4rem;
    margin-right: 0.22rem;
    color: #999999;
    font-size: 0.2rem;
    display: flex;
    justify-content: space-between;
    .copy-btn {
      color: #0083ff;
      margin-top:0.38rem;
      font-size: 0.24rem;
    }
    .deliverText{
      margin-bottom:0.14rem;
    }
  }

  .listDiv {
    .noPrizeDataStyle {
      height: 8.52rem;
      font-size: 0.24rem;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #8C8C8C;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/172853/31/40266/6154/64f823f2F7590f215/9708f486dd266e9c.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0 0.3rem;
    }
  }
}

.spanName {
  padding: 0 0.2rem;
}

.prizeDateOrder {
  padding: 0 0.2rem;
  color: #999999;
  font-size: 0.2rem;
  margin-top: 0.1rem;
}

.imgsbox {
  border-radius: 50%;
  height: 1rem;
  background-color: #ff723d;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  display: flex;
}

.imgBox {
  margin: 0 auto;
  width: 1rem;
  align-items: center;
  background-color: #ff723d;
}
</style>
