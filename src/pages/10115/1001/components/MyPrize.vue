<template>
  <div class="rule-popup" id="ActivityPrize" style="opacity: 0; transform: 'translateZ(0)'">
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/280484/33/17243/918/67f72edfF8b7f296b/989835f01adf2c9f.png" alt="" class="close" @click="close">
    <div class="rule-bk">
      <div class="content" v-if="prizes.length > 0">
        <div v-for="(item, index) in prizes" :key="index" class="prize">
          <!-- <div class="type">
            <span>{{ prizeType[item.prizeType] }}</span>
            <span>{{ item.userPrizeId ? item.userPrizeId : '' }}</span>
          </div> -->
          <div class="info">
            <img :src="item.prizeImg" alt="" class="show-img" />
            <div class="detail">
              <div class="name">{{ item.prizeName }}</div>
              <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
            </div>
            <!-- 领取状态 0:未领取 1:领取成功 2:领取失败 -->
            <div class="status">
              <!-- 去掉这种状态 -->
              <!-- <div v-if="item.receiveStatus === 0" class="blue" @click="getThisPrize(item)">领取</div> -->
              <div v-if="item.receiveStatus === 1" class="green">已发放</div>
              <div v-if="item.receiveStatus === 2" class="orange">领取失败</div>
            </div>
            <div v-if="item.receiveStatus === 2" class="tip">联系客服为您解决</div>
          </div>
        </div>
      </div>
       <div class="no-data-content" v-else>
        <div class="no-data">暂无获奖记录哦~</div>
       </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref, onMounted } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import openCard from '@/utils/openCard';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close']);

const props = defineProps({
  isMember: {
    type: Boolean,
    required: true,
    default: false,
  },
  prizes: {
    type: Array,
    required: true,
  },
});

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  4: '积分',
  6: '红包',
};

interface Prize {
  activityPrizeId: any;
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
}

// 领取奖品
const getPrize = async (prizeId: any, userPrizeId: any) => {
  try {
    const { data } = await httpRequest.post('/10115/receivePrize', {
      prizeId,
      userPrizeId,
    });
    // 0 未领取 1 领取成功 2 领取失败 3发放成功
    if (data.status === 1) {
      showToast('领取成功');
      // getUserPrizes();
    } else if (data.status === 2) {
      // 这里弹出失败原因
      showToast('领取失败');
    }
  } catch (error: any) {
    console.error(error);
    if (error?.message) {
      showToast(error.message);
    }
  }
};
/**
 * 去开卡
 */
const toOpenCard = () => {
  openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`, {}, '10115');
};
// 非会员去开卡
const getThisPrize = (prize: Prize) => {
  if (props.isMember) {
    getPrize(prize.activityPrizeId, prize.userPrizeId);
  } else {
    toOpenCard();
  }
};
onMounted(() => {
  const element = document.getElementById('ActivityPrize');
  let zoomStartTime: number|null = null;
  let pulseStartTime: number|null = null;
  let pulseCount = 0;

  function pulse(timestamp: number) {
    if (!pulseStartTime) pulseStartTime = timestamp;
    const progress = timestamp - pulseStartTime;
    const duration = 300; // 每次 pulse 动画持续时间为 0.3 秒

    if (progress < duration) {
      // 使用正弦函数实现平滑的缩放效果
      const scale = 1 - (0.1 * Math.sin((Math.PI * progress) / duration));
      element.style.transform = `scale(${scale}) rotateY(0deg) translateZ(0)`;
      requestAnimationFrame(pulse);
    } else {
      pulseCount++;
      if (pulseCount < 2) {
        pulseStartTime = null; // 重置时间，开始下一次 pulse 动画
        requestAnimationFrame(pulse);
      }
    }
  }
  function startPulse() {
    pulseStartTime = null;
    pulseCount = 0;
    requestAnimationFrame(pulse); // 启动 pulse 动画
  }
  function zoomAndFlip(timestamp: number) {
    if (!zoomStartTime) zoomStartTime = timestamp;
    const progress = timestamp - zoomStartTime;
    const duration = 500; // 动画持续时间为 0.8 秒

    if (progress < duration) {
      const scale = 0.1 + (0.9 * progress) / duration; // 从 0.5 到 1
      const rotateY = 360 - (360 * progress) / duration; // 从 90deg 到 0deg
      const opacity = progress / duration; // 从 0 到 1
      element.style.transform = `scale(${scale}) rotateY(${rotateY}deg) translateZ(0)`;
      element.style.opacity = opacity;
      requestAnimationFrame(zoomAndFlip);
    } else {
      element.style.transform = 'scale(1) rotateY(0deg) translateZ(0)';
      element.style.opacity = 1;
      startPulse(); // zoomAndFlip 结束后启动 pulse 动画
    }
  }

  requestAnimationFrame(zoomAndFlip); // 启动 zoomAndFlip 动画
});

</script>

<style scoped lang="scss">
@keyframes popupAnimation {
  0% {
    transform: scale(0.1) rotateY(0deg);
    opacity: 0;
  }
  100% {
    transform: scale(1) rotateY(360deg);
    opacity: 1;
  }
}
@-webkit-keyframes popupAnimation {
  0% {
    -webkit-transform: scale(0.1) rotateY(0deg);
    -webkit-opacity: 0;
  }
  100% {
    -webkit-transform: scale(1) rotateY(360deg);
    -webkit-opacity: 1;
  }
}
@keyframes popupAnimationAfter {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.9);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes popupAnimationAfter {
  0% {
    -webkit-transform: scale(1);
  }
  25% {
    -webkit-transform: scale(0.9);
  }
  50% {
    -webkit-transform: scale(1);
  }
  75% {
    -webkit-transform: scale(0.9);
  }
  100% {
    -webkit-transform: scale(1);
  }
}

.rule-popup {
  width: 6.9rem;
}
.rule-bk {
  margin-top: 0.6rem;
  width: 6.9rem;
  height: 10rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/273935/8/25859/438726/680b325aFe06289e9/d857b6ee434cc800.png);
  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  will-change: transform;
  perspective: 1000px;
  // animation: popupAnimation 1s cubic-bezier(0.25, 0.1, 0.25, 1),
  // popupAnimationAfter 0.8s cubic-bezier(0.42, 0, 0.58, 1) 1s;
  // -webkit-animation: popupAnimation 1s cubic-bezier(0.25, 0.1, 0.25, 1),
  // popupAnimationAfter 0.8s cubic-bezier(0.42, 0, 0.58, 1) 1s;

  .content {
    height: 5.6rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    width: 5.4rem;
    margin-left: 0.7rem;
    margin-top: 2.76rem;

    .prize {
      background: rgba(255, 255, 255, 0.5);
      margin-bottom: 0.1rem;
      padding-bottom: 0.1rem;
      border-radius: 0.16rem;

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.16rem;
        padding-bottom: 0.16rem;
        border-bottom: 0.02rem dashed #eee;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.1rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        position: relative;

          .tip {
            position: absolute;
            bottom: 0;
            right: 0;
            font-size: 0.16rem;
            color: #333;
          }

        .show-img {
          width: 0.5rem;
          height: 0.5rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #ff3333;
          }

          .time {
            color: #333333;
            font-size: 0.16rem;
            margin-top: 0.1rem;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;

          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            padding: 0.05rem 0.08rem;
            background: #0083ff;
            font-size: .22rem;
            color: #fff;
            border-radius: 0.05rem;
          }
        }
      }
      .deliver{
        margin-left: 1.3rem;
        margin-right: 0.22rem;
        color: #999999;
        font-size: 0.2rem;
        display: flex;
        justify-content: space-between;
        .copy-btn {
          color: #0083ff;
          margin-top:0.24rem;
          font-size: 0.24rem;
        }
      }
    }
  }
  .no-data-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 5.6rem;
    border: 0.3rem solid transparent;
    font-size: 0.24rem;
    width: 5.4rem;
    margin-left: 0.7rem;
    margin-top: 2.72rem;

    .no-data {
      text-align: center;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}
  .close {
    width: 0.6rem;
    height: 0.6rem;
    position: absolute;
    top: 0;
    right: 0.1rem;
    z-index: 3000;
  }
</style>
