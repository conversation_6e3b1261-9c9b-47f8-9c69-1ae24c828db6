<template>
  <div class="popupContainer" id="AwardPopup" style="opacity: 0; transform: 'translateZ(0)'">
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/280484/33/17243/918/67f72edfF8b7f296b/989835f01adf2c9f.png" alt="" class="close" @click="close" />
    <div class="bk">
      <div class="content">
        <p class="prize-name" :style="{ color: prizeColor }">获得{{ prize.prizeName }}</p>
        <img :src="prize.prizeImg" alt="" class="prize-img" />
        <!-- 会员 领取成功展示 -->
        <div v-if="type === 2" class="tips">
          <div class="p3" v-if="prize.prizeType === 2">
            <div>京豆将会自动发放到您的账户</div>
            <div>请至【我的】-【京豆】查看</div>
          </div>
          <div class="p3" v-if="prize.prizeType === 1">
            <div>优惠券将会自动发放到您的账户</div>
            <div>请至【我的】-【优惠券】查看</div>
          </div>
          <div class="p3" v-if="prize.prizeType === 4">
            <div>积分将会自动发放到您的账户</div>
            <div>请至【店铺】-【会员】查看</div>
          </div>
          <div class="p3" v-if="prize.prizeType === 6">
            <div>红包将会自动发放到您的账户</div>
            <div>请至【我的】-【我的钱包】查看</div>
          </div>
        </div>
        <!-- 未领取展示  -->
        <div class="btn-list" v-if="type === 1">
          <!-- 领取，开卡 -->
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/284488/2/26161/24425/680b2f5aF7ff6376a/36868762089de462.png" alt="" class="get-btn" @click="getPrizeNow" />
          <!-- 狠心离开 -->
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/280915/21/22328/8559/680b2ef6F4fa1af7a/b4e6ca0f988385b3.png" alt="" class="right-btn" @click="close"  />
        </div>
        <div class="tip" v-if="type === 1">关闭窗口将视为主动放弃本次奖励哦~</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref, onMounted, onUnmounted, PropType } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage, gotoSkuPage, exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { closeToast, showLoadingToast, showToast, showDialog } from 'vant';
import { callShare } from '@/utils/platforms/share';
import { httpRequest } from '@/utils/service';
import openCard from '@/utils/openCard';

const isPreview = inject('isPreview') as boolean;
const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
  prizeColor: {
    type: String,
  },
  isMember: {
    type: Boolean,
    required: true,
    default: false,
  },
  type: {
    type: Number,
    required: true,
  },
});

const emits = defineEmits(['getPrize', 'close', 'openGiveUpPrizeDialog']);
/**
 * 关闭函数
 *
 * @returns Promise<void>
 */
const close = async () => {
  if (localStorage.getItem('P_P_I')) {
    localStorage.removeItem('P_P_I');
    localStorage.removeItem('prizeId');
    localStorage.removeItem('userPrizeId');
  }
  if (props.type === 2) {
    emits('close');
  } else {
    emits('openGiveUpPrizeDialog');
  }

};

/**
 * 判断用户是否为会员并执行相应操作
 *
 * 如果用户不是会员，则将奖品ID和用户奖品ID保存到localStorage，并跳转到开卡页面
 * 如果用户是会员，则通过emits触发'getPrize'事件，传递奖品ID和用户奖品ID
 */
const getPrizeNow = () => {
  if (!props.isMember) {
    // 非会员去开卡
    localStorage.setItem('P_P_I', JSON.stringify(props.prize));
    localStorage.setItem('prizeId', props.prize.activityPrizeId);
    localStorage.setItem('userPrizeId', props.prize.userPrizeId);
    localStorage.setItem('activityId_has_prize', baseInfo.id);
    openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`, {}, '10115');
  } else {
    emits('getPrize', props.prize.activityPrizeId, props.prize.userPrizeId, 1);
  }
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};

onMounted(() => {
  console.log('组件已挂载');
  const element = document.getElementById('AwardPopup');
  let zoomStartTime: number|null = null;
  let pulseStartTime: number|null = null;
  let pulseCount = 0;

  function pulse(timestamp: number) {
    if (!pulseStartTime) pulseStartTime = timestamp;
    const progress = timestamp - pulseStartTime;
    const duration = 300; // 每次 pulse 动画持续时间为 0.3 秒

    if (progress < duration) {
      // 使用正弦函数实现平滑的缩放效果
      const scale = 1 - (0.1 * Math.sin((Math.PI * progress) / duration));
      element.style.transform = `scale(${scale}) rotateY(0deg) translateZ(0)`;
      requestAnimationFrame(pulse);
    } else {
      pulseCount++;
      if (pulseCount < 2) {
        pulseStartTime = null; // 重置时间，开始下一次 pulse 动画
        requestAnimationFrame(pulse);
      }
    }
  }
  function startPulse() {
    pulseStartTime = null;
    pulseCount = 0;
    requestAnimationFrame(pulse); // 启动 pulse 动画
  }
  function zoomAndFlip(timestamp: number) {
    if (!zoomStartTime) zoomStartTime = timestamp;
    const progress = timestamp - zoomStartTime;
    const duration = 500; // 动画持续时间为 0.8 秒

    if (progress < duration) {
      const scale = 0.1 + (0.9 * progress) / duration; // 从 0.5 到 1
      const rotateY = 360 - (360 * progress) / duration; // 从 90deg 到 0deg
      const opacity = progress / duration; // 从 0 到 1
      element.style.transform = `scale(${scale}) rotateY(${rotateY}deg) translateZ(0)`;
      element.style.opacity = opacity;
      requestAnimationFrame(zoomAndFlip);
    } else {
      element.style.transform = 'scale(1) rotateY(0deg) translateZ(0)';
      element.style.opacity = 1;
      startPulse(); // zoomAndFlip 结束后启动 pulse 动画
    }
  }

  requestAnimationFrame(zoomAndFlip); // 启动 zoomAndFlip 动画
});

onUnmounted(() => {
  console.log('组件已销毁');
});

</script>

<style scoped lang="scss">
@keyframes popupAnimation {
  0% {
    transform: scale(0.1) rotateY(0deg);
    opacity: 0;
  }
  100% {
    transform: scale(1) rotateY(360deg);
    opacity: 1;
  }
}
@-webkit-keyframes popupAnimation {
  0% {
    -webkit-transform: scale(0.1) rotateY(0deg);
    -webkit-opacity: 0;
  }
  100% {
    -webkit-transform: scale(1) rotateY(360deg);
    -webkit-opacity: 1;
  }
}
@keyframes popupAnimationAfter {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.9);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes popupAnimationAfter {
  0% {
    -webkit-transform: scale(1);
  }
  25% {
    -webkit-transform: scale(0.9);
  }
  50% {
    -webkit-transform: scale(1);
  }
  75% {
    -webkit-transform: scale(0.9);
  }
  100% {
    -webkit-transform: scale(1);
  }
}

.popupContainer {
  width: 6.9rem;
  position: relative;
  display: flex;
}
.bk {
  margin-top: 0.6rem;
  width: 6.9rem;
  height: 10rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/281260/9/24692/486897/680b2e6cF91be76b8/380c937e33f322e8.png);
  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  will-change: transform;
  // animation: popupAnimation 1s cubic-bezier(0.25, 0.1, 0.25, 1),
  // popupAnimationAfter 0.8s cubic-bezier(0.42, 0, 0.58, 1) 1s;
  // -webkit-animation: popupAnimation 1s cubic-bezier(0.25, 0.1, 0.25, 1),
  // popupAnimationAfter 0.8s cubic-bezier(0.42, 0, 0.58, 1) 1s;

  .content {
    height: 6rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    width: 5.4rem;
    margin-left: 0.6rem;
    margin-top: 2.6rem;
    position: relative;

    .prize-name {
      width: 4.4rem;
      margin-top: 0.6rem;
      margin-left: 0.6rem;
      font-size: 0.5rem;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .prize-img {
      width: 2rem;
      height: 2rem;
      margin-left: 1.75rem;
      margin-top: 0.3rem;
    }
    .tips {
      color: #fff;
      position: absolute;
      top: 4.5rem;
      width: 100%;
      .p3 {
        text-align: center;
        font-size: 0.32rem;
      }
    }
    .btn-list {
      width: 100%;
      position: absolute;
      top: 4.5rem;
      .get-btn {
        width: 2.58rem;
        position: absolute;
        left: 0.1rem;
        top: 0.1rem;
      }
      .right-btn {
        width: 2.58rem;
        position: absolute;
        right: 0.02rem;
        top: 0.1rem;
      }
    }
    .tip {
      width: 100%;
      text-align: center;
      position: absolute;
      color: #fff;
      font-size: 0.24rem;
      top: 5.6rem;
    }
  }
}
.close {
  width: 0.6rem;
  height: 0.6rem;
  position: absolute;
  top: 0;
  right: 0.1rem;
  z-index: 3000;
}
</style>
