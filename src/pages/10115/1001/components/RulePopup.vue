<template>
  <div class="rule-popup" id="ActivityRules" style="opacity: 0; transform: 'translateZ(0)'">
    <img src="//img10.360buyimg.com/imgzone/jfs/t1/280484/33/17243/918/67f72edfF8b7f296b/989835f01adf2c9f.png" alt="" class="close" @click="close">
    <div class="rule-bk">
      <div class="content" v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
onMounted(() => {
  console.log('组件已挂载');
  const element = document.getElementById('ActivityRules');
  let zoomStartTime: number|null = null;
  let pulseStartTime: number|null = null;
  let pulseCount = 0;

  function pulse(timestamp: number) {
    if (!pulseStartTime) pulseStartTime = timestamp;
    const progress = timestamp - pulseStartTime;
    const duration = 300; // 每次 pulse 动画持续时间为 0.3 秒

    if (progress < duration) {
      // 使用正弦函数实现平滑的缩放效果
      const scale = 1 - (0.1 * Math.sin((Math.PI * progress) / duration));
      element.style.transform = `scale(${scale}) rotateY(0deg) translateZ(0)`;
      requestAnimationFrame(pulse);
    } else {
      pulseCount++;
      if (pulseCount < 2) {
        pulseStartTime = null; // 重置时间，开始下一次 pulse 动画
        requestAnimationFrame(pulse);
      }
    }
  }
  function startPulse() {
    pulseStartTime = null;
    pulseCount = 0;
    requestAnimationFrame(pulse); // 启动 pulse 动画
  }
  function zoomAndFlip(timestamp: number) {
    if (!zoomStartTime) zoomStartTime = timestamp;
    const progress = timestamp - zoomStartTime;
    const duration = 500; // 动画持续时间为 0.8 秒

    if (progress < duration) {
      const scale = 0.1 + (0.9 * progress) / duration; // 从 0.5 到 1
      const rotateY = 360 - (360 * progress) / duration; // 从 90deg 到 0deg
      const opacity = progress / duration; // 从 0 到 1
      element.style.transform = `scale(${scale}) rotateY(${rotateY}deg) translateZ(0)`;
      element.style.opacity = opacity;
      requestAnimationFrame(zoomAndFlip);
    } else {
      element.style.transform = 'scale(1) rotateY(0deg) translateZ(0)';
      element.style.opacity = 1;
      startPulse(); // zoomAndFlip 结束后启动 pulse 动画
    }
  }

  requestAnimationFrame(zoomAndFlip); // 启动 zoomAndFlip 动画
});

onUnmounted(() => {
  console.log('组件已销毁');
});
</script>

<style scoped lang="scss">
@keyframes popupAnimation {
  0% {
    transform: scale(0.1) rotateY(0deg);
    opacity: 0;
  }
  100% {
    transform: scale(1) rotateY(360deg);
    opacity: 1;
  }
}
@-webkit-keyframes popupAnimation {
  0% {
    -webkit-transform: scale(0.1) rotateY(0deg);
    -webkit-opacity: 0;
  }
  100% {
    -webkit-transform: scale(1) rotateY(360deg);
    -webkit-opacity: 1;
  }
}
@keyframes popupAnimationAfter {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.9);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes popupAnimationAfter {
  0% {
    -webkit-transform: scale(1);
  }
  25% {
    -webkit-transform: scale(0.9);
  }
  50% {
    -webkit-transform: scale(1);
  }
  75% {
    -webkit-transform: scale(0.9);
  }
  100% {
    -webkit-transform: scale(1);
  }
}
.rule-popup {
  width: 6.9rem;
}
.rule-bk {
  margin-top: 0.6rem;
  width: 6.9rem;
  height: 10rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/282109/35/26944/443741/680b3256F5818239a/5fb583f655e9dfe8.png);
  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  perspective: 1000px;
  will-change: transform;
  // animation: popupAnimation 1s cubic-bezier(0.25, 0.1, 0.25, 1),
  // popupAnimationAfter 0.8s cubic-bezier(0.42, 0, 0.58, 1) 1s;
  // -webkit-animation: popupAnimation 1s cubic-bezier(0.25, 0.1, 0.25, 1),
  // popupAnimationAfter 0.8s cubic-bezier(0.42, 0, 0.58, 1) 1s;

  .content {
    height: 6rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    width: 5.1rem;
    margin-left: 1rem;
    margin-top: 2.6rem;
  }
}
  .close {
    width: 0.6rem;
    height: 0.6rem;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 3000;
  }
</style>
