<!-- eslint-disable prefer-const -->
<template>
<!-- furnishStyles.shopNameColor.value -->

  <div class="bg">
    <div class="step1" v-if="step === 1">
      <img class="kv-img" :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/277653/28/18464/85904/67f72edaF0df8ce70/13d6ce02968e5380.png'" alt="" />
      <img class="prize-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/280225/4/27068/10475/680b2b99F6bbaf3cb/ab00a0d780cac7c2.png" alt="" @click="getUserPrizes"/>
      <img class="rule-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/275602/34/26839/10456/680b2b94Fb0144a2a/545eac32c4ac3e75.png" alt="" @click="showRulePopup"/>
      <!-- 开始游戏 -->
      <img v-if="hasChance && !playAgain" class="main-btn" :src="furnish.startGameImg" alt="" @click="startGame"/>
      <!-- 再玩一次 -->
      <img v-if="hasChance && playAgain" class="main-btn" :src="furnish.playAgainImg" alt="" @click="startGame"/>
      <!-- 无抽奖机会 -->
      <img v-if="!hasChance" class="main-btn" :src="furnish.noChanceImg" alt=""/>
      <!-- 进入店铺 -->
      <img class="shop-btn" :src="furnish.goShopImg" alt="" @click="gotoShop"/>
    </div>
    <!-- 倒计时 -->
    <div class="step2" v-if="step === 0">
      <img v-if="numDown == 3" class="main-num" :src="furnish.countDownImg3" alt=""/>
      <img v-if="numDown == 2" class="main-num" :src="furnish.countDownImg2" alt=""/>
      <img v-if="numDown == 1" class="main-num" :src="furnish.countDownImg1" alt=""/>

    </div>
    <img class="bg-img1" v-show="step === 2" src="https://jconnect.oss-cn-hangzhou.aliyuncs.com/hongbaoyu/4(1).gif" alt="" />
    <img class="bg-img2" v-show="step === 2" src="//img10.360buyimg.com/imgzone/jfs/t1/276080/7/18803/204725/67f8b1dcF415a67a5/e1e3b003096f3e1e.png" alt="" />
    <!-- <keep-alive> -->
      <div class="step3" v-if="step === 2">
        <RedPacketRain :duration="activityInfo.activityDuration" :packetImage="furnish.actRedBg" @onEnd="cj" :countColor="furnish.countColor"/>
        <div class="stopGame" @click="stopGame">退出游戏</div>
      </div>
    <!-- </keep-alive> -->

  </div>
  <!-- 我的奖品列表弹窗 -->
  <VanPopup teleport="body" v-if="showMyPrize" v-model:show="showMyPrize" position="center" :closeOnClickOverlay="false" >
    <MyPrize :isMember="isMember" :prizes="prizes" @close="handleClose"></MyPrize>
  </VanPopup>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-if="showRule" v-model:show="showRule" position="center" :closeOnClickOverlay="false" >
    <RulePopup :rule="ruleTest" @close="handleClose"></RulePopup>
  </VanPopup>
  <!-- 未中奖弹窗 -->
  <VanPopup teleport="body" v-if="showNoAward" v-model:show="showNoAward" :closeOnClickOverlay="false" >
    <NoAwardPopup @close="closeAward" @goShop="gotoShop"></NoAwardPopup>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-if="showAward" v-model:show="showAward" :closeOnClickOverlay="false" >
    <AwardPopup
    @getPrize="getPrize"
    @close="closeAward"
    :type="awardType"
    :isMember="isMember"
    :prize="prizeInfo"
    :prizeColor="furnish.prizeColor"
    @openGiveUpPrizeDialog='openGiveUpPrizeDialog'></AwardPopup>
  </VanPopup>
  <!-- 放弃奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showGiveUpPrize" :closeOnClickOverlay="false" >
    <GiveUpPrizePopup @close="closeGiveUpPrizeDialog" @giveUpPrize="giveUpPrize"></GiveUpPrizePopup>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject, onMounted, onUnmounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import NoAwardPopup from '../components/NoAwardPopup.vue';
import AwardPopup from '../components/AwardPopup.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { Handler } from '@/utils/handle';
import RedPacketRain from '../components/RedPacketRain.vue';
import GiveUpPrizePopup from '../components/GiveUpPrizePopup.vue';
import useThreshold from '@/hooks/useThreshold';
import dayjs from 'dayjs';
import openCard from '@/utils/openCard';

interface ExtendedBaseInfo extends BaseInfo {
  isCountdown: number; // 倒计时去掉
}

const pathParams = inject('pathParams') as any;

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as ExtendedBaseInfo;
// 门槛提示
const showLimit = ref(false);

const step = ref(1);
const numDown = ref(3);
const ruleTest = ref('');
// 获奖信息
const showGiveUpPrize = ref(false);
const awardType = ref(1);
const prizeInfo = ref({});
const showRule = ref(false); // 是否显示规则弹窗
const showMyPrize = ref(false); // 是否显示我的奖品弹窗
const showNoAward = ref(false); // 是否显示未中奖弹窗
const showAward = ref(false); // 是否显示中奖弹窗
const isMember = ref(false); // 是否是会员

const playAgain = ref(false); // 是否再玩一次
const hasChance = ref(1); // 是否抽奖机会用完
const prizes = ref([]); // 奖品列表

const award = ref([{
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
}]);

// 活动基本信息
const activityInfo = ref({
  clickCount: 0,
  activityDuration: '',
});

const handleClose = () => {
  showMyPrize.value = false;
  showRule.value = false; // 关闭弹窗
};
const openGiveUpPrizeDialog = () => {
  showGiveUpPrize.value = true;
  showAward.value = false;
};
const closeGiveUpPrizeDialog = () => {
  showGiveUpPrize.value = false;
  showAward.value = true;
};

// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
// 领取奖品
const getPrize = async (prizeId: any, userPrizeId: any, type: number) => {
  try {
    const { data } = await httpRequest.post('/10115/receivePrize', {
      prizeId,
      userPrizeId,
    });
    // 0 未领取 1 领取成功 2 领取失败 3发放成功
    if (data.status === 1) {
      awardType.value = 2;
      prizeInfo.value = data;
      showAward.value = true;
    } else if (data.status === 2) {
      // 这里弹出失败原因
      // showToast('领奖失败');
      // 直接显示未中奖
      showNoAward.value = true;
      const timer = setTimeout(() => {
        step.value = 1;
        clearTimeout(timer);
      }, 1000);
    }
    if (type === 2) {
      localStorage.removeItem('P_P_I');
      localStorage.removeItem('prizeId');
      localStorage.removeItem('userPrizeId');
    }

  } catch (error: any) {
    console.error(error);
    if (error?.message) {
      showToast(error.message);
    }
  }
};

// 获取是否是会员
const getIsMember = async () => {
  try {
    const { data } = await httpRequest.post('/10115/isMember');
    isMember.value = data;
    if (localStorage.getItem('activityId_has_prize') === baseInfo.id) {
      if (isMember.value && localStorage.getItem('prizeId') && localStorage.getItem('userPrizeId')) {
        getPrize(localStorage.getItem('prizeId'), localStorage.getItem('userPrizeId'), 2);
      }
    } else {
      localStorage.removeItem('P_P_I');
      localStorage.removeItem('prizeId');
      localStorage.removeItem('userPrizeId');
    }

    if (!isMember.value && localStorage.getItem('P_P_I')) {
      prizeInfo.value = JSON.parse(localStorage.getItem('P_P_I'));
      awardType.value = 1;
      showAward.value = true;
    }
  } catch (error) {
    console.error(error);
  }
};
// 埋点
const trackEvent = async () => {
  try {
    const res = await httpRequest.post('/10115/trackEvent');
  } catch (error) {
    console.error(error);
  }
};
// 抽奖逻辑
const cj = async (redNum: number) => {
  // 开始抽奖，弹出中间弹窗或未中奖弹窗
  if (localStorage.getItem('P_P_I')) {
    localStorage.removeItem('P_P_I');
    localStorage.removeItem('prizeId');
    localStorage.removeItem('userPrizeId');
  }
  try {
    const res = await httpRequest.post('/10115/lotteryDraw', { clickCount: redNum });
    trackEvent();
    if (res.data.userPrizeId) {
      prizeInfo.value = res.data;
      if (isMember.value) {
        // 会员直接抽奖
        getPrize(res.data.activityPrizeId, res.data.userPrizeId, 2);
      } else {
        awardType.value = 1;
        showAward.value = true;
      }
    } else {
      // 未中奖
      showNoAward.value = true;
    }
  } catch (error) {
    console.error(error);
  }
};
// 获取是否是第二次玩
const getGameAgain = async () => {
  try {
    const res = await httpRequest.post('/10115/isFirstParticipate');
    // res.data，true 第一次玩，false 第二次玩
    playAgain.value = !res.data;
  } catch (error) {
    console.error(error);
  }
};
// 用户抽奖次数
const getChanceNum = async () => {
  try {
    const res = await httpRequest.post('/10115/chanceNum');
    // 剩余抽奖次数为0，机会已用完
    hasChance.value = res.data;
  } catch (error: any) {
    console.error(error);
  }
};

// 去店铺首页
const gotoShop = async () => {
  gotoShopPage(baseInfo.shopId);
};

// 开始游戏
const startGame = async () => {
  if (!isMember.value) {
    // 非会员先开卡
    openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`, {}, '10115');
    return;
  }
  try {
    // 门槛校验
    const res = await httpRequest.post('/10115/thresholdVerification');
    if (res.data.code === 0) {
      step.value = 0;
      // 显示倒计时

      // 修改定时器逻辑，确保每个数字停留完整1秒
      const timer = setInterval(() => {
        const now = Date.now();
        numDown.value -= 1;

        // 最后一个数字直接结束
        if (numDown.value === 0) {
          clearInterval(timer);
          step.value = 2;
          numDown.value = 3;
          return;
        }

        // 计算剩余时间补偿（确保总间隔为1秒）
        const elapsed = Date.now() - now;
        const remaining = Math.max(0, 1000 - elapsed);

      }, 1000);
    } else if (res.data.code === 3) {
      const thresholdList = [{
        thresholdTitle: '很遗憾',
        thresholdContent: '您不是参与此活动的指定人群',
      }];
      showLimit.value = useThreshold({
        thresholdList,
      });

    } else if (res.data.code === 1) {
      const thresholdList = [{
        thresholdTitle: '抱歉，活动暂未开始',
        thresholdContent: `活动开始时间：${dayjs(baseInfo.startTime).format('YYYY-MM-DD HH:mm:ss')}`,
      }];
      showLimit.value = useThreshold({
        thresholdList,
      });
    } else if (res.data.code === 2) {
      const thresholdList = [{
        thresholdTitle: '抱歉，活动已结束',
        thresholdContent: '敬请关注其他活动',
      }];
      showLimit.value = useThreshold({
        thresholdList,
      });
    }
  } catch (error: any) {
    console.error(error);
    if (error?.message) {
      showToast(error.message);
    }
  }

};
const getActivity = async () => {
  try {
    const { data } = await httpRequest.post('/10115/activity');
    console.log('活动基本信息', data);
    console.log('数据', `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
    activityInfo.value = data;
  } catch (error: any) {
    console.error(error);
    if (error?.message) {
      showToast(error.message);
    }
  }
};
// 退出游戏
const stopGame = () => {
  step.value = 1;
};
// 获取中奖列表
const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10115/userPrizes');
    closeToast();
    prizes.value = res.data;
    showMyPrize.value = true;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    // 是否是会员
    // 是否还有次数
    // 是否显示再玩一次
    await Promise.all([getActivity(), getIsMember(), getGameAgain(), getChanceNum()]);

    closeToast();
  } catch (error) {
    closeToast();
  }
};
/**
 * 关闭奖项弹窗
 *
 * 将奖项弹窗和未中奖弹窗的显示状态设置为 false，
 * 将步骤值重置为 1，并调用初始化函数。
 */
const closeAward = async () => {
  showAward.value = false;
  showNoAward.value = false;
  step.value = 1;
  init();
};
// 放弃奖品
const giveUpPrize = async () => {
  if (localStorage.getItem('P_P_I')) {
    localStorage.removeItem('P_P_I');
    localStorage.removeItem('prizeId');
    localStorage.removeItem('userPrizeId');
  }
  try {
    const res = await httpRequest.post('/10115/giveUp', { prizeId: prizeInfo.value.activityPrizeId, userPrizeId: prizeInfo.value.userPrizeId });
    showGiveUpPrize.value = false;
    closeAward();
  } catch (error) {
    showGiveUpPrize.value = false;
    closeAward();
    console.error(error);
  }
};
/**
 * 处理页面可见性变化事件
 *
 * @param e 事件对象
 */
const handleVisiable = async (e: any) => {
  // 如果缓存中存在flag再执行判断visibilityState
  if (document.visibilityState !== 'visible') return;
  await closeAward();
};
init();
onMounted(() => {
  // init();
  // document.addEventListener('visibilitychange', handleVisiable);
});
onUnmounted(() => {
  // document.removeEventListener('visibilitychange', handleVisiable);
});

</script>

<style scoped lang="scss">
.bg {
  background-size: 100% 100%;
  min-height: 100vh;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/270836/19/18859/6637/67f72f12Fb17b23b7/51bf17f36fd18b8f.jpg');
  background-repeat: no-repeat;
  position: relative;
  // touch-action: none;
  overscroll-behavior: contain;
  overflow: hidden;
}
.kv-img {
  width: 6rem;
  height: 4.7rem;
  position: absolute;
  top: 3.2rem;
  left: 10%;
}
.prize-btn {
  width: 0.8rem;
  position: absolute;
  top: 1rem;
  right: 0;
}
.rule-btn {
  width: 0.8rem;
  position: absolute;
  top: 2rem;
  right: 0;
}
.main-btn {
  width: 4.2rem;
  height: 0.98rem;
  position: absolute;
  top: 8.4rem;
  left: 50%;
  transform: translateX(-50%);
}
.shop-btn {
  width: 4.2rem;
  height: 0.98rem;
  position: absolute;
  top: 9.66rem;
  left: 50%;
  transform: translateX(-50%);
}
// @keyframes zoomIn {
//   11% {
//     transform: scale(0.5) translateX(-50%);
//     opacity: 0;
//   }
//   33% {
//     transform: scale(1);
//     opacity: 1;
//   }
//   44% {
//     transform: scale(0.5);
//     opacity: 0;
//   }
//   66% {
//     transform: scale(1);
//     opacity: 1;
//   }
//   77% {
//     transform: scale(0.5);
//     opacity: 0;
//   }
//   99% {
//     transform: scale(1);
//     opacity: 1;
//   }
// }

.step2 {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}
.main-num {
  height: 2rem;
  margin-top: 40%;
  transform: scale(0.5) translateZ(-200px); /* 初始状态：缩小并远离 */
  perspective: 200px;
  opacity: 0;
  animation: zoomIn 0.8s linear forwards; /* 动画效果 */
}

@keyframes zoomIn {
  0% {
    transform: scale(0.5) translateZ(-200px); /* 初始状态：缩小并远离 */
    opacity: 0;
  }
  100% {
    transform: scale(1) translateZ(0); /* 最终状态：正常大小并靠近 */
    opacity: 1;
  }
}
.bg-img1 {
    width: 7.5rem;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
}
.bg-img2 {
    width: 11rem;
    position: absolute;
    bottom: -1.64rem;
    right: 0;
    left: -1.6rem;
    z-index: 50;
    pointer-events: none;
}
.stopGame {
  width: 2rem;
  height: 0.6rem;
  line-height: 0.6rem;
  border: #000 1px solid;
  border-radius: 0.2rem;
  text-align: center;
  color: #fff;
  background-color: #000;
  position: absolute;
  bottom: 0.2rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.3rem;
  z-index: 200;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
