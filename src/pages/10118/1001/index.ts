import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import initializeRouter from './ts/routes';
import '@/style';
import { furnish } from './ts/furnishStyles';
import { getActivityData } from './ts/logic';
import JudgmentConditions from './components/JudgmentConditions.vue';
import thresholdPlugin from '@/plugins/ThresholdChecker';

initRem();

const app = createApp(index);

// 初始化页面
const config: InitRequest = {
  urlPattern: window.location.href.includes('promotion') ? '/v2/:activityType/:templateCode/promotion' : '/v2/:activityType/:templateCode',
  thresholdPopup: JudgmentConditions,
};

const _decoData = {
  // 活动主图
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/287637/29/5998/89886/682556d2F7f782de2/0292c4880a4efdcc.png',
  // 页面背景颜色
  actBgColor: '#ff5947',
  // 虚拟商品介绍图
  introductionImg: '//img10.360buyimg.com/imgzone/jfs/t1/231352/36/792/38567/6539e4d5F2550c94d/ee46a17f3814666b.png',
  // 楼层标题图
  floorTitleImg: '//img10.360buyimg.com/imgzone/jfs/t1/301551/22/6386/1988/68249a8cFc206a8d3/de914452aaaf4be7.png',
  // 活动规则图
  ruleImg: '//img10.360buyimg.com/imgzone/jfs/t1/236454/27/767/5177/6539e4d4Fc6b6928d/9ad6e23e621cf571.png',
  // 领取记录图标
  recordImg: '//img10.360buyimg.com/imgzone/jfs/t1/228484/32/776/5167/6539e4d4Fb975afe7/723ebd636223c170.png',
  // 领取攻略图标
  strategyImg: '//img10.360buyimg.com/imgzone/jfs/t1/194630/21/41096/5287/6539e4d4F2b9f8f31/8f1b5d3c28c085e2.png',
  // 预定成功页主图
  successKvImg: '//img10.360buyimg.com/imgzone/jfs/t1/318374/39/754/89646/682556cbF1fd90e14/64fa29b9880d7c89.png',
  // 预定成功页颜色
  successBgColor: '#ff5947',
  // 攻略图
  strategyPopupImg: '',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/163534/34/4106/101460/600e2292Ed3609887/824e50f6ac5477dd.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/123675/39/40734/23901/64feb230F9caecd1c/85d302f176ff72ad.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/101134/18/44826/69569/64feb231F72feb750/36145802ebe71525.png',
};

init(config).then(async ({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  document.title = baseInfo?.activityName;
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  await getActivityData();
  const router = initializeRouter();
  app.use(router);
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.use(thresholdPlugin);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
