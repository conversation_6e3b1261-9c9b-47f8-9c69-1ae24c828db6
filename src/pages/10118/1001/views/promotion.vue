<template>
  <div class="bk" :style="furnishStyles.successBgColor.value">
    <div class="kv">
      <img :src="furnish.successKvImg" alt="" class="kv-img" />
      <div class="btn-list">
        <img :src="furnish.ruleImg" alt="" @click="showRulePopup" />
        <img :src="furnish.recordImg" alt="" @click="recordPopup = true" />
        <img :src="furnish.strategyImg" alt="" @click="strategyPopup = true" />
      </div>
    </div>
    <div class="pro-time" v-if="activityData?.orderPrizeType">
      <div>{{ dayjs(activityData.orderStartTime).format('YYYY.MM.DD HH:mm:ss') }}~{{ dayjs(activityData.orderEndTime).format('YYYY.MM.DD HH:mm:ss') }}</div>
      <div>下单指定商品，单笔满{{ activityData?.orderAmount ?? 999999999 }}元即可获得赠礼</div>
    </div>
    <div class="block rights" v-if="activityData?.orderPrizeType">
      <div class="title">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/231669/19/811/450/6539e4d5F8308b0c5/558a0e50e50cbf06.png" alt="" class="icon" />
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/95030/23/36667/1521/653f432aFddae7963/401f588ae65773a2.png" alt="" class="text" />
      </div>
      <div class="prize-box" v-if="activityData.orderPrizeType">
        <div class="prize-img" :style="{ backgroundImage: `url(${activityData.orderPrizeImg})` }"/>
        <div class="prize-info">
          <div class="prize-name">{{activityData.orderPrizeName}}</div>
          <div class="prize-price">下单满{{activityData?.orderAmount ?? 300 }}元即可免费领取</div>
          <div class="prize-text">奖品数量有限，先到先得</div>
          <div class="prize-text">剩余库存{{activityData.sendTotalCount}}</div>
        </div>
      </div>
      <!-- <img :src="promotion?.giftPoster" alt="" class="giftPoster" /> -->
    </div>
    <div class="block activity">
      <div class="title">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/231669/19/811/450/6539e4d5F8308b0c5/558a0e50e50cbf06.png" alt="" class="icon" />
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/306762/37/841/2004/68249bccF37440e8b/fb8be9a3094d139f.png" alt="" class="text" />
      </div>
      <div class="sku-list">
        <div class="sku" v-for="item in skuList" :key="item.skuId">
          <div>
            <img :src="item.skuMainPicture" alt="" class="sku-img" />
          </div>
          <div class="sku-info">
            <div class="sku-name">{{ item.skuName }}</div>
            <div class="bot">
              <div class="sku-price">￥{{ item.jdPrice }}</div>
              <div class="add-card" @click="gotoSkuPage(item.skuId)">加购</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { activityData, showRulePopup, strategyPopup, recordPopup } from '../ts/logic';
import { ref, reactive } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';

const route = useRoute();
const skuList = ref([] as any[]);

const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10118/skuListPage');
    closeToast();
    // res.data.records.forEach((item: any) => {
    //   item.jdPrice /= 1000;
    // });
    skuList.value = res.data.records;
  } catch (error) {
    closeToast();
    console.error();
  }
};
getSkuList();
</script>

<style scoped lang="scss">
.bk {
  min-height: 100vh;
  position: relative;
  padding-bottom: 0.2rem;
}
.kv {
  position: relative;
  margin-bottom: 0.3rem;
  .kv-img {
    width: 100%;
  }
  .btn-list {
    position: absolute;
    top: 0.3rem;
    right: 0.3rem;
    img {
      width: 1.3rem;
      margin-bottom: 0.18rem;
    }
  }
}
.pro-time {
  text-align: center;
  font-size: 0.24rem;
  line-height: 0.35rem;
  color: #fff;
}
.block {
  width: 7.1rem;
  margin: 0 auto;
  background-size: 100%, 2.37rem;
  background-position: center 0.1rem, 4.3rem 0rem;
  background-repeat: no-repeat;
  border-radius: 0 0 0.3rem 0.3rem;
  margin-bottom: 0.2rem;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 0.2rem;
    .icon {
      height: 0.36rem;
      margin-right: 0.15rem;
    }
    .text {
      height: 0.34rem;
    }
  }
}
.rights {
  padding: 0.36rem 0.3rem 0.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193267/35/38277/188945/653a1349Fc500152e/29edaab05f171f44.png), url(//img10.360buyimg.com/imgzone/jfs/t1/91614/21/36103/10131/653a035dFe9a8052d/4c1e5be8cdeafeaf.png);
  .giftPoster {
    width: 100%;
  }
}
.activity {
  padding: 0.36rem 0.2rem 0.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193267/35/38277/188945/653a1349Fc500152e/29edaab05f171f44.png), url(//img10.360buyimg.com/imgzone/jfs/t1/172282/12/40797/10809/6539e4d5F5efc8691/3343aba0ce20482b.png);
  .sku-list {
    position: relative;
    min-height: 2.4rem;
    max-height: 5rem;
    overflow-y: scroll;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    &::-webkit-scrollbar {
      display: none;
    }
    .sku {
      width: 3.3rem;
      background-color: #fff;
      border-radius: 0.2rem;
      padding: 0.2rem;
      margin-bottom: 0.15rem;
      .sku-img {
        width: 100%;
        margin-bottom: 0.2rem;
      }
      .sku-name {
        font-size: 0.3rem;
        color: #262626;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-bottom: 0.2rem;
      }
      .bot {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .sku-price {
        font-size: 0.3rem;
        color: #ff3333;
      }
      .add-card {
        width: 1.1rem;
        height: 0.5rem;
        line-height: 0.5rem;
        color: #fff;
        background-color: #ff3333;
        text-align: center;
        border-radius: 0.25rem;
      }
    }
  }
}
.prize-box {
  padding: 0.1rem;
  height: 2.4rem;
  display: flex;
  background-color: #fff;
  border-radius: 0.2rem;
  align-items: center;
  .prize-img {
    margin-right: 0.2rem;
    width: 2rem;
    height: 2rem;
    background-size: 100%;
    background-repeat: no-repeat;
    border-radius: 0.2rem;
  }
  .prize-info {
    margin-top: 0.2rem;
    line-height: 0.45rem;
    .prize-name {
      font-size: 0.31rem;
      font-weight: bold;
      color: #262626;
      width: 4rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .prize-price {
      font-size: 0.24rem;
      color: #262626;
    }
    .prize-text {
      font-size: 0.24rem;
      color: #999999;
    }
  }
}

</style>
