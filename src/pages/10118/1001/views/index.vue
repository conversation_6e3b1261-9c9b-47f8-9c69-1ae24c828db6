<template>
  <div class="bk" :style="furnishStyles.pageBg.value">
    <div class="kv">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="shop-name" :style="furnishStyles.shopNameColor.value">
        <span v-if="furnish.disableShopName === 1" >{{shopName}}</span>
      </div>
      <div class="btn-list">
        <img :src="furnish.ruleImg" alt="" @click="showRulePopup" />
        <img :src="furnish.recordImg" alt="" @click="recordPopup = true" />
        <img :src="furnish.strategyImg" alt="" @click="strategyPopup = true" />
      </div>
    </div>
    <div class="block rights">
      <div class="title">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/231669/19/811/450/6539e4d5F8308b0c5/558a0e50e50cbf06.png" alt="" class="icon" />
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/294387/10/2095/3766/68249a88Fd9ac9812/45ea80fc047dfb92.png" alt="" class="text" />
      </div>
      <div class="introduction" :style="furnishStyles.introductionImg.value">
        <div class="tip">
          <div class="tipText" v-if="activityData?.lockRight === 2">您已购锁权，快去使用优惠吧</div>
          <div class="tipText" v-else>先下单锁定权益才可以参加活动哦</div>
        </div>
        <div class="btn">
          <img v-if="activityData?.lockRight === 2" src="//img10.360buyimg.com/imgzone/jfs/t1/305927/40/2022/21138/682499d7F33a7e3e6/b30cd5e2ed154174.png" alt="" v-threshold-click="join" />
          <img v-else src="//img10.360buyimg.com/imgzone/jfs/t1/190638/14/38667/8085/6539e4d4F244ea560/e7fba5e2f48f1086.png" alt="" v-threshold-click="getCoupon"/>
        </div>
      </div>
    </div>
    <div class="block activity" v-if="activityData?.orderPrizeType" >
      <div class="title">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/231669/19/811/450/6539e4d5F8308b0c5/558a0e50e50cbf06.png" alt="" class="icon" />
        <img :src="furnish.floorTitleImg" alt="" class="text" />
      </div>
      <div class="prize-status">
        <div v-if="activityData?.orderPrizeType" class="prize-box">
          <div class="prize-img" :style="{ backgroundImage: `url(${activityData?.orderPrizeImg})` }"/>
          <div class="prize-info">
            <div class="prize-name">{{activityData?.orderPrizeName}}</div>
            <div class="prize-price">下单满{{activityData?.orderAmount ?? 999999999 }}元即可免费领取</div>
            <div class="prize-text">奖品数量有限，先到先得</div>
            <div class="prize-text">剩余库存{{activityData?.sendTotalCount}}</div>
          </div>
          <div class="btn gray" v-if="activityData?.lockRight === 3">立即下单</div>
          <div class="btn gray" v-else-if="activityData?.sendStatus === 2">已领取</div>
          <div class="btn gray" v-else-if="activityData?.sendStatus === 3">领取失败</div>
          <div class="btn" v-else v-threshold-click="join">立即下单</div>
        </div>
        <div class="time">
          <span>下单时间：{{ dayjs(activityData?.orderStartTime).format('YYYY.MM.DD HH:mm:ss') }}-{{ dayjs(activityData?.orderEndTime).format('YYYY.MM.DD HH:mm:ss') }}</span>
        </div>
      </div>
    </div>
    <div class="tips-gray">支付状态更新可能会有延迟。若未收到优惠券，请刷新页面，或稍后进入活动页面查看领取记录。</div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, onMounted, onUnmounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { DecoData } from '@/types/DecoData';
import { editAddressPopup, activityData, showRulePopup, strategyPopup, recordPopup, showDialog, dialogInfo, getActivityData } from '../ts/logic';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { checkThreshold } from '@/components/Threshold/ts/logic';

const baseInfo = inject('baseInfo') as BaseInfo;
const decoData = inject('decoData') as DecoData;
const shopName = ref(baseInfo.shopName);
const router = useRouter();
const route = useRoute();

let needCheckPay = false;
const toPay = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    closeToast();
    const { data } = await httpRequest.post('/10118/pay', {
      activityId: baseInfo.activityId,
      amount: 1,
      shopId: baseInfo.shopId,
      source: '01',
      token: sessionStorage.getItem(constant.LZ_JD_TOKEN),
    });
    const param = {
      orderId: data.orderId,
      paySign: data.paySign,
      returnUrl: encodeURIComponent(window.location.href),
    };
    if (activityData.value?.lockRight === 3) {
      return;
    }
    const payUrl = `openapp.jdmobile://virtual?params={"category":"jump","des":"jdmp","appId":"2B43F9A518AE09BAE8789053047A685E","vapptype":"1","path":"pages/saas-pay/saas-pay.html","pageAlias":"","param":${JSON.stringify(param)}}`;
    needCheckPay = true;
    window.location.href = payUrl;
    // console.log(payUrl);
  } catch (error: any) {
    closeToast();
    if (error.message) {
      if (error.message === '请先填写地址' && +activityData.value?.orderPrizeType === 3) {
        editAddressPopup.value = true;
      } else if (error.message === '当前有生效订单无法再次支付') {
        // showToast({
        //   message: error.message,
        // });
        getActivityData();
      } else {
        showToast({
          message: error.message,
        });
      }
    }
  }
};
const getCoupon = () => {
  if (activityData.value?.lockRight === 1 && (activityData.value?.sendTotalCount < 0 || activityData.value?.sendTotalCount === 0)) {
    showDialog({
      title: '下单礼暂无库存，锁权仅会获得优惠券，是否继续进行锁权',
      btnText: '去下单',
      onClick: () => {
        dialogInfo.popup = false;
        toPay();
      },
    });
    return;
  }
  toPay();
};
const join = () => {
  if (activityData.value?.lockRight === 1) {
    showDialog({
      title: '先下单锁定权益才可以参加活动哦',
      content: '您还未下单',
      btnText: '去下单',
      onClick: () => {
        dialogInfo.popup = false;
        getCoupon();
      },
    });
    return;
  }
  if (+activityData.value?.status === 1) {
    router.push({
      path: '/promotion',
      query: { shopId: baseInfo.shopId, activityId: baseInfo.activityId },
    });
  } else {
    showDialog({
      title: `若参与赠礼活动，请在${dayjs(activityData.value?.orderStartTime).format('YYYY.MM.DD HH:mm:ss')}-${dayjs(activityData.value?.orderEndTime).format('YYYY.MM.DD HH:mm:ss')}内下单。`,
      btnText: '我知道了',
      onClick: () => {
        dialogInfo.popup = false;
        router.push({
          path: '/promotion',
          query: { shopId: baseInfo.shopId, activityId: baseInfo.activityId },
        });
      },
    });
  }
};

// const joinOther = (item: any) => {
//   if (activityData?.sendStatus === 2) { // 如果已领取
//     return;
//   }
//   if (item.status === 0) {
//     showDialog({
//       title: '活动还没开始哦',
//       content: `活动开始时间：${dayjs(item.orderStartTime).format('YYYY-MM-DD HH:mm:ss')}`,
//     });
//   } else if (item.status === 2) {
//     showToast({
//       message: '活动已结束',
//     });
//   }
// };

// 检查支付状态
const checkPay = () => {
  if (!needCheckPay) return;
  needCheckPay = false;
  getActivityData();
};
onMounted(() => {
  console.log(needCheckPay, 'onMounted');
  // 从上一页返回时，重新获取数据
  document.addEventListener('visibilitychange', checkPay);
});
onUnmounted(() => {
  console.log(needCheckPay, 'onUnmounted');
  document.removeEventListener('visibilitychange', checkPay);
});
</script>

<style scoped lang="scss">
.bk {
  min-height: 100vh;
  position: relative;
  padding-bottom: 0.2rem;
}
.tips-gray {
  color: #c3c3c3;
  font-size: 0.2rem;
  width: 7rem;
  margin: 0 auto;
}
.kv {
  position: relative;
  margin-bottom: 0.3rem;
  .kv-img {
    width: 100%;
  }
  .shop-name{
    position: absolute;
    left: 0.3rem;
    top: 0.3rem;
    font-size: 0.24rem;
    color: #fff;
  }
  .btn-list {
    position: absolute;
    top: 0.3rem;
    right: 0.3rem;
    img {
      width: 1.3rem;
      margin-bottom: 0.18rem;
    }
  }
}
.block {
  width: 7.1rem;
  margin: 0 auto;
  background-size: 100%, 2.37rem;
  background-position: center 0.1rem, 4.3rem 0rem;
  background-repeat: no-repeat;
  border-radius: 0 0 0.3rem 0.3rem;
  margin-bottom: 0.2rem;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 0.2rem;
    .icon {
      height: 0.36rem;
      margin-right: 0.15rem;
    }
    .text {
      height: 0.34rem;
    }
  }
}
.rights {
  padding: 0.36rem 0.3rem 0.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193267/35/38277/188945/653a1349Fc500152e/29edaab05f171f44.png), url(//img10.360buyimg.com/imgzone/jfs/t1/91614/21/36103/10131/653a035dFe9a8052d/4c1e5be8cdeafeaf.png);
}
.activity {
  padding: 0.36rem 0.2rem 0.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193267/35/38277/188945/653a1349Fc500152e/29edaab05f171f44.png), url(//img10.360buyimg.com/imgzone/jfs/t1/172282/12/40797/10809/6539e4d5F5efc8691/3343aba0ce20482b.png);
}
.introduction {
  width: 6.5rem;
  height: 2rem;
  background-size: 100%;
  position: relative;
  .tip {
    position: absolute;
    left: 0.45rem;
    top: 1.22rem;
    width: 3.5rem;
    height: 0.38rem;
    line-height: 0.38rem;
    font-size: 0.2rem;
    text-align: center;
    color: #fff;
    background-color: #e73b41;
    border-radius: 0.08rem;
    &.gray {
      background-color: #c3c3c3;
    }
    .tipText {
      margin-left: -0.1rem;
      white-space: nowrap;
      font-size: 0.2rem;
      transform: scale(0.85);
    }
  }
  .btn {
    position: absolute;
    top: 0.35rem;
    right: 0.47rem;
    img {
      width: 1.3rem;
    }
  }
}
.prize-status {
  position: relative;
  padding: 0.1rem;
  height: 2.6rem;
  background-color: #fff;
  border-radius: 0.2rem;
  .prize-box {
    display: flex;
    align-items: center;
    .prize-img {
      margin-right: 0.2rem;
      width: 2rem;
      height: 2rem;
      background-size: 100%;
      background-repeat: no-repeat;
      border-radius: 0.2rem;
    }
    .prize-info {
      line-height: 0.4rem;
      .prize-name {
        font-size: 0.31rem;
        font-weight: bold;
        color: #262626;
        width: 4rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .prize-price {
        font-size: 0.24rem;
        color: #262626;
      }
      .prize-text {
        font-size: 0.24rem;
        color: #999999;
      }
    }
    .btn {
      position: absolute;
      right: 0.28rem;
      bottom: 0.6rem;
      width: 1.35rem;
      height: 0.55rem;
      line-height: 0.55rem;
      text-align: center;
      border-radius: 0.3rem;
      background-color: #ff3333;
      color: #fff;
      font-size: 0.24rem;
      &.gray {
        background-color: #c3c3c3;
      }
    }
  }
  .time {
    text-align: center;
    font-size: 0.24rem;
    color: #999999;
  }
}

.pay {
  width: 100%;
  height: 2rem;
  background-color: #000;
  color: #fff;
  text-align: center;
  line-height: 2rem;
}
</style>
