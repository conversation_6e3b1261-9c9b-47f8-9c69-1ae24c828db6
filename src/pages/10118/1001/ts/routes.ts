// 定义路由组件.
import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/', // 主页
    name: 'index',
    component: () => import('../views/index.vue'),
  },
  {
    path: '/promotion',
    name: 'promotion',
    component: () => import('../views/promotion.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
    name: 'notFound',
    hidden: true,
  },
];

function initializeRouter() {
  const router = createRouter({
    history: createWebHistory(`${process.env.VUE_APP_PATH_PREFIX_NO_CDN}10118/1001/`),
    routes,
  });
  // router.beforeEach((to, from, next) => {
  //   // 把from中的query参数传递给to
  //   next();
  // });
  return router;
}

export default () => initializeRouter();
