import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { reactive, ref } from 'vue';

export const rulePopup = ref(false);
export const ruleText = ref('');
export const editAddressPopup = ref(false); // 填写地址弹窗
export const addressInfo = ref({}); // 填写地址弹窗数据
export const showAddressInfo = ref(false); // 查看地址
export const currentAddress = ref({}); // 当前点击查看的收货地址
export const shopName = ref('xxx自营旗舰店');

// 展示活动规则，首次获取规则
export const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const { data } = await httpRequest.get('/common/getRule');
      closeToast();
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error) {
    closeToast();
    console.error();
  }
};
// 领取记录弹窗
export const recordPopup = ref(false);
// 领取攻略弹窗
export const strategyPopup = ref(false);
export const previewActivityData = ref({});
export const activityData = ref({} as any);
export const getActivityData = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10118/activityResponse');
    activityData.value = res.data;
    if (dayjs().isBefore(dayjs(res.data.orderStartTime))) {
      activityData.value = {
        ...activityData.value,
        status: 0,
      };
    } else if (dayjs().isAfter(dayjs(res.data.orderStartTime)) && dayjs().isBefore(dayjs(res.data.orderEndTime))) {
      activityData.value = {
        ...activityData.value,
        status: 1,
      };
    } else if (dayjs().isAfter(dayjs(res.data.orderStartTime))) {
      activityData.value = {
        ...activityData.value,
        status: 2,
      };
    }
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
export const userPrizeId = ref('');
export const savePhonePopup = ref(false);
export const planDesc = ref('');
// 领取京元宝
export const savePhone = (item: any) => {
  userPrizeId.value = item.userPrizeId;
  planDesc.value = JSON.parse(item.prizeContent).result.planDesc;
  // planDesc.value = '这里是描述这里是描述';
  savePhonePopup.value = true;
};

// 展示卡密
export const copyCardPopup = ref(false);
export const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  prizeImg: '',
});
// 展示礼品卡
export const showCardNum = (item: any) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = { ...prizeContent, prizeName, prizeImg }[item];
  });
  copyCardPopup.value = true;
};

export const prizes = ref([] as any);
// 获取奖品列表
export const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10118/receiveRecord');
    prizes.value = res.data;
    // prizes.value = [
    //   {
    //     activityPrizeId: '',
    //     address: '大连东软信息学院B3',
    //     addressId: '',
    //     city: '大连市',
    //     county: '甘井子区',
    //     createTime: 1747205200000,
    //     deliverName: '顺丰',
    //     deliverNo: '11111111111',
    //     deliveryStatus: 1,
    //     isFuLuWaitingReceive: true,
    //     mobile: '13666666666',
    //     orderId: '21312312321',
    //     prizeContent: '',
    //     prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/215393/22/4731/33329/61946651E535ea01f/5cee5951d6bd1612.png',
    //     prizeName: '这是一张优惠券',
    //     prizeType: 12,
    //     province: '辽宁省',
    //     realName: '测试人222',
    //     receive: 1,
    //     status: 1,
    //     userPrizeId: '1922274860037877762',
    //   },
    //   {
    //     activityPrizeId: '',
    //     address: '大连东软信息学院B3111111',
    //     addressId: '',
    //     city: '大连市1111111',
    //     county: '甘井子区111111',
    //     createTime: 1747205200000,
    //     deliverName: '顺丰',
    //     deliverNo: '11111111111',
    //     deliveryStatus: false,
    //     isFuLuWaitingReceive: true,
    //     mobile: '13666666666',
    //     orderId: '21312312321',
    //     prizeContent: '',
    //     prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/215393/22/4731/33329/61946651E535ea01f/5cee5951d6bd1612.png',
    //     prizeName: '这是一张优惠券',
    //     prizeType: 12,
    //     province: '辽宁省1111',
    //     realName: '测试人222',
    //     receive: 0,
    //     status: 2,
    //     userPrizeId: '1922274860037877762',
    //   },
    // ];
    closeToast();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
// dialog
export const dialogInfo = reactive({
  popup: false,
  title: '',
  content: '',
  btnText: '',
  onClick: undefined as undefined | (() => void),
});
export const showDialog = (config: { title: string; content?: string; btnText?: string; onClick?: () => void }) => {
  dialogInfo.title = config.title;
  dialogInfo.content = config.content || '';
  dialogInfo.btnText = config.btnText || '';
  dialogInfo.onClick = config.onClick;
  dialogInfo.popup = true;
};
