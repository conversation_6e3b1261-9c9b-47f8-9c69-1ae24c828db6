import { computed, reactive } from 'vue';

export const furnish = reactive({
  // 活动主图
  actBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 虚拟商品介绍图
  introductionImg: '',
  // 楼层标题图
  floorTitleImg: '',
  // 活动规则图
  ruleImg: '',
  // 领取记录图标
  recordImg: '',
  // 领取攻略图标
  strategyImg: '',
  // 预定成功页主图
  successKvImg: '',
  // 预定成功页颜色
  successBgColor: '',
  // 攻略图
  strategyPopupImg: '',
  shopNameColor: '', // 店铺名称颜色
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));

const introductionImg = computed(() => ({
  backgroundImage: furnish.introductionImg ? `url(${furnish.introductionImg})` : '',
}));

const successBgColor = computed(() => ({
  backgroundColor: furnish.successBgColor ?? '',
}));
const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));
export default {
  pageBg,
  introductionImg,
  successBgColor,
  shopNameColor,
};
