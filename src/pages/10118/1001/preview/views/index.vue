<template>
  <div class="bk" :style="furnishStyles.pageBg.value">
    <div class="kv">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="shop-name" :style="furnishStyles.shopNameColor.value">
        <span v-if="furnish.disableShopName === 1" >{{shopName}}</span>
      </div>
      <div class="btn-list">
        <img :src="furnish.ruleImg" alt="" @click="rulePopup = true" />
        <img :src="furnish.recordImg" alt="" @click="recordPopup = true" />
        <img :src="furnish.strategyImg" alt="" @click="strategyPopup = true" />
      </div>
    </div>
    <div class="block rights">
      <div class="title">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/231669/19/811/450/6539e4d5F8308b0c5/558a0e50e50cbf06.png" alt="" class="icon" />
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/294387/10/2095/3766/68249a88Fd9ac9812/45ea80fc047dfb92.png" alt="" class="text" />
      </div>
      <div class="introduction" :style="furnishStyles.introductionImg.value">
        <div class="tip">
          <div class="tipText">先下单锁定权益才可以参加活动哦</div>
        </div>
        <div class="btn">
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/190638/14/38667/8085/6539e4d4F244ea560/e7fba5e2f48f1086.png" alt="" />
        </div>
      </div>
    </div>
    <div class="block activity" v-if="previewActivityData.orderPrizeFlag" >
      <div class="title">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/231669/19/811/450/6539e4d5F8308b0c5/558a0e50e50cbf06.png" alt="" class="icon" />
        <img :src="furnish.floorTitleImg" alt="" class="text" />
      </div>
      <div class="prize-status">
        <div class="prize-box">
          <div class="prize-img" :style="{ backgroundImage: `url(${ previewActivityData?.prizeList ? previewActivityData?.prizeList[0]?.prizeImg : 0})` }"/>
          <div class="prize-info">
            <div class="prize-name">{{previewActivityData?.prizeList ? previewActivityData?.prizeList[0]?.prizeName : ''}}</div>
            <div class="prize-price">下单满{{previewActivityData?.orderPrice}}元即可免费领取</div>
            <div class="prize-text">奖品数量有限，先到先得</div>
            <div class="prize-text">剩余库存{{previewActivityData?.prizeList ? previewActivityData?.prizeList[0]?.sendTotalCount : ''}}</div>
          </div>
          <div class="btn" v-if="previewActivityData?.status === 1">立即下单</div>
          <div class="btn gray" v-else @click="joinOther(previewActivityData)">立即下单</div>
        </div>
        <div class="time">
          <span>下单时间：{{ dayjs(previewActivityData?.orderStartTime).format('YY.MM.DD') }}-{{ dayjs(previewActivityData?.orderEndTime).format('YY.MM.DD') }}</span>
        </div>
      </div>
    </div>
    <div class="tips-gray">支付状态更新可能会有延迟。若未收到优惠券，请刷新页面，或稍后进入活动页面查看领取记录。</div>
  </div>
  <!-- <div class="pay" @click="toPay">去支付</div> -->
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { inject, reactive, ref } from 'vue';
import furnishStyles, { furnish } from '../../ts/furnishStyles';
import { DecoData } from '@/types/DecoData';
import { previewActivityData, rulePopup, strategyPopup, recordPopup, showDialog, shopName } from '../../ts/logic';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { checkThreshold } from '@/components/Threshold/ts/logic';
import { type } from 'os';

const baseInfo = inject('baseInfo') as BaseInfo;
const decoData = inject('decoData') as DecoData;

const router = useRouter();
const route = useRoute();

const join = () => {
  if (previewActivityData?.value.status === 1) {
    router.push({
      path: '/promotion',
      query: { shopId: baseInfo.shopId, activityId: baseInfo.activityId },
    });
  }
};
const joinOther = (item: any) => {
  if (item.status === 0) {
    showDialog({
      title: '活动还没开始哦',
      content: `活动开始时间：${dayjs(item.orderStartTime).format('YYYY-MM-DD HH:mm:ss')}`,
    });
  } else if (item.status === 2) {
    showToast({
      message: '活动已结束',
    });
  }
};
</script>

<style scoped lang="scss">
.bk {
  min-height: 100vh;
  position: relative;
  padding-bottom: 0.2rem;
}
.tips-gray {
  color: #999999;
  font-size: 0.2rem;
  width: 7rem;
  margin: 0 auto;
}
.kv {
  position: relative;
  margin-bottom: 0.3rem;
  .kv-img {
    width: 100%;
  }
  .shop-name{
    position: absolute;
    left: 0.3rem;
    top: 0.3rem;
    font-size: 0.24rem;
    color: #fff;
  }
  .btn-list {
    position: absolute;
    top: 0.3rem;
    right: 0.3rem;
    img {
      width: 1.3rem;
      margin-bottom: 0.18rem;
    }
  }
}
.block {
  width: 7.1rem;
  margin: 0 auto;
  background-size: 100%, 2.37rem;
  background-position: center 0.1rem, 4.3rem 0rem;
  background-repeat: no-repeat;
  border-radius: 0 0 0.3rem 0.3rem;
  margin-bottom: 0.2rem;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 0.2rem;
    .icon {
      height: 0.36rem;
      margin-right: 0.15rem;
    }
    .text {
      height: 0.34rem;
    }
  }
}
.rights {
  padding: 0.36rem 0.3rem 0.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193267/35/38277/188945/653a1349Fc500152e/29edaab05f171f44.png), url(//img10.360buyimg.com/imgzone/jfs/t1/91614/21/36103/10131/653a035dFe9a8052d/4c1e5be8cdeafeaf.png);
}
.activity {
  padding: 0.36rem 0.2rem 0.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193267/35/38277/188945/653a1349Fc500152e/29edaab05f171f44.png), url(//img10.360buyimg.com/imgzone/jfs/t1/172282/12/40797/10809/6539e4d5F5efc8691/3343aba0ce20482b.png);
}
.introduction {
  width: 6.5rem;
  height: 2rem;
  background-size: 100%;
  position: relative;
  .tip {
    position: absolute;
    left: 0.45rem;
    top: 1.22rem;
    width: 3.5rem;
    height: 0.38rem;
    line-height: 0.38rem;
    font-size: 0.2rem;
    text-align: center;
    color: #fff;
    background-color: #e73b41;
    border-radius: 0.08rem;
    &.gray {
      background-color: #c3c3c3;
    }
  }
  .btn {
    position: absolute;
    top: 0.35rem;
    right: 0.47rem;
    img {
      width: 1.3rem;
    }
  }
}

.prize-status {
  position: relative;
  padding: 0.1rem;
  height: 2.6rem;
  background-color: #fff;
  border-radius: 0.2rem;
  .prize-box {
    display: flex;
    align-items: center;
    .prize-img {
      margin-right: 0.2rem;
      width: 2rem;
      height: 2rem;
      background-size: 100%;
      background-repeat: no-repeat;
      border-radius: 0.2rem;
    }
    .prize-info {
      line-height: 0.4rem;
      .prize-name {
        font-size: 0.31rem;
        font-weight: bold;
        color: #262626;
        width: 4rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .prize-price {
        font-size: 0.24rem;
        color: #262626;
      }
      .prize-text {
        font-size: 0.24rem;
        color: #999999;
      }
    }
    .btn {
      position: absolute;
      right: 0.28rem;
      bottom: 0.6rem;
      width: 1.35rem;
      height: 0.55rem;
      line-height: 0.55rem;
      text-align: center;
      border-radius: 0.3rem;
      background-color: #ff3333;
      color: #fff;
      font-size: 0.24rem;
      &.gray {
        background-color: #c3c3c3;
      }
    }
  }
  .time {
    text-align: center;
    font-size: 0.24rem;
    color: #999999;
  }
}

.pay {
  width: 100%;
  height: 2rem;
  background-color: #000;
  color: #fff;
  text-align: center;
  line-height: 2rem;
}
</style>
