<template>
  <div class="" v-if="isLoadingFinish">
    <RouterView></RouterView>
    <VanPopup teleport="body" v-model:show="rulePopup" :closeOnClickOverlay="false">
      <Rule></Rule>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="strategyPopup" :closeOnClickOverlay="false">
      <Strategy></Strategy>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="recordPopup" :closeOnClickOverlay="false">
      <Record></Record>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="dialogInfo.popup" :closeOnClickOverlay="false">
      <Dialog></Dialog>
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import { previewActivityData, rulePopup, strategyPopup, recordPopup, dialogInfo, ruleText, shopName } from '../ts/logic';
import Rule from '../components/Rule.vue';
import Strategy from '../components/Strategy.vue';
import Record from '../components/Record.vue';
import Dialog from '../components/Dialog.vue';
import { inject, nextTick, onMounted, onUnmounted, ref } from 'vue';
import { furnish } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';
import dayjs from 'dayjs';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const isLoadingFinish = ref(false);

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    let status = 0;
    if (data.orderStartTime && data.orderEndTime) {
      if (dayjs().isBefore(dayjs(data.orderStartTime))) {
        status = 0;
      } else if (dayjs().isAfter(dayjs(data.orderStartTime)) && dayjs().isBefore(dayjs(data.orderEndTime))) {
        status = 1;
      } else if (dayjs().isAfter(dayjs(data.orderStartTime))) {
        status = 2;
      }
    }
    const newPreviewActivityData = {
      orderPrizeFlag: data?.orderPrizeFlag,
      orderPrice: data?.orderPrice,
      prizeList: data?.prizeList,
      orderStartTime: data?.orderStartTime,
      orderEndTime: data?.orderEndTime,
      orderPrizeImg: data?.orderPrizeImg,
      status,
    };
    previewActivityData.value = newPreviewActivityData;
    ruleText.value = data.rules;
    shopName.value = data.shopName;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    console.log(data);
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    ruleText.value = activityData.rules;
    let status = 0;
    if (activityData.orderStartTime && activityData.orderEndTime) {
      if (dayjs().isBefore(dayjs(activityData.orderStartTime))) {
        status = 0;
      } else if (dayjs().isAfter(dayjs(activityData.orderStartTime)) && dayjs().isBefore(dayjs(activityData.orderEndTime))) {
        status = 1;
      } else if (dayjs().isAfter(dayjs(activityData.orderStartTime))) {
        status = 2;
      }
    }
    const newPreviewActivityData = {
      orderPrizeFlag: activityData?.orderPrizeFlag,
      orderPrice: activityData?.orderPrice,
      prizeList: activityData?.prizeList,
      orderStartTime: activityData?.orderStartTime,
      orderEndTime: activityData?.orderEndTime,
      orderPrizeImg: activityData?.orderPrizeImg,
      status,
    };
    previewActivityData.value = newPreviewActivityData;
    ruleText.value = activityData.rules;
    shopName.value = activityData.shopName;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss"></style>
