<template>
  <div class="popup-bk">
    <div class="title">
      <div class="close" @click="dialogInfo.popup = false"></div>
    </div>
    <div class="content">
      <div>
        <div class="dialog-title">{{ dialogInfo.title }}</div>
        <div class="tip">{{ dialogInfo.content }}</div>
      </div>
    </div>
    <div v-if="dialogInfo.btnText" class="bottom-btn" @click="dialogInfo.onClick">{{ dialogInfo.btnText }}</div>
  </div>
</template>

<script lang="ts" setup>
import { dialogInfo } from '../ts/logic';
</script>

<style scoped lang="scss">
.popup-bk {
  width: 6.32rem;
  height: 5rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/231719/36/2293/25039/654afcebFa8994e8d/2993427b15e5dbd1.png) no-repeat;
  background-size: 100%;
  .title {
    width: 100%;
    height: 0.74rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 0.2rem 0 0.8rem;
    img {
      height: 0.34rem;
    }
    .close {
      width: 0.4rem;
      height: 0.4rem;
    }
  }
  .content {
    width: 5.6rem;
    height: 3rem;
    margin: 0 auto;
    padding: 0.6rem 0.2rem 0.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    .dialog-title {
      font-size: 0.34rem;
      color: #262626;
      margin-bottom: 0.1rem;
    }
    .tip {
      font-size: 0.24rem;
      color: #999999;
    }
  }
  .bottom-btn {
    width: 4.5rem;
    height: 0.95rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/233961/3/2185/12222/654afcebF724e51f3/d15d2b82b4d44035.png) no-repeat;
    background-size: 100%;
    margin: 0 auto 0;
    text-align: center;
    font-size: 0.36rem;
    color: #fff198;
    line-height: 0.9rem;
  }
}
</style>
