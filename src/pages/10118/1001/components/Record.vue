<template>
  <div class="popup-bk" :style="{ backgroundImage: `url(${bkMap[actTab]})` }">
    <div class="title">
      <div class="tab" @click="actTab = 1"></div>
      <div class="tab" @click="actTab = 2"></div>
      <div class="close" @click="recordPopup = false"></div>
    </div>
    <div class="content" v-if="actTab === 1">
      <div>
        <div v-for="(item, index) in prizes" :key="index" class="prize">
          <div v-if="item.status === 1">
              <div class="info">
              <img :src="item.prizeImg" alt="" class="show-img" />
              <div class="detail">
                <div class="prize-info">赠品名称：{{ item.prizeName }}</div>
                <div class="prize-info">领取时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                <div class="btn" @click="gotoMyCoupon()"  v-if="item.receive === 1">去使用</div>
              </div>
               <div class="status">
                <div class="red" v-if="item.receive === 2">发放失败</div>
                <div class="green" v-if="item.receive === 1">已发放</div>
              </div>
            </div>
          </div>
          <div v-if="item.status === 2">
            <div class="type">
              <span>{{ prizeType[item.prizeType] }}</span>
              <span>{{ item.userPrizeId ? item.userPrizeId : '' }}</span>
            </div>
            <div class="info">
              <img :src="item.prizeImg" alt="" class="show-img" />
              <div class="detail">
                <div class="name">{{ item.prizeName }}</div>
                <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
              </div>
              <div class="status" v-if="item.prizeType === 3">
                <div class="orange" v-if="!item.deliveryStatus && item.receive === 0">待发货</div>
                <div class="red" v-if="item.receive === 2">发放失败</div>
                <div class="green" v-if="item.deliveryStatus && item.receive === 1">已发货</div>
                <div class="blue" @click="clickShowAddress(item)">查看地址</div>
              </div>
              <div class="status" v-else-if="item.prizeType === 7">
                <div class="orange" v-if="item.receive === 0">待发放</div>
                <div class="green" v-else-if="item.receive === 1">已发放</div>
                <div class="red" v-else-if="item.receive === 2">发放失败</div>
                <div class="blue" @click="showCardNum(item)" v-if="item.receive === 1">如何兑换</div>
              </div>
              <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
                <div class="orange" v-if="item.receive === 0">待发放</div>
                <div class="green" v-else-if="item.receive === 1">已发放</div>
                <div class="red" v-else-if="item.receive === 2">发放失败</div>
                <div class="blue" @click="exchangePlusOrAiqiyi" v-if="item.receive === 1">立即兑换</div>
              </div>
              <div class="status" v-else-if="item.prizeType === 12">
                <div class="orange" v-if="item.receive === 0">待发放</div>
                <div class="green" v-else-if="item.receive === 1">已发放</div>
                <div class="red" v-else-if="item.receive === 2">发放失败</div>
                <div class="blue" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive && item.receive === 1">填写信息</div>
              </div>
              <div class="status" v-else>
                <div class="orange" v-if="item.receive === 0">待发放</div>
                <div class="green" v-else-if="item.receive === 1">已发放</div>
                <div class="red" v-else-if="item.receive === 2">发放失败</div>
              </div>
            </div>
            <div v-if="item.orderId" class="orderBox">
              <div>关联订单：{{ item.orderId }}</div>
              <div class="copy-btn" :copy-text="item.orderId">复制</div>
            </div>
            <div class="deliver" v-if="item.prizeType === 3 && item.deliveryStatus">
              <div>
                <div>快递公司：{{item.deliverName}}</div>
                <div>快递单号：{{item.deliverNo}}</div>
              </div>
              <div class="copy-btn" :copy-text="item.deliverNo">复制单号</div>
            </div>
          </div>
        </div>
        <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
      </div>
      <div v-if="recordData?.length === 0" class="no-data">暂无记录</div>
    </div>
    <div class="content-2" v-if="actTab === 2">
      <div class="info" v-if="prizeRecord.createTime">
        <div>
          <div class="name">{{ prizeRecord.nickName }}</div>
          <div class="time">{{ dayjs(prizeRecord.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
        </div>
        <div class="finish">{{ prizeRecord.detail }}</div>
      </div>
      <div v-else class="no-data">暂无记录</div>
      <div class="content-2-desc">支付记录请至“我的-钱包-账单”中查看</div>
    </div>
    <div class="bottom-btn" @click="recordPopup = false"></div>
  </div>
  <VanPopup v-model:show="showAddressInfo" >
    <AddressInfo v-if="showAddressInfo"/>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup"/>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard v-if="copyCardPopup"/>
  </VanPopup>
</template>

<script lang="ts" setup>
import { closeToast, showLoadingToast, showToast } from 'vant';
import { recordPopup, showAddressInfo, currentAddress, savePhone, savePhonePopup, copyCardPopup, cardDetail, showCardNum, getRecord, prizes } from '../ts/logic';
import { httpRequest } from '@/utils/service';
import { inject, reactive, ref } from 'vue';
import Clipboard from 'clipboard';
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import dayjs from 'dayjs';
import AddressInfo from './AddressInfo.vue';
import SavePhone from './SavePhone.vue';
import CopyCard from './CopyCard.vue';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

const isPreview = inject('isPreview') as boolean;
const baseInfo = inject('baseInfo') as BaseInfo;

const bkMap = {
  1: '//img10.360buyimg.com/imgzone/jfs/t1/236709/35/1503/161521/654375deF5cb0cd6c/af81210df9b7a4e7.png',
  2: '//img10.360buyimg.com/imgzone/jfs/t1/201968/33/38021/87425/654458e3F45ad4f19/e6fb5651f9282883.png',
};
const actTab = ref(1);

const userName = ref(window.sessionStorage.getItem(constant.LZ_JD_USER_NAME) as string);
const prizeType = {
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  12: '京元宝',
};
interface List {
  giftImage: string;
  giftName: string;
  orderId: string;
  receiveTime: string;
  orderStatus: string;
}

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
// 去使用优惠券
const gotoMyCoupon = () => {
  window.jmfe.toMyCoupon();
};

const prizeRecord = reactive({
  nickName: '',
  detail: '',
  createTime: '',
});
const getPrizeRecord = async () => {
  try {
    const res = await httpRequest.post('/10118/prizeRecord');
    if (res.data) {
      prizeRecord.nickName = res.data?.nickName;
      prizeRecord.detail = res.data?.detail;
      prizeRecord.createTime = res.data?.createTime;
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
// 查看地址
const clickShowAddress = (item: any) => {
  showAddressInfo.value = true;
  currentAddress.value = {
    realName: item.realName,
    mobile: item.mobile,
    province: item.province,
    city: item.city,
    county: item.county,
    address: item.address,
  };
};
if (!isPreview) {
  getRecord();
  getPrizeRecord();
}
</script>

<style scoped lang="scss">
.popup-bk {
  width: 6.32rem;
  height: 9rem;
  background-repeat: no-repeat;
  background-size: 100%;
  .title {
    width: 100%;
    height: 0.74rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.1rem 0 0.6rem;
    .tab {
      width: 2.5rem;
      height: 0.6rem;
    }
    img {
      height: 0.34rem;
    }
    .close {
      width: 0.4rem;
      height: 0.4rem;
    }
  }
  .content {
    width: 6.32rem;
    height: 7rem;
    margin: 0 auto;
    padding: 0.2rem;
    overflow-y: scroll;
    .prize {
      background: #ffffff;
      margin-bottom: 0.1rem;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.16rem;
        padding-bottom: 0.16rem;
        border-bottom: 0.02rem dashed #eee;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.24rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;

        .show-img {
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.2rem;

          .name {
            font-size: 0.28rem;
            color: #ff3333;
          }

          .time {
            color: #999999;
            font-size: 0.2rem;
            margin-top: 0.2rem;
          }
          .prize-info {
            color: #999999;
            font-size: 0.2rem;
          }
          .btn {
            margin-top: 0.1rem;
            width: 1.15rem;
            height: 0.4rem;
            background-color: #ff3333;
            border-radius: 0.2rem;
            font-size: 0.2rem;
            color: #ffffff;
            text-align: center;
            line-height: 0.4rem;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;

          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #0083ff;
          }

          .red {
            color: #ff3333;
          }
        }
      }
      .deliver, .orderBox{
        margin-left: 1.2rem;
        margin-right: 0.22rem;
        color: #999999;
        font-size: 0.2rem;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
      }
      .copy-btn {
        color: #0083ff;
        font-size: 0.24rem;
      }
    }

    .no-data {
      text-align: center;
      line-height: 35vh;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
    .list {
      .num-title {
        font-size: 0.24rem;
        color: #666666;
        margin-bottom: 0.1rem;
      }
      .num-list {
        position: relative;
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 0.2rem;
        overflow: hidden;
        padding: 0.1rem;
        margin-bottom: 0.1rem;
        .prize-img {
          width: 1.2rem;
          height: 1.2rem;
          background-color: #fff8f0;
          border-radius: 0.1rem;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            padding: 0.15rem;
          }
        }
        .info {
          padding-left: 0.17rem;
          flex: 1;
          font-size: 0.22rem;
          color: #262626;
          line-height: 0.4rem;
          div {
            // 文字最多2行展示
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
          .copy {
            display: flex;
            align-items: center;
            div {
              max-width: 3.75rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .copy-btn {
              color: #3399ff;
              width: max-content;
              padding: 0 0.1rem;
            }
          }
        }
        .cancel {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.8);
          padding: 0.3rem 0.25rem 0;
          color: #fff;
          font-size: 0.22rem;
          .fs28 {
            font-size: 0.28rem;
            margin-bottom: 0.05rem;
          }
        }
      }
    }
  }
  .content-2 {
    width: 6.32rem;
    height: 7rem;
    margin: 0 auto;
    padding: 0.7rem 0.45rem 0;
    position: relative;
    .info {
      height: 1.2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.24rem;
      border-bottom: solid 0.01rem #e6e6e6;
      .name {
        color: #262626;
        margin-bottom: 0.1rem;
      }
      .time {
        font-size: 0.2rem;
        color: #999999;
      }
      .finish {
        color: #ff3333;
      }
    }
    .content-2-desc {
      position: absolute;
      bottom: 0.2rem;
      left: 0;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0.24rem;
      color: #999999;
      scale: 0.7;
      white-space: nowrap;
    }
  }
  .no-data {
    width: 100%;
    height: 5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.24rem;
    color: #999999;
  }
  .bottom-btn {
    width: 4.5rem;
    height: 0.9rem;
    margin: 0.4rem auto 0;
  }
}
</style>
