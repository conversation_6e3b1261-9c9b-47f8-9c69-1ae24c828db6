<template>
  <div class="address-edit">
    <icon class="close-icon" name="close" color="gray" size="25" @click="editAddressPopup = false" />
    <van-config-provider :theme-vars="customAddressEdit">
      <van-address-edit :area-list="areaList" tel-maxlength="11" detail-maxlength="30" save-button-text='提交' show-search-result :area-columns-placeholder="['请选择', '请选择', '请选择']" area-placeholder="省市区县、乡镇等" @save="onSave" :address-info="addressInfo" />
    </van-config-provider>
    <div class="tips-text">请认真核实所填信息，提交后将无法对信息进行修改</div>
  </div>
</template>

<script lang="ts" setup>
import { closeToast, Locale, showLoadingToast, showToast, Icon } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { editAddressPopup, addressInfo } from '../ts/logic';
import ExchangeTips from '../components/ExchangeTips.vue';
import { areaList } from '@vant/area-data';
import { onMounted, reactive, ref, watch, inject } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { httpRequest } from '@/utils/service';

const router = useRouter();
const route = useRoute();
const btnName = ref('确认');
const deleteId = ref('');
const searchResult = ref([]);
const baseInfo = inject('baseInfo') as BaseInfo;
const toPay = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    closeToast();
    const { data } = await httpRequest.post('/10118/pay', {
      activityId: baseInfo.activityId,
      amount: 1,
      shopId: baseInfo.shopId,
      source: '01',
      token: sessionStorage.getItem(constant.LZ_JD_TOKEN),
    });
    const param = {
      orderId: data.orderId,
      paySign: data.paySign,
      returnUrl: encodeURIComponent(window.location.href),
    };
    const payUrl = `openapp.jdmobile://virtual?params={"category":"jump","des":"jdmp","appId":"2B43F9A518AE09BAE8789053047A685E","vapptype":"1","path":"pages/saas-pay/saas-pay.html","pageAlias":"","param":${JSON.stringify(param)}}`;
    window.location.href = payUrl;
    console.log(payUrl);
  } catch (error: any) {
    closeToast();
    if (error.message) {
      if (error.message === '请先填写地址') {
        editAddressPopup.value = true;
      } else {
        showToast({
          message: error.message,
        });
      }
    }
  }
};
// 新增地址
const onSave = async (info: any) => {
  console.log(info, '====info');
  try {
    const { data } = await httpRequest.post('/10118/userAddressInfo', {
      realName: info.name,
      mobile: info.tel,
      province: info.province,
      city: info.city,
      county: info.county,
      address: info.addressDetail,
      addressCode: info.areaCode,
    });
    editAddressPopup.value = false;
    toPay();
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};

const customAddressEdit = reactive({
  fieldLabelWidth: '1.6rem !important',
  fieldLabelColor: '#333333',
});
const messages = {
  'zh-CN': {
    vanAddressEdit: {
      name: '收货人',
      tel: '手机号码',
      area: '选择省/市/区',
      nameEmpty: '请输入收货人姓名',
      telInvalid: '请输入正确的手机号码',
    },
  },
};

Locale.add(messages);

onMounted(() => {
  const inputEl = document.querySelector('.van-field__control');
  inputEl?.setAttribute('maxlength', '20');
});
</script>

<style scoped lang="scss">
.address-edit{
  position: relative;
  padding:0 0.2rem 0.2rem 0.2rem;
  background-color: #fff;
  border-radius: 0.2rem;
  text-align: center;
  .close-icon {
    position: absolute;
    right: 0.2rem;
    z-index: 1;
    top:0.1rem
  }
}
.tips-text {
  margin-top: -0.8rem;
  color: #ff3333;
  font-size: 0.24rem;
}
</style>
