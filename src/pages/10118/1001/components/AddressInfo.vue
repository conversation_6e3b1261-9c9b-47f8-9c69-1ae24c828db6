<template>
   <div class="address-info">
    <icon class="close-icon" name="close" color="gray" size="25" @click="showAddressInfo = false" />
    <div class="info-line">
      <div class="title">收货人</div>
      <div class="info">{{currentAddress.realName}}</div>
    </div>
    <div class="info-line">
      <div class="title">手机号码</div>
      <div class="info">{{currentAddress.mobile}}</div>
    </div>
    <div class="info-line">
      <div class="title">省/市/区</div>
      <div class="info">{{currentAddress.province}}{{currentAddress.city}}{{currentAddress.county}}</div>
    </div>
    <div class="info-line">
      <div class="title">详细地址</div>
      <div class="info">{{currentAddress.address}}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { showAddressInfo, currentAddress } from '../ts/logic';
import { Icon } from 'vant';
</script>

<style scoped lang="scss">
.address-info{
  position: relative;
  padding: 0.5rem;
  background-color: #fff;
  border-radius: 0.2rem;
  width: 6.32rem;
  line-height: 0.5rem;
  .close-icon {
    position: absolute;
    right: 0.2rem;
    z-index: 1;
    top:0.1rem
  }
  .info-line {
    display: flex;
    gap: 0.35rem;
    .title {
      color: #999999;
      white-space: nowrap;
      text-align: right;
      width: 1.3rem;
    }
    .info {
      text-align: left;
      flex: 1;
    }
  }
}
</style>
