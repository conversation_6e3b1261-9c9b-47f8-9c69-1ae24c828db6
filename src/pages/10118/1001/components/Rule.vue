<template>
  <div class="popup-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/221620/27/31025/2329/6540be03F38dc20f7/f5184ff97d707746.png" alt="" />
      <div class="close" @click="rulePopup = false"></div>
    </div>
    <div class="content">
      <div v-html="ruleText"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { rulePopup, ruleText } from '../ts/logic';
</script>

<style scoped lang="scss">
.popup-bk {
  width: 6.32rem;
  height: 8.17rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/85534/11/36303/11866/6540be03F2cff3d43/4b75ae468a665bd8.png) no-repeat;
  background-size: 100%;
  .title {
    width: 100%;
    height: 0.74rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.2rem 0 0.8rem;
    img {
      height: 0.34rem;
    }
    .close {
      width: 0.4rem;
      height: 0.4rem;
    }
  }
  .content {
    width: 5.6rem;
    height: 7.05rem;
    margin: 0 auto;
    padding: 0.2rem 0;
    div {
      height: 100%;
      overflow-y: scroll;
      font-size: 0.24rem;
      color: #262626;
      white-space: pre-wrap;
      padding: 0 0.2rem;
    }
  }
}
</style>
