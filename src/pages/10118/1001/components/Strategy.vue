<template>
  <div class="popup-bk">
    <div class="title">
      <div class="close" @click="strategyPopup = false"></div>
    </div>
    <div class="content">
      <img :src="furnish.strategyPopupImg" alt="" />
    </div>
    <div class="bottom-btn" @click="strategyPopup = false"></div>
  </div>
</template>

<script lang="ts" setup>
import { strategyPopup } from '../ts/logic';
import { furnish } from '../ts/furnishStyles';
</script>

<style scoped lang="scss">
.popup-bk {
  width: 6.32rem;
  height: 8.5rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/206060/9/37974/39411/654218bdF5c852a93/0a155fc311e7e7fd.png) no-repeat;
  background-size: 100%;
  .title {
    width: 100%;
    height: 0.74rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 0.2rem 0 0.8rem;
    img {
      height: 0.34rem;
    }
    .close {
      width: 0.4rem;
      height: 0.4rem;
    }
  }
  .content {
    width: 5.6rem;
    height: 6.45rem;
    margin: 0 auto;
    padding: 0.2rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .bottom-btn {
    width: 4.5rem;
    height: 0.9rem;
    margin: 0.4rem auto 0;
  }
}
</style>
