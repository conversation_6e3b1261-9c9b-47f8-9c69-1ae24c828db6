<template>
  <div class="">
    <RouterView></RouterView>
    <VanPopup teleport="body" v-model:show="rulePopup" :closeOnClickOverlay="false">
      <Rule></Rule>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="strategyPopup" :closeOnClickOverlay="false">
      <Strategy></Strategy>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="recordPopup" :closeOnClickOverlay="false">
      <Record v-if="recordPopup"></Record>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="dialogInfo.popup" :closeOnClickOverlay="false">
      <Dialog></Dialog>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="editAddressPopup" :closeOnClickOverlay="false">
      <EditAddress></EditAddress>
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import { rulePopup, editAddressPopup, strategyPopup, recordPopup, dialogInfo } from './ts/logic';
import EditAddress from './components/EditAddress.vue';
import Rule from './components/Rule.vue';
import Strategy from './components/Strategy.vue';
import Record from './components/Record.vue';
import Dialog from './components/Dialog.vue';
</script>

<style scoped lang="scss"></style>
