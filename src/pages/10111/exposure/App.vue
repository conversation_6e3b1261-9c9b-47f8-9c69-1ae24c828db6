<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { computed, inject, onMounted, onUnmounted, ref } from 'vue';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { DecoData } from '@/types/DecoData';

const query = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
});

const total = ref(0);
const skuList = ref([]);
const loading = ref(false); // 加一个loading
const hasNext = computed(() => total.value > 0 && skuList.value.length < total.value);

const observer = ref<IntersectionObserver | null>(null); // 移到外面
const loadMoreElement = ref<Element | null>(null);

const decoData = inject('decoData') as DecoData;
const { actBg } = decoData;
console.log(actBg);

const getSkuList = async () => {
  if (loading.value) return;
  loading.value = true;
  try {
    const { data } = await httpRequest.post('/10111/getExposureSkuPage', query.value);
    skuList.value.push(...data.records);
    total.value = data.total;
  } finally {
    loading.value = false;
  }
};

const onNextPage = () => {
  if (loading.value || !hasNext.value) return;
  query.value.pageNum++;
  getSkuList();
};

const initObserver = () => {
  if (observer.value) {
    observer.value.disconnect();
  }
  observer.value = new IntersectionObserver(
    (entries) => {
      if (entries[0].isIntersecting) {
        onNextPage();
      }
    },
    {
      root: null,
      rootMargin: '100px', // 提前100px触发
      threshold: 0, // 只要进入就触发
    },
  );
  if (loadMoreElement.value) {
    observer.value.observe(loadMoreElement.value);
  }
};

onMounted(() => {
  loadMoreElement.value = document.querySelector('.load-more');
  if (loadMoreElement.value) {
    initObserver();
  }
});

onUnmounted(() => {
  if (observer.value) {
    observer.value.disconnect();
  }
});

getSkuList();
</script>

<template>
  <div class="body">
    <img class="kv" :src="actBg" alt="" />
    <div class="sku-list">
      <div class="sku-item" v-for="item in skuList" :key="item.skuId" @click="gotoSkuPage(item.skuId)">
        <img class="sku-cover" :src="item.skuMainPicture" :alt="item.skuName" v-lazy="img" />
        <div class="sku-name">{{ item.skuName }}</div>
        <div class="sku-price">
          <span class="p-sign">¥</span>
          {{ item.jdPrice }}
        </div>
      </div>
    </div>
    <div class="load-more">
      {{ hasNext ? (loading ? '加载中...' : '加载更多...') : '没有更多了' }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.body {
  background-color: rgb(245, 245, 245);
  overflow: auto;
}

.kv {
  width: 100%;
}

.sku-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 0.2rem;
  gap: 0.2rem;

  .sku-item {
    position: relative;
    background-color: #fff;
    padding: 0.2rem;
    border-radius: 0.2rem;
    overflow: hidden;
    cursor: pointer;

    .sku-cover {
      width: 100%;
    }

    .sku-name {
      margin: 0.1rem 0;
      font-size: 0.24rem;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .sku-price {
      display: inline-block;
      color: #ff0f23;
      font-weight: bold;

      .p-sign {
        font-size: 0.25rem;
        font-weight: normal;
      }
    }

    .sku-link {
    }
  }
}

.load-more {
  text-align: center;
  padding: 0.2rem 0;
  color: #666;
  font-size: 0.2rem;
}
</style>
