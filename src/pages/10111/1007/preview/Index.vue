<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" v-if="isLoadingFinish">
    <img alt="暂无图片" :src="furnishStyles.kvImg.value.src"  class="kv-img"/>
    <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
      <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
    </div>
    <div class="header-btn-box">
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="toast">
        活动规则
      </div>
      <div class="header-btn" :style="furnishStyles.operateBtn.value"  @click="toast">
        我的奖品
      </div>
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="toast">
        我的订单
      </div>
    </div>
    <div class="time-icon-box">
      <van-count-down :time="activityEndTime - new Date().getTime()" format="DD:HH:mm:ss" class="time-box">
        <template #default="timeData">
          <span class="time-text">{{ timeData.days }}</span><span class="letter-space">天：</span>
          <span class="time-text">{{ timeData.hours }}</span><span class="letter-space">时：</span>
          <span class="time-text">{{ timeData.minutes }}</span><span class="letter-space">分：</span>
          <span class="time-text">{{ timeData.seconds }}</span><span class="letter-space">秒</span>
        </template>
      </van-count-down>
    </div>
    <van-swipe :loop="true" :show-indicators="false" :touchable="true">
      <div v-for="(item, index) in taskRequestList" :key="index">
        <van-swipe-item >
          <div class="goods-task-list-box"  @dragstart.prevent>
            <div class="goods-task-list-title" v-if="item.rule || (consumptionActivityType===0 && item.rule===0)"> 消费满<span class="goods-task-list-limit">{{item.rule}}</span>{{ activityTypeUnitText }}即送 </div>
            <div class="goods-task-list-title" :style="{'width':'4.6rem'}" v-else>消费有礼 完成任务即可领 </div>
            <div class="goods-task-goods">
              <div class="goods-task-goods-img-box">
                <img  class="goods-task-goods-img" :src="item.prizeList[0].prizeImg" alt="暂无图片"/>
              </div>
              <div class="goods-task-goods-info">
                <div class="goods-task-goods-name">{{ item.prizeList[0].prizeName }}</div>
                <div class="goods-task-goods-remain">奖品剩余：{{ item.prizeList[0].sendTotalCount }}份</div>
              </div>
            </div>
            <div class="task-progress-box">
              <div class="task-progress-num"></div>
            </div>
            <div class="task-progress-text">您已确认收货：0{{['元', '件', '单'][consumptionActivityType]}}</div>
            <div class="see-limit" @click="showToast('活动预览，仅供参考');"/>
          </div>
        </van-swipe-item>
      </div>
    </van-swipe>
    <div class="goods-list-box" v-if="assignGoodsFlag === 1  || assignGoodsFlag === 2">
        <div class="goods-list-title"></div>
        <div class="content-box" v-if="skuListPreview && skuListPreview.length !== 0" :class="skuListPreview.length === 1 ? 'content-box1' : ''">
            <div v-for="(item, index) in skuListPreview" :key="item.skuId + index" class="goods-box">
                <img :src="item.skuMainPicture" alt="暂无图片" class="goods-pic"/>
                <div class="goods-info-box">
                    <div class="goods-name">{{ item.skuName }}</div>
                    <div class="goods-info">
                        <div class="goods-price"><span class="price-unit">¥</span>{{ item.jdPrice }}</div>
                        <div class="bug-now-btn" @click="showToast('活动预览，仅供参考');"/>
                    </div>
                </div>
            </div>
            <div class="more-btn-all">
              <div class="more-btn" v-if="goodsList.length > 18" @click="toast">点我加载更多</div>
            </div>
        </div>
    </div>
    <div class="award-list-box" :style="{'top': assignGoodsFlag === 1  || assignGoodsFlag === 2 ? '1.5rem' : '0.5rem'}">
      <div class="award-content-null">
        暂无相关获奖信息哦~
      </div>
    </div>
    <div class="go-to-shop" v-if="!assignGoodsFlag">
      <div class="get-btn"  @click="showToast('活动预览，仅供参考');"/>
    </div>
    <!-- 获奖规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 我的订单弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyOrder @close="showMyPrize = false"></MyOrder>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject, watch } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import RulePopup from '../components/RulePopup.vue';
import { showToast } from 'vant';
import MyPrize from '../components/MyPrize.vue';
import MyOrder from '../components/MyOrder.vue';

interface Prize {
  prizeImg: string;
  prizeName: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
}

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const consumptionActivityType = ref(0); // 活动类型 0:满额有礼 1:满件有礼 2:满次有礼
const activityTypeUnitText = ref('元'); // 活动类型 0:满额有礼 1:满件有礼 2:满次有礼
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const showMyPrize = ref(false);
const taskRequestList = ref([{
  prizeList: [{
    prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/168358/21/42622/20362/65ee69ceF73042fd1/0db837403f91ecfe.png',
    prizeName: '清扬男士洗发水500g*2+100g*4',
    sendTotalCount: 200,
  }],
  rule: '',
  sort: 1,
}]);
type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const assignGoodsFlag = ref(1);
const goodsList = ref<Sku[]>([]); // 活动商品
const showSelect = ref(false);
const skuListPreview = ref([
  {
    jdPrice: '79.9',
    skuId: 1,
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/168358/21/42622/20362/65ee69ceF73042fd1/0db837403f91ecfe.png',
    skuName: '清扬男士洗发水500g*2+100g*4',
  },
  {
    jdPrice: '79.9',
    skuId: 2,
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/168358/21/42622/20362/65ee69ceF73042fd1/0db837403f91ecfe.png',
    skuName: '清扬男士洗发水500g*2+100g*4',
  },
  {
    jdPrice: '79.9',
    skuId: 3,
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/168358/21/42622/20362/65ee69ceF73042fd1/0db837403f91ecfe.png',
    skuName: '清扬男士洗发水500g*2+100g*4',
  },
  {
    jdPrice: '79.9',
    skuId: 4,
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/168358/21/42622/20362/65ee69ceF73042fd1/0db837403f91ecfe.png',
    skuName: '清扬男士洗发水500g*2+100g*4',
  },
]);
const activityEndTime = ref(new Date().getTime());
const ruleTest = ref('');
// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
const setDataInfo = (data:any) => { // 数据赋值
  console.log(showSelect.value, data, 'data============');
  // if (!showSelect.value) {
  const awardConditionsArr: any[] = [];
  if (data.taskRequestList.length !== 0) {
    taskRequestList.value = data.taskRequestList;
    data.taskRequestList.forEach((v:any) => {
      awardConditionsArr.push(v.rule);
    });
  }
  assignGoodsFlag.value = data.assignGoodsFlag;
  goodsList.value = data.skuList;
  skuListPreview.value = data.skuListPreview;
  // }
  if (data.orderStartTime.length !== 0) {
    activityEndTime.value = data.orderStartTime[1].$d ? new Date(data.orderStartTime[1].$d).getTime() : new Date(data.orderStartTime[1]).getTime();
  }
  if (data.rules) {
    ruleTest.value = data.rules;
  }
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    if (data.consumptionActivityType || data.consumptionActivityType === 0) {
      consumptionActivityType.value = data.consumptionActivityType;
      activityTypeUnitText.value = ['元', '件', '单'][data.consumptionActivityType];
    }
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    setDataInfo(data);
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
const toast = () => {
  showToast('活动预览，仅供查看');
};
// 截图监听
registerHandler('screen', () => {
  createImg();
  showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  if (activityData) {
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    if (decoData.consumptionActivityType || decoData.consumptionActivityType === 0) {
      consumptionActivityType.value = decoData.consumptionActivityType;
      activityTypeUnitText.value = ['元', '件', '单'][decoData.consumptionActivityType];
    }
    isLoadingFinish.value = true;
  }
});
// watch(() => consumptionActivityType, (newActivityType, oldActivityType) => {
//   if (newActivityType) {
//     taskRequestList.value = [
//       {
//         prizeList: [{
//           prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/168358/21/42622/20362/65ee69ceF73042fd1/0db837403f91ecfe.png',
//           prizeName: '清扬男士洗发水500g*2+100g*4',
//           sendTotalCount: 200,
//         }],
//         rule: '',
//         sort: 1,
//       }];
//   }
//
// }, { deep: true });
onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
img[src=""]
,img:not([src])
{
  opacity:0;
}
.bg {
  position: relative;
  padding-bottom: 2rem;
  min-height: 100vh;
  max-width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  .kv-img {
    position: relative;
    width: 7.5rem;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .header-btn-box {
    position: absolute;
    top: 2.4rem;
    right: 0;
    .header-btn {
      margin-bottom: 0.2rem;
      padding-left: 0.22rem;
      width: 1.26rem;
      height: 0.54rem;
      font-size: 0.24rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 40px 0 0 40px;
    }
  }
  .time-icon-box {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    margin-top: 0.5rem;
    width: 7.1rem;
    height: 1.1rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/214119/21/39275/11674/661609e3Fbe7a7b6b/f2cd39b5fea0860b.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    align-items: center;
    justify-content: space-between;
    .time-box {
      position: absolute;
      left: 2.5rem;
      font-family: PingFang-SC-Bold;
      font-size: 0.24rem;
      color: #ffffff;
      .time-text {
        color: #ffffff;
        color: #ffffff;
        padding:0.1rem;
        background: #e63e0a;
        border-radius: 0.1rem;
      }
      .letter-space {
        margin: 0 0.1rem;
      }
    }
  }
  .goods-task-list-box {
    position: relative;
    margin-left: 0.2rem;
    margin-top: 0.5rem;
    padding: 2.3rem 0.8rem 0.6rem;
    height:7.23rem;
    width: 7.13rem;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/91405/30/42223/27397/661609e3Faa3122b8/3e12ed8401d276ca.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .goods-task-list-title {
      position: absolute;
      transform: translateX(-50%);
      top: 1.6rem;
      left: 50%;
      font-family: PangMenZhengDao;
      font-size: 0.372rem;
      font-style: italic;
      letter-spacing: 0.02rem;
      color: #ffffff;
      .goods-task-list-limit {
        color: #FFFF00;
      }
    }
    .goods-task-goods {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 1.8rem;
      .goods-task-goods-img-box {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 2.15rem;
        height: 1.8rem;
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/120838/32/44047/6264/65ea7b35F00dea9cb/ecc159b3645bae61.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 0.1rem;
        .goods-task-goods-img {
          width: 1.88rem;
          height: 1.6rem;
        }
      }
      .goods-task-goods-info {
        .goods-task-goods-name {
          margin-bottom:0.2rem;
          width: 3rem;
          font-family: SourceHanSansCN-Medium;
          font-size: 0.332rem;
          font-style: italic;
          color: #fff;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp:2;
          -webkit-box-orient: vertical;
        }
        .goods-task-goods-remain {
          font-family: SourceHanSansCN-Medium;
          font-size: 0.288rem;
          font-style: italic;
          line-height: 0.13rem;
          color: #f5d88d;
          white-space: nowrap;
        }
      }
    }
    .task-progress-box {
      margin-top: 0.15rem;
      padding:0 0.72rem 0 0rem;
      display: flex;
      align-items: center;
      width: 5.38rem;
      height: 0.32rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/176738/40/39332/1969/661630d1F11aab313/9df55de866b64a7b.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .task-progress-num {
        width: 0;
        height: 0.36rem;
        background-image: linear-gradient(0deg, #74c321 0%, #bbeb89 100%);
        box-shadow: 0 0.02rem 0.02rem 0 rgba(223, 111, 48, 0.2), inset 0.025rem 0.043rem 0 0 rgba(207, 244, 167, 0.5), inset -0.035rem -0.035rem 0.05rem 0rem rgba(108, 185, 29, 0.7);
        border-radius: 0.201rem;
      }
    }
    .task-progress-text {
      margin-top: 0.16rem;
      font-family: SourceHanSansCN-Medium;
      font-size: 0.288rem;
      font-style: italic;
      line-height: 0.13rem;
      color: #f6d193;
    }
    .see-limit {
      margin-top: 0.28rem;
      margin-left: 0.6rem;
      width: 4.04rem;
      height: 0.94rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/102493/3/38236/35157/6507c818F055b6c9c/29175e79d30fce9e.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .goods-list-box {
    min-height:9.9rem;
    width: 7.12rem;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/105340/6/48322/23439/661609e3Ff46ecd9b/707fbeb658d045b8.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin:0.24rem auto 0;
    .goods-list-title {
      width: 5.2rem;
      height: 1.5rem;
    }
    .content-box {
      margin-left: 0.15rem;
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      width: 6.84rem;
      max-height: 8rem;
      overflow: auto;
    }
    .content-box1 {
      justify-content:flex-start;
      padding-left: 10px;
    }
    .more-btn-all{
      width:6.60rem;
      display:flex;
      justify-content:center;
      .more-btn {
        width: 1.8rem;
        height: 0.5rem;
        font-size: 0.2rem;
        color: #fff;
        background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
        background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
        border-radius: 0.25rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .goods-box {
      margin-bottom:0.2rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      width: 3.21rem;
      height: 3.83rem;
      background-image: linear-gradient(0deg,
        #fff7e2 0%,
        #ffffff 100%);
      box-shadow: 0.109rem 0.214rem 0.376rem 0.024rem
      rgba(221, 141, 27, 0.19);
      border-radius: 0.2rem;
      .goods-pic {
        margin-top:0.1rem;
        width: 2.6rem;
        height: 2.2rem;
        border-radius: 0.1rem;
      }
    }
    .goods-info-box {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      height: 1.16rem;
      width: 3.13rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/106280/6/40786/3055/65eebc9aFea075eb3/0f8b9b9ccd9221f4.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .goods-name {
        margin-bottom:0.1rem;
        width: 2.65rem;
        font-family: SourceHanSansCN-Regular;
        overflow:hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow:ellipsis;
        font-size: 0.26rem;
        font-style: italic;
        color: #ffffff;
      }
      .goods-info{
        width: 2.65rem;
        display:flex;
        justify-content: space-between;
        align-items: center;
        .goods-price {
          font-family: SourceHanSansCN-Bold;
          font-size: 0.3rem;
          font-style: italic;
          color: #ffffff;
          .price-unit {
            font-size: 0.2rem;
          }
        }
        .bug-now-btn {
          width: 1.43rem;
          height: 0.46rem;
          background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/179633/40/43510/9063/65ea7c7bF74a1bf1c/fd5a580f24a912c5.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }
  .award-list-box {
    padding: 1.5rem 0.5rem;
    margin-bottom: 0.5rem;
    position: relative;
    left: 50%;
    top: 1.5rem;
    transform: translateX(-50%);
    width: 7.12rem;
    height: 7.15rem;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/249589/38/7107/25633/661609e3F54212898/33cb92d18fd709c8.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-align: center;
    .award-content-null {
      height: 4.5rem;
      overflow-y: scroll;
      text-align: center;
      line-height: 3.9rem;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
    .award-content {
      height: 4.5rem;
      overflow-y: scroll;
    }
    .award-box {
      margin-bottom: 0.1rem;
      display: flex;
      justify-content: space-between;
      height: 0.78rem;
      line-height: 0.78rem;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t20353/283/457089967/998/1cdf509/5b0e3f7cNa6ff600e.png) left bottom repeat-x;
      color: rgb(0, 0, 0);
      font-size: 0.28rem;
      .award-name-box {
        display: flex;
        .user-img {
          margin-right: 0.1rem;
          width: 0.72rem;
          height: 0.72rem;
          border-radius: 50%;
        }
      }
    }
  }
  .go-to-shop {
    position: fixed;
    height: 0.88rem;
    line-height: 1.5rem;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .get-btn {
      width: 7.5rem;
      height: 0.88rem;
      background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/192572/12/27705/4069/6303376fE584f4c53/c1196beb18066383.png");
      background-size: 100%;
      background-repeat: no-repeat;
      font-family: MiSans-Regular;
      text-align: center;
      font-size: 0.53rem;
      letter-spacing: 0.011rem;
      color: #2d53f3;
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
