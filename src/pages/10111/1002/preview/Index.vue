<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" v-if="isLoadingFinish">
    <img alt="暂无图片" :src="furnishStyles.kvImg.value.src"  class="kv-img"/>
    <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
      <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
    </div>
    <div class="header-btn-box">
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="toast">
        活动规则
      </div>
      <div class="header-btn" :style="furnishStyles.operateBtn.value"  @click="toast">
        我的奖品
      </div>
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="toast">
        我的订单
      </div>
    </div>
    <div class="time-icon-box">
      <van-count-down :time="activityEndTime - new Date().getTime()" format="DD:HH:mm:ss" class="time-box">
        <template #default="timeData">
          <span class="time-text">{{ timeData.days }}</span><span class="letter-space">天</span>
          <span class="time-text">{{ timeData.hours }}</span><span class="letter-space">时</span>
          <span class="time-text">{{ timeData.minutes }}</span><span class="letter-space">分</span>
          <span class="time-text">{{ timeData.seconds }}</span><span class="letter-space">秒</span>
        </template>
      </van-count-down>
    </div>
    <div class="task-title" :style="[{backgroundImage: `url(${bgImg[consumptionActivityType]})` }]"/>
    <div class="goods-task-list-big-box select-hover">
      <div class="goods-task-list-content">
        <div class="goods-task-list swiper-container" ref="swiperRef">
          <div class="swiper-wrapper">
            <div class="goods-task-list-box swiper-slide" v-for="(item, index) in taskRequestList" :key="index" @dragstart.prevent :style="{'margin-left': taskRequestList.length>1 ? '0.15rem' : '0.4rem'}">
              <div class="goods-task-list-title" > 满<span class="goods-task-list-limit">{{item.rule}}</span>{{ activityTypeUnitText }}即送 </div>
              <div class="goods-task-goods">
                <div class="goods-task-goods-img-box">
                  <img  class="goods-task-goods-img" :src="item.prizeList[0].prizeImg" alt="暂无图片"/>
                </div>
                <div class="goods-task-goods-info">
                  <div class="goods-task-goods-name">{{ item.prizeList[0].prizeName }}</div>
                  <div class="goods-task-goods-remain">奖品剩余：{{ item.prizeList[0].sendTotalCount }}份</div>
                </div>
              </div>
              <div class="task-progress-box">
                <div class="task-progress-num"></div>
              </div>
              <div class="task-progress-text-box">
                <div class="task-progress-text">您已确认收货：0{{['元', '件', '单'][consumptionActivityType]}}</div>
                <div class="task-progress-text1">{{item.rule || 1}}{{ activityTypeUnitText }}</div>
              </div>
              <div class="see-limit" @click="showToast('活动预览，仅供参考');"/>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="prize-list-title" v-if="assignGoodsFlag === 1  || assignGoodsFlag === 2"/>
    <div class="goods-list-box" v-if="assignGoodsFlag === 1  || assignGoodsFlag === 2">
        <div class="content-box" v-if="skuListPreview && skuListPreview.length !==0">
            <div v-for="(item, index) in skuListPreview" :key="item.skuId + index" class="goods-box">
                <img :src="item.skuMainPicture" alt="暂无图片" class="goods-pic"/>
                <div class="goods-info-box">
                    <div class="goods-name">{{ item.skuName }}</div>
                    <div class="goods-info">
                        <div class="goods-price"><span class="price-unit">¥</span>{{ item.jdPrice }}</div>
                        <div class="bug-now-btn" @click="showToast('活动预览，仅供参考');"/>
                    </div>
                </div>
            </div>
            <div class="more-btn-all">
              <div class="more-btn" v-if="skuListPreview.length >= 10" @click="toast">点我加载更多</div>
            </div>
        </div>
    </div>
    <div class="award-list-title"/>
    <div class="winner-null">
      暂无相关获奖信息哦~
    </div>
    <div class="go-to-shop" v-if="!assignGoodsFlag">
      <div class="get-btn"  @click="showToast('活动预览，仅供参考');"/>
    </div>
    <!-- 获奖规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 我的订单弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyOrder @close="showMyPrize = false"></MyOrder>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject, nextTick } from 'vue';
import furnishStyles, { furnish, bgImg } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import RulePopup from '../components/RulePopup.vue';
import { showToast } from 'vant';
import MyPrize from '../components/MyPrize.vue';
import MyOrder from '../components/MyOrder.vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';

Swiper.use([Autoplay]);
interface Prize {
  prizeImg: string;
  prizeName: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
}

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const consumptionActivityType = ref(0); // 活动类型 0:满额有礼 1:满件有礼 2:满次有礼
const activityTypeUnitText = ref('元'); // 活动类型 0:满额有礼 1:满件有礼 2:满次有礼
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const showMyPrize = ref(false);
const taskRequestList = ref([{
  prizeList: [{
    prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/97397/24/41133/46581/66068a3cFe220179c/9578f79242bad354.png',
    prizeName: '清扬 男士去屑洗发水清爽控油型...',
    sendTotalCount: 200,
  }],
  rule: '1',
  sort: 1,
}]);
type Sku = {
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const assignGoodsFlag = ref(1);
const goodsList = ref<Sku[]>([]); // 活动商品
const skuListPreview = ref([
  {
    jdPrice: '3999.00',
    skuId: 1,
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/124652/3/44214/8797/66053856Fadde3f32/d740d9fe53357678.png',
    skuName: '荣耀30 Pro 50倍远摄 麒麟 990 5G 4000万超...',
  },
  {
    jdPrice: '3999.00',
    skuId: 2,
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/124652/3/44214/8797/66053856Fadde3f32/d740d9fe53357678.png',
    skuName: '荣耀30 Pro 50倍远摄 麒麟 990 5G 4000万超...',
  },
  {
    jdPrice: '3999.00',
    skuId: 3,
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/124652/3/44214/8797/66053856Fadde3f32/d740d9fe53357678.png',
    skuName: '荣耀30 Pro 50倍远摄 麒麟 990 5G 4000万超...',
  },
  {
    jdPrice: '3999.00',
    skuId: 4,
    skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/124652/3/44214/8797/66053856Fadde3f32/d740d9fe53357678.png',
    skuName: '荣耀30 Pro 50倍远摄 麒麟 990 5G 4000万超...',
  },
]);

const activityEndTime = ref(new Date().getTime());
const ruleTest = ref('');
// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
const setDataInfo = (data:any) => { // 数据赋值
  const awardConditionsArr: any[] = [];
  if (data.taskRequestList.length !== 0) {
    taskRequestList.value = data.taskRequestList;
    data.taskRequestList.forEach((v:any) => {
      awardConditionsArr.push(v.rule);
    });
  }
  assignGoodsFlag.value = data.assignGoodsFlag;
  goodsList.value = data.skuList;
  skuListPreview.value = data.skuListPreview;
  if (data.orderStartTime.length !== 0) {
    activityEndTime.value = data.orderStartTime[1].$d ? new Date(data.orderStartTime[1].$d).getTime() : new Date(data.orderStartTime[1]).getTime();
  }
  if (data.rules) {
    ruleTest.value = data.rules;
  }
  nextTick(() => {
    const mySwiper = new Swiper('.swiper-container', {
      autoplay: false,
      direction: 'horizontal',
      loop: false,
      slidesPerView: 'auto',
      spaceBetween: -15,
    });
  });
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    if (data.consumptionActivityType || data.consumptionActivityType === 0) {
      consumptionActivityType.value = data.consumptionActivityType;
      activityTypeUnitText.value = ['元', '件', '单'][data.consumptionActivityType];
    }
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    setDataInfo(data);
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
const toast = () => {
  showToast('活动预览，仅供查看');
};
// 截图监听
registerHandler('screen', () => {
  createImg();
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  if (activityData) {
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    if (decoData.consumptionActivityType || decoData.consumptionActivityType === 0) {
      consumptionActivityType.value = decoData.consumptionActivityType;
      activityTypeUnitText.value = ['元', '件', '单'][decoData.consumptionActivityType];
    }
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
img[src=""]
,img:not([src])
{
  opacity:0;
}
.bg {
  position: relative;
  padding-bottom: 2rem;
  min-height: 100vh;
  max-width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  .kv-img {
    position: relative;
    width: 7.5rem;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .header-btn-box {
    position: absolute;
    top: 0.9rem;
    right: 0.2rem;
    .header-btn {
      width: 1.35rem;
      height: 0.5rem;
      margin-bottom: 0.1rem;
      font-size: 0.25rem;
      text-align: center;
      border-radius: 0.22rem;
      border: 0.01rem;
      border-style: solid;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
  .time-icon-box {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    margin-top: 0.14rem;
    width: 6.9rem;
    height: 0.78rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/241357/8/5768/6805/66053853F9bd6fd67/dc560b58ed8b9607.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    align-items: center;
    justify-content: space-between;
    .time-box {
      position: absolute;
      left: 3.6rem;
      font-family: PingFang-SC-Bold;
      font-size: 0.24rem;
      color: #ffffff;
      .time-text {
        color: #ff706f;
      }
      .letter-space {
        margin: 0 0.05rem;
        color: #fdefb5;
      }
    }
  }
  .task-title {
    margin: 0.3rem auto;
    width: 5.8rem;
    height: 0.38rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/109441/9/46373/6533/6614f182F6958b02e/5b1263bc25ccb743.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .prize-list-title {
    margin: 0.3rem 0;
    width: 7.5rem;
    height: 0.58rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/101346/36/53843/9007/6718a51bF327fa077/5d122027dcfb527b.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .award-list-title {
    margin: 0.3rem 0;
    width: 7.5rem;
    height: 0.58rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/87671/24/41164/8140/66053853F0b9d0161/fb16ad00128ec14e.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .winners {
    width: 6.89rem;
    height: 5.74rem;
    background-color: #fef2cd;
    border-radius: 0.1rem;
    margin: 0.49rem auto 0;
    padding-top: 0.3rem;
    .winners-content {
      width: 6.3rem;
      height: 4.8rem;
      border-radius: 0.1rem;
      margin: 0 auto;
      .winner-list {
        width: 100%;
        height: 100%;
        overflow: hidden;
        .winner {
          padding: 0.26rem;
          margin: 0.02rem 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #ffffff;
          border-radius: 0.1rem;
          img {
            width: 0.5rem;
            height: 0.5rem;
            object-fit: cover;
            border-radius: 1.2rem;
            display: inline;
            vertical-align: middle;
            margin-right: 0.2rem;
          }
          span {
            vertical-align: middle;
            font-size: 0.24rem;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .winner-prize-name {
            color: #ff3333;
          }
        }
      }
    }
  }
  .winner-null {
    width: 6.89rem;
    height: 5.74rem;
    background-color: #fef2cd;
    border-radius: 0.1rem;
    margin: 0.49rem auto 0;
    text-align: center;
    font-size: 0.24rem;
    line-height: 5.74rem;
  }
  .goods-task-list-big-box {
    display:flex;
    justify-content: center;
    height:5.5rem;
    width: 7.5rem;
    .goods-task-list-content {
      padding: 0 0.2rem;
      width: 7.5rem;
      height: 5.5rem;
      .goods-task-list {
        width: 100%;
        height: 100%;
        .goods-task-list-box {
          position: relative;
          padding: 1rem 0.65rem 0.47rem;
          height:5.5rem;
          width: 6.34rem;
          transform: scale(0.9);
          background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/167290/5/35734/3310/66053854Fe9855a03/28daaaaa06742360.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          .goods-task-list-title {
            position: absolute;
            transform: translateX(-50%);
            top: 0.19rem;
            left: 50%;
            font-family: SourceHanSansCN-Bold;
            font-size: 0.36rem;
            color: #ffffff;
            .goods-task-list-limit {
              margin: 0 0.05rem;
              color: #fcff00;
            }
          }
          .goods-task-goods {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 2.23rem;
            .goods-task-goods-img-box {
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 0.25rem;
              width: 2rem;
              height: 2rem;
              background-color: #fef2cd;
              border-radius: 0.2rem;
              margin-right: 0.4rem;
              .goods-task-goods-img {
                width: 1.5rem;
                height: 1.5rem;
              }
            }
            .goods-task-goods-info {
              .goods-task-goods-name {
                margin-bottom:0.12rem;
                width: 3rem;
                font-family: SourceHanSansCN-Regular;
                font-size: 0.3rem;
                color: #333333;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp:2;
                -webkit-box-orient: vertical;
              }
              .goods-task-goods-remain {
                font-family: SourceHanSansCN-Regular;
                font-size: 0.24rem;
                line-height: 0.13rem;
                color: #ff0000;
                white-space: nowrap;
              }
            }
          }
          .task-progress-box {
            padding: 0.18rem 0.6rem 0 0.05rem;
            display: flex;
            align-items: center;
            width: 5.36rem;
            height: 0.63rem;
            background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/228393/18/15458/8742/66053854Fa7576a82/f9752c7f30e4d7a1.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            .task-progress-num {
              width: 0%;
              height: 0.26rem;
              background: linear-gradient(
                  -45deg,
                  #FC960E 0, #FC960E 25%, #fed018 25%, #fed018 50%,
                  #FC960E 50%, #FC960E 75%, #fed018 75%, #fed018
              );
              border-radius: 0.201rem;
              background-size: 20px 20px;
            }
          }
          .task-progress-text-box {
            margin-top: 0.05rem;
            width: 5.23rem;
            display: flex;
            justify-content: space-between;
            aligh-items: center;
            .task-progress-text {
              font-family: SourceHanSansCN-Regular;
              font-size: 0.24rem;
              color: #ff7800;
            }
            .task-progress-text1 {
              margin-left: 0.5rem;
            }
          }
          .see-limit {
            margin-top: 0.1rem;
            width: 5.11rem;
            height: 0.9rem;
            background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/240802/20/6644/4676/66053856Fbc71a957/c7f34bc042465ec0.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }
        .swiper-slide-active {
          transform: scale(1);
        }
      }
    }
  }
  .goods-list-box {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.21rem 0.21rem 0.28rem;
    width: 6.89rem;
    min-height:4rem;
    max-height: 9.77rem;
    background-color: #fef2cd;
    border-radius: 0.1rem;
    .content-box {
      display: flex;
      flex-wrap: wrap;
      width: 6.89rem;
      max-height: 9.28rem;
      overflow: auto;
    }
    .more-btn-all{
      width:6.47rem;
      display:flex;
      justify-content:center;
      .more-btn {
        width: 1.8rem;
        height: 0.5rem;
        font-size: 0.2rem;
        color: #fff;
        background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
        background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
        border-radius: 0.25rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .goods-box {
      margin: 0 0.13rem 0.1rem 0;
      padding: 0.16rem 0.25rem 0.2rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      width: 3.15rem;
      height: 4.58rem;
      background-color: #ffffff;
      border-radius: 0.2rem;
      .goods-pic {
        width: 2.24rem;
        height: 2.24rem;
      }
    }
    .goods-info-box {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      .goods-name {
        margin-bottom:0.1rem;
        width: 2.64rem;
        height: 0.53rem;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        line-height: 0.3rem;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp:2;
        -webkit-box-orient: vertical;
      }
      .goods-price {
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        line-height: 0.3rem;
        color: #e2231a;
        .price-unit {
          margin-right: 0.1rem;
        }
      }
      .bug-now-btn {
        margin-top: 0.14rem;
        width: 2.56rem;
        height: 0.61rem;
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/159534/16/44028/2981/66053852Fb0d37c9a/b379560d8fd8cbc4.png ");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
  }
  .go-to-shop {
    position: fixed;
    height: 0.88rem;
    line-height: 1.5rem;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    .get-btn {
      width: 7.5rem;
      height: 0.88rem;
      background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/192572/12/27705/4069/6303376fE584f4c53/c1196beb18066383.png");
      background-size: 100%;
      background-repeat: no-repeat;
      font-family: MiSans-Regular;
      text-align: center;
      font-size: 0.53rem;
      letter-spacing: 0.011rem;
      color: #2d53f3;
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
