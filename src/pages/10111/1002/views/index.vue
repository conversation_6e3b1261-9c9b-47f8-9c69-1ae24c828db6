<template>
  <div class="bg" :style="furnishStyles.actBgColor.value">
    <img alt="暂无图片" :src="furnishStyles.kvImg.value.src" class="kv-img" />
    <div class="shop-name-text" :style="furnishStyles.shopNameColor.value">
      <span v-if="furnish.disableShopName === 1">{{ shopName }}</span>
    </div>
    <div class="header-btn-box">
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="showRule = true">活动规则</div>
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="showMyPrize = true">我的奖品</div>
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="showMyOrder = true">我的订单</div>
    </div>
    <div class="time-icon-box">
      <van-count-down :time="activityInfo.orderEndTime - new Date().getTime()" format="DD:HH:mm:ss" class="time-box">
        <template #default="timeData">
          <span class="time-text">{{ timeData.days }}</span
          ><span class="letter-space">天</span> <span class="time-text">{{ timeData.hours }}</span
          ><span class="letter-space">时</span> <span class="time-text">{{ timeData.minutes }}</span
          ><span class="letter-space">分</span> <span class="time-text">{{ timeData.seconds }}</span
          ><span class="letter-space">秒</span>
        </template>
      </van-count-down>
    </div>
    <div class="task-title" :style="[{ backgroundImage: `url(${bgImg[activityInfo.configType]})` }]" />
    <div class="new-tips-box" v-if="activityInfo.tasks.length > 1">可以左右滑动查看不同阶梯门槛及奖品~</div>
    <div class="goods-task-list-big-box select-hover">
      <div class="goods-task-list-content">
        <div class="goods-task-list swiper-container" ref="swiperRef">
          <div class="swiper-wrapper">
            <div class="goods-task-list-box swiper-slide" v-for="(item, index) in activityInfo.tasks" :key="index" :style="{ 'margin-left': activityInfo.tasks.length > 1 ? '0.15rem' : '0.4rem' }">
              <div class="goods-task-list-title" v-if="item.rule || (activityInfo.configType === 0 && item.rule === 0)">
                满<span class="goods-task-list-limit">{{ item.rule }}</span
                >{{ ['元', '件', '单'][activityInfo.configType] }}即送
              </div>
              <div class="goods-task-list-title" :style="{ width: '4.6rem' }" v-else>消费有礼 完成任务即可领</div>
              <div class="goods-task-goods">
                <div class="goods-task-goods-img-box">
                  <img class="goods-task-goods-img" :src="item.prizeImg" alt="暂无图片" />
                </div>
                <div class="goods-task-goods-info">
                  <div class="goods-task-goods-name">{{ item.prizeName }}</div>
                  <div class="goods-task-goods-remain">奖品剩余：{{ item.residuePrize }}份</div>
                </div>
              </div>
              <div class="task-progress-box">
                <div class="task-progress-num" :style="{ width: `${Number((+activityInfo[activityTotal[activityInfo.configType]] / +item.rule) * 100).toFixed(1)}%` }"></div>
              </div>
              <div class="task-progress-text-box">
                <div class="task-progress-text">您已确认收货：{{ activityInfo.configType === 0 ? Number(activityInfo[activityTotal[activityInfo.configType]]).toFixed(2) : activityInfo[activityTotal[activityInfo.configType]] }}{{ ['元', '件', '单'][activityInfo.configType] }}</div>
                <div class="task-progress-text1">{{ item.rule || 1 }}{{ ['元', '件', '单'][activityInfo.configType] }}</div>
              </div>
              <div class="receive-btn" v-threshold-if :style="{ backgroundImage: `url(${item.isCan || +item.residuePrize ? btnBg[item.isCan] : btnBg[2]})` }" v-threshold-click="() => clickGetPrizeFn(item)" />
              <div class="receive-btn" v-threshold-else :style="{ backgroundImage: `url(//img10.360buyimg.com/imgzone/jfs/t1/210529/30/30134/17380/64080220F6efb69c4/b26abf4cc1d06a41.png)` }" v-threshold-click="() => clickGetPrizeFn(item)" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="prize-list-title" v-if="skuList.length !== 0" />
    <div class="goods-list-box" v-if="skuList.length !== 0">
      <div class="goods-list-title"></div>
      <div class="content-box" v-if="skuList.length !== 0">
        <div v-for="(item, index) in skuList" :key="item.skuId + index" class="goods-box">
          <img :src="item.skuMainPicture" alt="暂无图片" class="goods-pic" />
          <div class="goods-info-box">
            <div class="goods-name">{{ item.skuName }}</div>
            <div class="goods-info">
              <div class="goods-price"><span class="price-unit">¥</span>{{ item.jdPrice }}</div>
              <div class="bug-now-btn" @click="gotoSkuPage(item.skuId)" />
            </div>
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="skuList.length && skuList.length !== total" @click="loadMore">点我加载更多</div>
          <div class="more-btn" @click="handleShowAll">查看全部</div>
        </div>
      </div>
    </div>
    <div class="award-list-title" />
    <div class="winners select-hover">
      <div class="winners-content">
        <div class="winner-list swiper-container1" ref="swiperRef">
          <div class="swiper-wrapper" v-if="activityInfo.activity10111SuccessResponse.length !== 0">
            <div class="winner swiper-slide" v-for="(item, index) in activityInfo.activity10111SuccessResponse" :key="index">
              <div>
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" />
                <span>{{ item.nickName.substr(0, 1) + '****' + item.nickName.substr(-1) }}</span>
              </div>
              <span class="winner-prize-name">{{ item.prizeName }}</span>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
    <div class="go-to-shop" v-if="skuList.length === 0">
      <div class="get-btn" @click="gotoShopPage(baseInfo.shopId)" />
    </div>
    <!-- 获奖规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="activityRule" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
    </VanPopup>
    <!-- 我的订单弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyOrder" position="bottom">
      <MyOrder v-if="showMyOrder" @close="showMyOrder = false"></MyOrder>
    </VanPopup>
    <!-- 领取奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showReceivePrize">
      <ReceivePrize :isDelay="activityInfo.isDelay" :delayDays="activityInfo.delayDays" :shopId="baseInfo.shopId" :receivePrizeInfo="receivePrizeInfo" :currentPrizeId="currentPrizeId" :currentReceivePrizeInfo="currentReceivePrizeInfo" @close="showReceivePrize = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></ReceivePrize>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress v-if="showSaveAddress" :activityPrizeId="currentPrizeId" :addressId="addressId" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="copyCardPopup" :closeOnClickOverlay="false">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
    </VanPopup>
    <!-- 领取京元宝权益 -->
    <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom" :closeOnClickOverlay="false">
      <SavePhone v-if="savePhonePopup" :currentPrizeId="currentPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, inject, reactive, nextTick } from 'vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import furnishStyles, { furnish, bgImg } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import RulePopup from '../components/RulePopup.vue';
import _ from 'lodash';
import MyOrder from '../components/MyOrder.vue';
import MyPrize from '../components/MyPrize.vue';
import ReceivePrize from '../components/receivePrize.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import SavePhone from '../components/SavePhone.vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';

Swiper.use([Autoplay]);

export interface CardType {
  cardDesc: string;
  cardNumber: string;
  cardPassword: string;
  id: number;
  prizeName: string;
  showImg: string;
}
let vm;
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const activityInfo = ref({
  activity10111SuccessResponse: [
    {
      drawTime: '',
      nickName: '',
      prizeName: '',
    },
  ],
  orderEndTime: '',
  configType: 0,
  orderSkuisExposure: 0,
  tasks: [],
  totalAmount: '',
  totalOrder: '',
  totalSku: '',
  isDelay: false,
}); // 活动相关数据
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const cursorNum = ref(0);

const shopName = ref(baseInfo.shopName);
const receivePrizeInfo = ref({
  taskId: '',
  isCan: '',
  prizeImg: '',
  prizeName: '',
  residuePrize: '',
  prizeType: '',
  rule: '',
  sort: 0,
});
const showRule = ref(false);
const showMyPrize = ref(false);
const showMyOrder = ref(false);
const showReceivePrize = ref(false);
const showSaveAddress = ref(false);
const activityRule = ref('');
const activityTotal = ['totalAmount', 'totalSku', 'totalOrder'];
const currentReceivePrizeInfo = ref();
const btnBg = ['//img10.360buyimg.com/imgzone/jfs/t1/240802/20/6644/4676/66053856Fbc71a957/c7f34bc042465ec0.png', '//img10.360buyimg.com/imgzone/jfs/t1/221465/27/20660/3871/6311a58dE6005779c/deb8fb3b695d76c3.png', '//img10.360buyimg.com/imgzone/jfs/t1/114536/39/29768/3766/6311a58dE47ada9f3/f9df3917305ca197.png'];
const currentPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (v: any) => {
  addressId.value = v.addressId;
  currentPrizeId.value = v.activityPrizeId;
  showReceivePrize.value = false;
  showSaveAddress.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (v: any) => {
  currentPrizeId.value = v.userPrizeId;
  planDesc.value = v.planDesc || '';
  showReceivePrize.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};
// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showReceivePrize.value = false;
  copyCardPopup.value = true;
};
// 获取活动信息
const getActivityInfo = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10111/activity');
    closeToast();
    activityInfo.value = data;
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: false,
        direction: 'horizontal',
        loop: false,
        slidesPerView: 'auto',
        spaceBetween: -15,
      });
      const autoplay = {
        delay: 1000,
        stopOnLastSlide: false,
        disableOnInteraction: false,
      };
      const mySwiper1 = new Swiper('.swiper-container1', {
        autoplay: data.activity10111SuccessResponse.length > 4 ? autoplay : false,
        direction: 'vertical',
        loop: data.activity10111SuccessResponse.length > 4,
        slidesPerView: 4,
        loopedSlides: 8,
        spaceBetween: 10,
      });
    });
  } catch (error) {
    console.error(error);
    closeToast();
  }
};

// 获取指定商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/10111/getExposureSkuPage', {
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    closeToast();
  } catch (error) {
    closeToast();
  }
};

const loadMore = async () => {
  pageNum.value += 1;
  await getSkuList();
};

const handleShowAll = () => {
  window.location.href = `${process.env.VUE_APP_PATH_PREFIX_NO_CDN}10111/exposure/?shopId=${baseInfo.shopId}&activityId=${pathParams.activityId}`;
};
// 获取活动规则
const getActivityRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    activityRule.value = data;
  } catch (error) {
    console.error(error);
  }
};
// 领取奖品
const clickGetPrize = async (item) => {
  console.log(item);
  receivePrizeInfo.value = item;
  if (+receivePrizeInfo.value.isCan !== 1 && +receivePrizeInfo.value.residuePrize) {
    // 可以兑换
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const { code, data } = await httpRequest.post('/10111/sendPrize', { taskId: receivePrizeInfo.value.taskId });
      if (code === 200) {
        showReceivePrize.value = true;
        currentReceivePrizeInfo.value = data;
        console.log(data, '兑换结果');
      }
      closeToast();
      await getActivityInfo();
    } catch (error) {
      showToast(error.message);
      console.error(error);
    }
  }
};
const clickGetPrizeFn = _.debounce(clickGetPrize, 500, {
  leading: true, // 延长开始后调用
  trailing: false, // 延长结束前调用
});

// 移除组件时，取消防抖
onUnmounted(() => {
  clickGetPrizeFn.cancel();
});

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getActivityInfo();
    await getActivityRule();
    await getSkuList();
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.bg {
  position: relative;
  padding-bottom: 2rem;
  min-height: 100vh;
  max-width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  .kv-img {
    position: relative;
    width: 7.5rem;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .header-btn-box {
    position: absolute;
    top: 0.9rem;
    right: 0.2rem;
    .header-btn {
      width: 1.35rem;
      height: 0.5rem;
      margin-bottom: 0.1rem;
      font-size: 0.25rem;
      text-align: center;
      border-radius: 0.22rem;
      border: 0.01rem;
      border-style: solid;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
  .time-icon-box {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    margin-top: 0.14rem;
    width: 6.9rem;
    height: 0.78rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/241357/8/5768/6805/66053853F9bd6fd67/dc560b58ed8b9607.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    align-items: center;
    justify-content: space-between;
    .time-box {
      position: absolute;
      left: 3.6rem;
      font-family: PingFang-SC-Bold;
      font-size: 0.24rem;
      color: #ffffff;
      .time-text {
        color: #ff706f;
      }
      .letter-space {
        margin: 0 0.05rem;
        color: #fdefb5;
      }
    }
  }
  .task-title {
    margin: 0.3rem auto;
    width: 5.8rem;
    height: 0.38rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/109441/9/46373/6533/6614f182F6958b02e/5b1263bc25ccb743.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .new-tips-box {
    margin: -8px 0px 8px;
    text-align: center;
    color: gainsboro;
  }
  .prize-list-title {
    margin: 0.3rem 0;
    width: 7.5rem;
    height: 0.58rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/101346/36/53843/9007/6718a51bF327fa077/5d122027dcfb527b.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .award-list-title {
    margin: 0.3rem 0;
    width: 7.5rem;
    height: 0.58rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/87671/24/41164/8140/66053853F0b9d0161/fb16ad00128ec14e.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .task-box {
    overflow: hidden;
    height: 7.23rem;
    width: 7.13rem;
  }
  .goods-task-list-big-box {
    display: flex;
    justify-content: center;
    height: 5.5rem;
    width: 7.5rem;
    .goods-task-list-content {
      padding: 0 0.2rem;
      width: 7.5rem;
      height: 5.5rem;
      .goods-task-list {
        width: 100%;
        height: 100%;
        overflow: hidden;
        .goods-task-list-box {
          position: relative;
          margin-left: 0.15rem;
          padding: 1rem 0.65rem 0.47rem;
          height: 5.5rem;
          width: 6.34rem;
          transform: scale(0.9);
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/167290/5/35734/3310/66053854Fe9855a03/28daaaaa06742360.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          .goods-task-list-title {
            position: absolute;
            transform: translateX(-50%);
            top: 0.19rem;
            left: 50%;
            font-family: SourceHanSansCN-Bold;
            font-size: 0.36rem;
            color: #ffffff;
            .goods-task-list-limit {
              margin: 0 0.05rem;
              color: #fcff00;
            }
          }
          .goods-task-goods {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 2.23rem;
            .goods-task-goods-img-box {
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 0.25rem;
              width: 2rem;
              height: 2rem;
              background-color: #fef2cd;
              border-radius: 0.2rem;
              margin-right: 0.4rem;
              .goods-task-goods-img {
                width: 1.5rem;
                height: 1.5rem;
              }
            }
            .goods-task-goods-info {
              .goods-task-goods-name {
                margin-bottom: 0.12rem;
                width: 3rem;
                font-family: SourceHanSansCN-Regular;
                font-size: 0.3rem;
                color: #333333;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
              .goods-task-goods-remain {
                font-family: SourceHanSansCN-Regular;
                font-size: 0.24rem;
                line-height: 0.13rem;
                color: #ff0000;
                white-space: nowrap;
              }
            }
          }
          .task-progress-box {
            padding: 0.18rem 0.6rem 0 0.05rem;
            display: flex;
            align-items: center;
            width: 5.36rem;
            height: 0.63rem;
            background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/228393/18/15458/8742/66053854Fa7576a82/f9752c7f30e4d7a1.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            .task-progress-num {
              width: 0;
              height: 0.27rem;
              background: linear-gradient(-45deg, #fc960e 0, #fc960e 25%, #fed018 25%, #fed018 50%, #fc960e 50%, #fc960e 75%, #fed018 75%, #fed018);
              border-radius: 0.201rem;
              background-size: 20px 20px;
            }
          }
          .task-progress-text-box {
            margin-top: 0.05rem;
            width: 5.23rem;
            display: flex;
            justify-content: space-between;
            aligh-items: center;
            .task-progress-text {
              font-family: SourceHanSansCN-Regular;
              font-size: 0.24rem;
              color: #ff7800;
            }
            .task-progress-text1 {
              margin-left: 0.5rem;
            }
          }
          .receive-btn {
            margin-top: 0.1rem;
            width: 5.11rem;
            height: 0.9rem;
            background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/240802/20/6644/4676/66053856Fbc71a957/c7f34bc042465ec0.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }
        .swiper-slide-active {
          transform: scale(1);
        }
      }
    }
  }
  .goods-list-box {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.21rem 0.21rem 0.28rem;
    width: 6.89rem;
    min-height: 4rem;
    max-height: 9.77rem;
    background-color: #fef2cd;
    border-radius: 0.1rem;
    .content-box {
      display: flex;
      flex-wrap: wrap;
      width: 6.89rem;
      max-height: 9.28rem;
      overflow: auto;
    }
    .goods-box {
      margin: 0 0.13rem 0.1rem 0;
      padding: 0.16rem 0.25rem 0.2rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      width: 3.15rem;
      height: 4.58rem;
      background-color: #ffffff;
      border-radius: 0.2rem;
      .goods-pic {
        width: 2.24rem;
        height: 2.24rem;
      }
    }
    .goods-info-box {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      .goods-name {
        margin-bottom: 0.1rem;
        width: 2.64rem;
        height: 0.53rem;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        line-height: 0.3rem;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .goods-price {
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        line-height: 0.3rem;
        color: #e2231a;
        .price-unit {
          margin-right: 0.1rem;
        }
      }
      .bug-now-btn {
        margin-top: 0.14rem;
        width: 2.56rem;
        height: 0.61rem;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/159534/16/44028/2981/66053852Fb0d37c9a/b379560d8fd8cbc4.png ');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
  }
  .more-btn-all {
    width: 6.47rem;
    display: flex;
    justify-content: center;
    gap: 0.2rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .winners {
    width: 6.89rem;
    height: 5.74rem;
    background-color: #fef2cd;
    border-radius: 0.1rem;
    margin: 0.49rem auto 0;
    padding-top: 0.3rem;
    .winners-content {
      width: 6.3rem;
      height: 4.8rem;
      border-radius: 0.1rem;
      margin: 0 auto;
      .winner-list {
        width: 100%;
        height: 100%;
        overflow: hidden;
        .winner {
          padding: 0.26rem;
          margin: 0.02rem 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #ffffff;
          border-radius: 0.1rem;
          img {
            width: 0.5rem;
            height: 0.5rem;
            object-fit: cover;
            border-radius: 1.2rem;
            display: inline;
            vertical-align: middle;
            margin-right: 0.2rem;
          }
          span {
            vertical-align: middle;
            font-size: 0.24rem;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .winner-prize-name {
            color: #ff3333;
          }
        }
      }
    }
  }
  .winner-null {
    border-radius: 0.1rem;
    text-align: center;
    font-size: 0.24rem;
    line-height: 4.74rem;
  }
  .go-to-shop {
    position: fixed;
    height: 0.88rem;
    line-height: 1.5rem;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    .get-btn {
      width: 7.5rem;
      height: 0.88rem;
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/192572/12/27705/4069/6303376fE584f4c53/c1196beb18066383.png');
      background-size: 100%;
      background-repeat: no-repeat;
      font-family: MiSans-Regular;
      text-align: center;
      font-size: 0.53rem;
      letter-spacing: 0.011rem;
      color: #2d53f3;
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
