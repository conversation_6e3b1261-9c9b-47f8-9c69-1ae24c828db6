<template>
  <div class="prize-bk">
    <div class="close" @click="close" />
    <div class="title">
      <div class="leftLineDiv" />
      <div>我的奖品</div>
      <div class="rightLineDiv" />
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="type">
          <span>{{ prizeType[+item.prizeType] }}</span>
          <span>{{ item.userPrizeId ? item.userPrizeId : '' }}</span>
        </div>
        <div class="info">
          <img :src="item.prizeImg" alt="" class="show-img" />
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
            <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="status" v-if="+item.prizeType === 3">
            <template v-if="item.deliveryStatus == 1 && item.status == 1">
              <div class="green" style="width: 1.8rem">已发货</div>
            </template>
            <template v-else-if="item.status == 3 || item.status == 1">
              <div class="orange">{{ item.status === 3 ? '待发放' : '待发货' }}</div>
              <div v-if="item.realName" class="right1btn" @click="changAddress(item)">查看地址</div>
              <div v-if="!item.realName" class="right1btn" @click="changAddress(item)">填写地址</div>
            </template>
            <template v-else>
              <div class="orange">已取消</div>
            </template>
          </div>
          <div class="status" v-else-if="item.prizeType === 7">
            <div class="orange" v-if="item.status === 3">待发放</div>
            <div class="blue" v-else-if="item.status === 1" @click="showCardNum(item)">如何兑换</div>
            <div class="orange" v-else>已取消</div>
          </div>
          <div class="status" v-else-if="+item.prizeType === 9 || +item.prizeType === 10">
            <div class="orange" v-if="item.status === 3">待发放</div>
            <div class="blue" v-else-if="item.status === 1" @click="exchangePlusOrAiqiyi">立即兑换</div>
            <div class="orange" v-else>已取消</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 12">
            <div class="orange" v-if="item.status === 3">待发放</div>
            <template v-else-if="item.status === 1">
              <div class="orange" v-if="item.isFuLuWaitingReceive">待领取</div>
              <div class="orange" v-else>已发放</div>
              <div class="blue" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive">点击领取</div></template
            >
            <div class="orange" v-else>已取消</div>
          </div>
          <div class="status" v-else>
            <div class="orange" v-if="item.status === 3">待发放</div>
            <div class="green" v-else-if="item.status === 1">已发放</div>
            <div class="orange" v-else>已取消</div>
          </div>
        </div>
        <div class="deliver" v-if="item.prizeType === 3 && item.deliveryStatus">
          <div>
            <div>快递公司：{{ item.deliverName }}</div>
            <div>快递单号：{{ item.deliverNo }}</div>
          </div>
          <div class="copy-btn" :copy-text="item.deliverNo">复制单号</div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :activityPrizeId="currentPrizeId" :recordId="recordId" :addressId="addressId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import Clipboard from 'clipboard';

import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;
const emits = defineEmits(['close', 'showCardNum', 'savePhone']);
const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  status: number;
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  dealStatus: number;
  realName: string;
  deliverNo: string;
  deliverName: string;
  activityPrizeId: string;
}

const prizes = reactive([] as Prize[]);
const currentPrizeId = ref('');
const addressId = ref('');

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};
// 领取京元宝
const savePhone = (item: Prize) => {
  if (item.prizeContent) {
    const prizeContent = JSON.parse(item.prizeContent);
    emits('savePhone', { userPrizeId: item.userPrizeId, planDesc: prizeContent.result.planDesc });
  } else {
    emits('savePhone', { userPrizeId: item.userPrizeId, planDesc: '' });
  }
};
const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10111/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const showSaveAddress = ref(false);
const recordId = ref('');
const echoData = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  recordId.value = item.userPrizeId;
  addressId.value = item.addressId;
  currentPrizeId.value = item.activityPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};
</script>

<style scoped lang="scss">
.prize-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;
  height: 9.48rem;
  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/58825/13/20897/7405/63084eadE7f6b9252/0db6644ea77ae3b0.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    border-radius: 0.2rem 0.2rem 0 0;
    color: #fff;
    .leftLineDiv {
      margin-right: 0.1rem;
      width: 34px;
      height: 4px;
      line-height: 0.08rem;
      background: linear-gradient(to left, rgb(243, 167, 50), rgb(255, 255, 255));
      border-radius: 4px;
    }
    .rightLineDiv {
      margin-left: 0.1rem;
      width: 34px;
      height: 4px;
      line-height: 0.08rem;
      background: linear-gradient(to right, rgb(243, 167, 50), rgb(255, 255, 255));
      border-radius: 4px;
    }
  }
  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }
  .content {
    position: absolute;
    top: 1.3rem;
    left: 0.5rem;
    height: 8rem;
    width: 6.5rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .prize {
      background: #ffffff;
      margin-bottom: 0.1rem;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.16rem;
        padding-bottom: 0.16rem;
        border-bottom: 0.02rem dashed #eee;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.24rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;

        .show-img {
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #ff3333;
          }

          .time {
            color: #999999;
            font-size: 0.2rem;
            margin-top: 0.2rem;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;

          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #0083ff;
          }
        }
      }

      .deliver {
        margin-left: 1.3rem;
        margin-right: 0.22rem;
        color: #999999;
        font-size: 0.2rem;
        display: flex;
        justify-content: space-between;
        .copy-btn {
          color: #0083ff;
          margin-top: 0.24rem;
          font-size: 0.24rem;
        }
      }
    }

    .no-data {
      text-align: center;
      line-height: 35vh;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}
.deliver-dialog {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0.2rem 0.8rem;
  width: 5.88rem;
  height: 2.76rem;
  background-color: #fff;
  border-radius: 0.2rem;
  align-items: center;
  .deliver-btn {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/249881/20/551/18047/65894308F93be030b/8a3bbbb31e4294cc.png') no-repeat;
    background-size: 100% 100%;
    width: 4.5rem;
    height: 0.76rem;
  }
}
.right1btn {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/236543/30/8550/5618/6585328aF1f8cc84b/dd057198e10bd3ef.png) no-repeat;
  background-size: 100% 100%;
  width: 1.2rem;
  height: 0.44rem;
  margin-left: 0.3rem;
  margin-top: 0.1rem;
  text-align: center;
  line-height: 0.44rem;
  color: #ffffff;
  font-size: 0.24rem;
}
.right2 {
  width: 1.6rem;
  text-align: center;
  font-size: 0.24rem;
  color: #ffa161;
}
</style>
