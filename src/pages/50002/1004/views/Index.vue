<template>
  <div class="bg">
    <div class="kvBgStyle">
      <img @click="getPrize()"  v-if="imageUrl" class="giftImageUrlDiv" :class="['animate__animated', effectNames]" :src="imageUrl" alt="">
      <div v-if="isShowClose" class="closeDiv" @click="toast"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import 'animate.css';
import { ref, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { jumpGetCoupon, closeGiftPop } from '@/utils/shopGiftBag.js';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;
console.log(pathParams, 'pathParams');
const isShowClose = ref(pathParams.isGift);
const effectNames = ref('');
const imageUrl = ref('');
const link = ref('');
const userReceiveRecordId = ref(0);
// 获取数据
const getAllData = async () => {
  try {
    const { data } = await httpRequest.post('/50002/query');
    imageUrl.value = data.imgUrl;
    link.value = data.link;
    userReceiveRecordId.value = data.userReceiveRecordId;
  } catch (errMsg) {
    showToast(errMsg.message);
  }
};
// 领取奖品
const getPrize = async () => {
  if (pathParams.isGift) {
    showToast('仅供预览');
    return;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/50002/receive', {
      userReceiveRecordId: userReceiveRecordId.value,
    });
    effectNames.value = furnishStyles.outEffectName.value.name;
    closeToast();
    closeGiftPop(this, () => {
      console.log('关闭弹窗');
    });
    jumpGetCoupon(link.value, () => {
      console.log('跳转链接');
    });
  } catch (errMsg) {
    showToast(errMsg.message);
  }
};
const toast = () => {
  if (pathParams.isGift) {
    showToast('仅供预览');
  }
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    effectNames.value = furnishStyles.enterEffectName.value.name;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getAllData()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();

</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  min-height: 100vh;
  position: relative;
  background: rgba(0,0,0,0.7);
  .kvBgStyle{
    background-size: 100%;
    background-repeat: no-repeat;
    position: absolute;
    width: 6.17rem;
    height: 7.85rem;
    left: calc(50% - 6.17rem / 2);
    top: calc(50% - 7.85rem / 2);
    .giftImageUrlDiv{
      width: 6.17rem;
      height: 7.85rem;
    }
  }
  .closeDiv{
    position: absolute;
    width: 0.5rem;
    height: 0.5rem;
    top: -0.8rem;
    right: 0;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/240116/39/1181/7700/65a6402cF2d4309d7/f9f24e9fcb55e30c.png");
  }
}
</style>
