// 顶部样式
.common-message-header-30003 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png") no-repeat;
  background-size: contain;
  padding-top: 1.5rem;
}
// 利用footer来做关闭按钮
.common-message-footer-30003 {
  position: absolute;
  width: .25rem;
  height: .25rem;
  right: 0.28rem;
  top: 0.65rem;
  background: transparent;
  color: transparent;
  border: none transparent;
}
// 内容
.common-message-content-30003 {
  font-size: .23rem;
  line-height: 0.3rem;
}
// 通用主题
.common-message-30003 {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/223902/11/37746/36468/656ecff0F589c4749/40bda67b278cab24.png") !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100% !important;
  width: 5.96rem !important;
  height: 7.33rem;
  padding-top: 3.51rem;
  .van-dialog__footer {
    //display: none;
    @extend .common-message-footer-30003;
    .van-dialog__confirm {
      color: transparent;
      background: transparent;
      border: none;
    }
    .van-button__content{
      background: transparent;
    }
  }
  .van-hairline--top:after{
    border-top-width:0;
  }
  .van-dialog__message {
    @extend .common-message-content-30003;
  }
}

