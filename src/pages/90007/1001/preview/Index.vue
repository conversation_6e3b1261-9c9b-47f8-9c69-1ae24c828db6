<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">{{ shopName }}</div>
        <div>
          <div class="header-btn header-btn1" :style="furnishStyles.headerBtn.value" @click="rulePopup = true"><div>活动规则</div></div>
          <div class="header-btn header-btn2" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true"><div>我的奖品</div></div>
          <div class="header-btn header-btn3" :style="furnishStyles.headerBtn.value" @click="showOrderRecord = true"><div>我的订单</div></div>
        </div>
      </div>
    </div>
    <div class="ac-Introduction" :style="furnishStyles.acIntroductionBg.value">
      <!-- <p>
        活动时间：2024年2月26日一2024年4月30日（仅识别入会后的订单） 奖品领取时间：2024年2月26日-2024年5月10日 活动对象：伊利母婴京东自营旗舰店会员
        参与活动商品：QQ星榛高铂金装正装奶粉；塞纳牧2、3、4段正装奶粉； 睿护2、3、4段正装奶粉；其它系列2、3、4段及QQ星系列其他正装奶粉
        *购买指定产品并达到指定罐数赠送指定实物奖品，兑换奖品后消耗对应的罐数， 需要重新累计获取下一级奖品，奖品兑换后不可更换
        活动规则：活动时间内，会员购买指定产品，累计罐数达标后，可申请兑换相应 赠品；兑换赠品后，需扣除相应罐数；活动结束后，罐数清零 参与方式:
        会员在活动期间报名参加活动后产生购买行为，且购买罐数累计满足以下条件 （储值卡也计入金额） 购买金领冠指定系列奶粉指定商品且确认收货后可申请领取
      </p>
      <div class="ac-Introduction-footer">*集罐有礼活动截止日期: 2024年4月30日</div> -->
    </div>
    <div class="series-box" :style="{ backgroundImage: seriesBoxBk }" v-for="(item, index) in seriesList" :key="index">
      <div class="series-name">{{ item.seriesName }}</div>
      <div class="series-goods" :style="{ backgroundImage: `url(${item.seriesPic})` }">
        <div class="had-buy">您已经购买XX罐</div>
        <!-- 继续集罐按钮 -->
        <div class="buy-btn" :style="furnishStyles.buyBtnBg.value" @click="toSign"></div>
      </div>
      <div class="prize-list">
        <div class="prize-item" v-for="(child, childIndex) in item.seriesPrizeList" :key="childIndex">
          <div class="prize-threshold">满{{ child.potNum }}罐</div>
          <img :src="child.prizeImg" alt="" class="prize-img" />
          <div class="inventory">剩余:{{ child.sendTotalCount }}</div>
          <img :src="furnish.exchangeBtn ?? '//img10.360buyimg.com/imgzone/jfs/t1/191145/33/40745/4516/6615114aFe6ad325a/72e3159873772eef.png'" alt="" class="exchange-btn" @click="toSign" />
          <div class="prize-desc">{{ child.prizeName }}</div>
        </div>
      </div>
    </div>
    <!-- 曝光商品 -->
    <div class="sku">
      <img class="title-img" v-if="showSelect || isExposure === 1" :src="furnish.winnersBg" alt="" />
      <div class="sku-list" v-if="exposureSkuList.length">
        <div class="sku-item" v-for="(item, index) in exposureSkuList" :key="index">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns" @click="toSign">
            <div class="price">￥{{ item.jdPrice }}</div>
            <div class="to-bug">抢购</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="prizeTipPopup" position="bottom" :closeOnClickOverlay="false"><PrizeTip @close="prizeTipPopup = false"></PrizeTip></VanPopup>
  <VanPopup teleport="body" v-model:show="rulePopup" position="bottom" :closeOnClickOverlay="false">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup" position="bottom" :closeOnClickOverlay="false">
    <MyPrize @close="myPrizePopup = false"></MyPrize>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
    <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import { showToast } from 'vant';
import PrizeTip from '../components/PrizeTip.vue';
import MyPrize from '../components/MyPrize.vue';
import Rule from '../components/Rule.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import { deepCopy } from '@/utils/platforms/client';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const seriesBoxBk = computed(
  () => `url('${furnish?.seriesBoxBkHead || '//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png'}'),
    url('${furnish?.seriesBoxBkFooter || '//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png'}'),
    url('${furnish?.seriesBoxBkBody || '//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png'}')`,
);

const shopName = ref('xxx自营旗舰店');
// 订单弹窗是否显示
const showOrderRecord = ref(false);
const isExposure = ref(1);
const isLoadingFinish = ref(false);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const exposureSkuList = ref<Sku[]>([]);

const defaultSeriesList = [
  {
    seriesName: '系列名',
    seriesPic: '',
    seriesPrizeList: [],
    seriesSkuList: [
      {
        potNum: 6,
        skuId: 12345678,
      },
    ],
    seriesUrl: '',
  },
];
// 系列列表
const seriesList = ref<any[]>(deepCopy(defaultSeriesList));

const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const prizeTipPopup = ref(false);

const toSign = () => {
  showToast('活动预览，仅供查看');
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  isExposure.value = data.isExposure;
  seriesList.value = data.seriesList.length ? data.seriesList : deepCopy(defaultSeriesList);
  exposureSkuList.value = data.exposureSkuList;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    isExposure.value = activityData.isExposure;
    seriesList.value = activityData.seriesList.length ? activityData.seriesList : deepCopy(defaultSeriesList);
    exposureSkuList.value = activityData.exposureSkuList;
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem 0 0 0.22rem;
    border: 0.01rem;
    border-right: 0;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    right: 0;
    font-weight: 700;
  }
  .header-btn1 {
    top: 0.2rem;
  }
  .header-btn2 {
    top: 0.7rem;
  }
  .header-btn3 {
    top: 1.2rem;
  }
}
.ac-Introduction {
  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/209819/10/40835/79774/66151148Fa00bf5a1/e9158b30a8fffdb6.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.9rem;
  height: 6.72rem;
  margin: 0 auto;
  font-size: 0.2rem;
  line-height: 0.4rem;
  color: #7c5400;
  padding: 0 0.5rem;
  padding-top: 1.2rem;
  position: relative;
  margin-bottom: 0.3rem;
  .ac-Introduction-footer {
    width: 100%;
    position: absolute;
    text-align: center;
    color: #dc2c1c;
    font-weight: 600;
    bottom: 0.2rem;
    left: 50%;
    transform: translate(-50%);
  }
}
.series-box {
  width: 6.91rem;
  margin: 0 auto;
  // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png'), url('//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png');
  background-repeat: no-repeat, no-repeat, no-repeat;
  background-size: 100%, 100%, 100% calc(100% - 2.53rem);
  background-position-y: top, bottom, 2rem;
  margin-bottom: 0.3rem;
  .series-name {
    font-size: 0.4rem;
    background: linear-gradient(180deg, #8b4406, #c47a2a);
    -webkit-background-clip: text;
    color: transparent;
    padding: 0.3rem 0;
    text-align: center;
    font-weight: 600;
  }
  .series-goods {
    width: 6.65rem;
    height: 4.97rem;
    margin: 0 auto;
    // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/182387/30/43641/66908/66151149F053b1850/119e49d97df5e086.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    .had-buy {
      position: absolute;
      top: 3.4rem;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0.3rem;
      font-weight: 700;
      color: #7c5400;
      text-align: center;
    }
    .buy-btn {
      position: absolute;
      bottom: 0.3rem;
      left: 50%;
      transform: translateX(-50%);
      width: 2.95rem;
      height: 0.67rem;
      line-height: 0.67rem;
      text-align: center;
      cursor: pointer;
      // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/183774/23/42582/7645/6615114aF3215b691/ead252530ccc7427.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .prize-list {
    display: flex;
    justify-content: space-between;
    padding: 0.1rem;
    flex-wrap: wrap;
    .prize-item {
      width: 3.23rem;
      height: 3.55rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/242108/26/3502/9353/6615114aF5c1bd6fd/b318d30364c2a5f8.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: relative;
      margin-bottom: 0.1rem;
      .prize-threshold {
        color: #72421f;
        size: 0.22rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
      }
      .prize-img {
        width: 2rem;
        margin: 0 auto;
      }
      .inventory {
        min-width: 1.05rem;
        height: 0.3rem;
        background-color: #6e6c65;
        opacity: 0.9;
        position: absolute;
        font-size: 0.24rem;
        color: white;
        left: 0;
        top: 1rem;
        border-radius: 0 1rem 1rem 0;
      }
      .exchange-btn {
        width: 2.05rem;
        // height: 0.47rem;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: 2.5rem;
      }
      .prize-desc {
        font-size: 0.25rem;
        color: #734320;
        font-weight: 500;
        text-align: center;
        position: absolute;
        bottom: 0.1rem;
        width: 100%;
      }
    }
  }
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background-color: #f6ebd0;
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        font-size: 0.3rem;
        color: #262626;
        height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.43rem;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/244615/4/7225/1431/661644d6F575dfb61/6ee1cc99f718c0ca.png');
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug {
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}
</style>
