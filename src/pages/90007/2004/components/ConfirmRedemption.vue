<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false">
    <div class="dialog">
      <icon class="close_icon" name="close" color="#d6b377" size="25" @click="closeDialog" />
      <div class="btn-confirm" @click="confirm"></div>
      <div class="btn-cancel" @click="closeDialog"></div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps } from 'vue';

const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog', 'confirmFun']);
const closeDialog = () => {
  emits('closeDialog');
};
const confirm = () => {
  emits('confirmFun');
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.5rem;
  height: 8rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/246724/31/8381/24937/66383871F34ff54c4/b4c6e0a01c1e1862.jpg') no-repeat;
  background-size: contain;
  padding: 0.28rem;
  box-sizing: border-box;
  text-align: center;
  .logo {
    width: 1.1rem;
  }

  .title {
    font-size: 0.3rem;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;

    &:after {
      content: '';
      width: 0.5rem;
      height: 0.02rem;
      margin-left: 0.2rem;
      background: #333;
    }

    &:before {
      content: '';
      width: 0.5rem;
      height: 0.02rem;
      margin-right: 0.2rem;
      background: #333;
    }
  }

  .btn-confirm {
    position: absolute;
    bottom: 2.35rem;
    left: 1.1rem;
    width: 1.2rem;
    height: 0.6rem;
  }
  .btn-cancel {
    position: absolute;
    bottom: 2.35rem;
    right: 1.1rem;
    width: 1.2rem;
    height: 0.6rem;
  }
  .tips {
    margin: 1.5rem 0;
    font-size: 0.32rem;
    text-align: center;
  }
}
.close_icon {
  position: absolute;
  right: 0.3rem;
}
</style>
