<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @open="openDialog">
    <div class="dialog">
      <icon class="close_icon" name="close" color="#d6b377" size="25" @click="closeDialog" />
      <div class="list">
        <div>日期</div>
        <div>奖品</div>
        <div>备注</div>
      </div>
      <div class="item-box" v-if="prizes.length > 0">
        <div class="list item" v-for="item in prizes" :key="item.recordId">
          <div>{{ dayjs(item.drawTime).format('YYYY.MM.DD') }}</div>
          <div>{{ item.prizeName }}</div>
          <div class="btn" @click="changeAddress(item)" v-if="item.prizeType === '3' && !item.writeAddress">{{ !item.writeAddress ? '填写信息' : '' }}</div>
          <div v-else>无</div>
        </div>
      </div>
      <div v-else class="no-data">暂无数据</div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { Popup, Icon, showLoadingToast, closeToast } from 'vant';
import { computed, defineEmits, defineProps, ref, inject, reactive, toRaw } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import dayjs from 'dayjs';
import { httpRequest } from '@/utils/service';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog', 'changeAddress']);
const closeDialog = () => {
  emits('closeDialog');
};
interface Prize {
  writeAddress: boolean;
  drawTime: Date;
  prizeType: string;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  winStatus: number;
  recordId: string;
}

const prizes = reactive([] as Prize[]);

const openDialog = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90007/getUserPrizes');
    closeToast();
    Object.assign(prizes, res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

const changeAddress = (data: Prize) => {
  emits('changeAddress', toRaw(data));
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6rem;
  height: 7rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/162872/15/40921/10205/66308a52F62681c36/6424e8b7543b3280.jpg') no-repeat;
  background-size: contain;
  padding: 0.25rem 0.28rem;
  box-sizing: border-box;
  text-align: center;

  .title {
    padding: 0.1rem 0.4rem;
    box-sizing: border-box;
    display: inline-block;
    white-space: nowrap;
    line-height: 1;
    font-size: 0.32rem;
    color: #8e5014;
    background-color: #d6b377;
    border-radius: 0.23rem;
    border: solid 0.03rem #d7b36c;
  }

  .list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.24rem;
    color: #36393d;
    background-color: #b1d3ff;
    border: solid 0.01rem #f5faff;
    padding: 0.15rem 0rem;
    box-sizing: border-box;

    div {
      flex: 1;

      &:nth-child(2) {
        flex: 2;
        border-left: 0.03rem solid #fff;
        border-right: 0.03rem solid #fff;
      }
    }
  }
  .item-box {
    max-height: 5.5rem;
    overflow-y: auto;
  }
  .item {
    margin-bottom: 0.1rem;
    font-size: 0.24rem;
    margin-top: 0;
    background-color: #eaf4ff;
    div {
      &:nth-child(2) {
        border: 0;
      }
    }
    .btn {
      background: #3b57de;
      color: #fff;
      line-height: 0.4rem;
      box-sizing: border-box;
      border-radius: 0.2rem;
    }
  }
}
.no-data {
  font-size: 0.3rem;
  margin-top: 1.5rem;
  text-align: center;
}
.close_icon {
  position: absolute;
  right: 0.3rem;
  top: 0.1rem;
}
</style>
