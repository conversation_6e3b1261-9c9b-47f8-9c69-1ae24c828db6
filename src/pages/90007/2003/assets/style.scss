.kv {
  //background: url(https://img10.360buyimg.com/imgzone/jfs/t1/107364/15/49209/97196/662f722dFee5ef6be/b36344acf0c9bbb7.png) no-repeat;
  //background-size: cover;
  height: 8.76rem;
  width: 7.5rem;
  position: relative;

  .ac-time {
    font-size: 0.19rem;
    line-height: 0.27rem;
    color: #261e93;
    position: absolute;
    top: 2.54rem;
    left: 0.3rem;
    right: 0.3rem;
    text-align: center;
  }

  .kv-btn {
    font-size: 0.21rem;
    line-height: 1;
    color: #545454;
    padding: 0.07rem 0.17rem;
    border-radius: 0.15rem 0 0 0.15rem;
    box-sizing: border-box;
    position: absolute;
    top: 0.47rem;
    background-color: #ffffff;
    right: 0;
  }

  .prize {
    top: 0.86rem;
  }

  .history {
    top: 1.25rem;
  }
}

.content {
  width: 7.05rem;
  background-color: #f0f5fc;
  border-radius: 0.12rem;
  padding: 0.85rem 0.5rem 0.2rem;
  box-sizing: border-box;
  margin: 0 auto;
  position: relative;

  .progress {
    background-image: linear-gradient(180deg, #65adff 0%, #3d96fd 54%, #147ffb 100%), linear-gradient(#518bff, #518bff);
    width: 100%;
    background-repeat: no-repeat;
    background-size: 100%;
    height: 0.17rem;
    border-radius: 0.2rem;
    position: relative;

    .progress-bar {
      width: 0%;
      background-repeat: repeat-x;
      background-size: contain;
      border-radius: 0.2rem;
      height: 0.11rem;
      position: absolute;
      top: 0.03rem;
      left: 0;
      background-image: linear-gradient(90deg, #efd98d 0%, #fffac6 100%), linear-gradient(#518bff, #518bff);
    }
  }

  .gift {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    top: 0.36rem;
    left: 0.54rem;
    right: 0.54rem;
    color: var(--drawTextColor);
    .gift-item {
      flex: 1;
      text-align: center;

      .gift-step {
        font-size: 0.23rem;
        font-weight: bold;
        margin-bottom: 0.1rem;
      }

      .gift-icon {
        width: 0.26rem;
        margin: 0 auto;
      }

      .gift-img {
        width: 1.66rem;
        height: 1.15rem;
        background-color: #b5d1ef;
        border-radius: 0.05rem;
        margin: 0.2rem auto 0.18rem;
        padding-top: 0.1rem;
        box-sizing: border-box;

        img {
          width: 1.46rem;
          height: 0.95rem;
          object-fit: contain;
          margin: 0 auto;
        }
      }

      .gift-name {
        font-size: 0.2rem;
        font-weight: 700;
        font-stretch: normal;
        line-height: 0.2rem;
        color: #01185b;
      }

      .gift-remainder {
        font-size: 0.15rem;
        font-weight: 700;
        line-height: 0.2rem;
        color: #423b49;
        margin-top: 0.05rem;
      }

      .gift-btn {
        background-color: var(--drawBtnBg);
        border-radius: 0.12rem;
        font-size: 0.15rem;
        font-weight: bold;
        line-height: 0.2rem;
        color: var(--drawBtnColor);
        padding: 0.03rem 0.22rem;
        box-sizing: border-box;
        display: inline-block;
        margin-top: 0.12rem;
      }

      .item-btn-disabled {
        opacity: 0.5;
      }
    }
  }

  .user-info {
    font-size: 0.21rem;
    font-weight: bold;
    line-height: 0.25rem;
    text-align: center;
    margin-top: 2.8rem;
  }
}

.sku {
  margin-top: 0.1rem;
  padding: 0.3rem 0.13rem;
  box-sizing: border-box;

  .title {
    font-size: 0.42rem;
    color: #001763;
    padding-left: 0.2rem;
    box-sizing: border-box;
  }

  .til {
    font-size: 0.21rem;
    line-height: 0.23rem;
    color: #67696c;
    padding-left: 0.15rem;
    box-sizing: border-box;
  }

  .sku-list {
    margin-top: 0.2rem;
    max-height: 8rem;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 0.1rem;

    .sku-item {
      text-align: center;

      .sku-img {
        background-color: #518bff;
        border-radius: 0.15rem;
        border: solid 0.02rem #dcb97d;
        box-sizing: border-box;
        height: 3.34rem;
      }

      .sku-btn {
        width: 1.43rem;
        margin: 0.2rem auto 0;
      }
    }
  }

  .sku-tips {
    font-size: 0.21rem;
    line-height: 0.23rem;
    color: #67696c;
    text-align: center;
    margin-top: 0.2rem;
  }
}
.act-info {
  font-size: 0.21rem;
  font-weight: bold;
  margin: 0.1rem 0.275rem 0;
  color: #474747;
  text-align: center;
  word-wrap: break-word;
}
.go-shop {
  width: 3.86rem;
  margin: 0 auto 0.3rem;
  display: block;
}
