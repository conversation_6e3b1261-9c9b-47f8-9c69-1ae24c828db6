<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
      <div class="header-kv">
          <img
            :src="furnish.actBg"
            alt=""
            class="kv-img" />
          <div class="header-content">
            <div>
              <div
                class="header-btn"
                v-for="(btn, index) in btnList"
                :key="index"
                @click="btn.event"
                v-click-track="btn.clickCode">
                {{ btn.name }}
              </div>
            </div>
          </div>
          <div v-if=" status == 1" class="trial-btn" @click="handleApply">立刻申请</div>
          <div v-if=" status == 2" class="trial-btn gray">不符合参与条件</div>
          <div v-if=" status == 3" class="trial-btn gray">您已领取</div>
          <div v-if=" status == 4" class="trial-btn gray">试用商品已领完</div>
        </div>
      <div>
  </div>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress v-if="showSaveAddress" @close="saveAddressClose" :prizeId="prizeId" ></SaveAddress>
    </VanPopup>

    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showTrialSuccess" position="center">
      <TrialSuccess v-if="showTrialSuccess" @close="showTrialSuccess=false"  ></TrialSuccess>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { inject, ref } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';

import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import useThreshold from '@/hooks/useThreshold';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

import RulePopup from '../components/RulePopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import TrialSuccess from '../components/TrialSuccess.vue';
import Threshold2 from '@/components/Threshold2/index.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const showLimit = ref(false);
const showRule = ref(false);
const showSaveAddress = ref(false);
const showTrialSuccess = ref(false);
const ruleTest = ref('');
const prizeId = ref('');

const status = ref(0); // 1:可领取 2:不符合参与条件 3:已领取 4:试用商品已领完

const openCard = () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

// 初始化
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});
// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
// 按钮列表
const btnList = [
  {
    name: '活动规则',
    clickCode: 'hdgz',
    event: () => {
      showRulePopup();
    },
  },
];

// 获取活动信息(
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/95007/activity');
    if (data.status === 5) {
      openCard();
    } else {
      status.value = data.status;
      prizeId.value = data.prizeId;
    }
  } catch (error) {
    console.error(error);
  }
};
// 报名参加活动
const handleApply = () => {
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  lzReportClick('ljsq');
  showSaveAddress.value = true;
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityInfo()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
// 保存地址弹窗关闭
const saveAddressClose = (type: any) => {
  if (type) {
    showSaveAddress.value = false;
    init();
  } else {
    showSaveAddress.value = false;
  }
};

init();
</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 1rem;
}
.header-kv {
  position: relative;
  .kv-img {
    width: 100%;
  }
  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.26rem 0.3rem 0;
    display: flex;
    justify-content: flex-end;
    .create-img {
      .header-btn {
        div {
          margin-top: -0.18rem;
        }
      }
    }
    .header-btn {
      padding: 0 0.2rem;
      height: 0.44rem;
      margin-bottom: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: transparent;
    }
  }
  .trial-btn {
    width: 7rem;
    height: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #dc1b06;
    background: url("//img10.360buyimg.com/imgzone/jfs/t1/241497/32/14533/21314/66a06cbeF160d0d98/b0b2925494cd1d9d.png") no-repeat;
    background-size: 100%;
    margin: 0 auto;
    position: absolute;
    top: 11rem;
    left: 0.25rem;
    font-weight: bold;
    font-size: 0.4rem;
  }
  .gray {
    color: #8b7c7c;
    filter: grayscale(100%);
  }
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
