import { computed, reactive } from 'vue';

export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  cutDownBg: '', // 倒计时组件
  cutDownColor: '', // 倒计时字体颜色
  prizeBg: '', // 奖品背景
  ruleBg: '', // 规则背景
  winnersBg: '', // 曝光商品标题
  btnToShop: '', // 按钮跳转店铺
  btnShare: '', // 按钮分享
  signButtonBg: '', // 报名按钮背景图
  signButtonColor: '', // 报名按钮字体颜色
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
  backgroundRepeat: 'repeat-y',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));

const cutDownColor = computed(() => ({
  color: furnish.cutDownColor ?? '',
}));

const btnToShop = computed(() => ({
  backgroundImage: furnish.btnToShop ? `url("${furnish.btnToShop}")` : '',
}));

const btnShare = computed(() => ({
  backgroundImage: furnish.btnShare ? `url("${furnish.btnShare}")` : '',
}));

const singnStyle = computed(() => ({
  color: furnish.signButtonColor ?? '',
  backgroundImage: furnish.signButtonBg ? `url("${furnish.signButtonBg}")` : '',
}));
export default {
  pageBg,
  shopNameColor,
  headerBtn,
  cutDownColor,
  btnToShop,
  btnShare,
  singnStyle,
};
