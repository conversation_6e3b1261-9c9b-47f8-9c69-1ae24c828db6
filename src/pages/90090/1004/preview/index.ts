import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/106901/32/50116/6006/66f67d9fF3d6aee9d/dd76bcb3fa2135c5.png',
  actBgColor: '#00622a',
  shopNameColor: '',
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/178535/1/46608/5841/66f67d9cFc8261624/30bd819703dc78b6.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/245427/24/18953/6009/66f67d9cF4269c955/514913aac9c4f413.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/249597/17/19752/347292/66f67d9cF8848d01b/f9ae8caa044ddadf.png',
  qualificationBg: '//img10.360buyimg.com/imgzone/jfs/t1/176172/2/47268/113618/66f4c6ffF7d3d9d35/ffa0b95d8285249f.png',
  qualificationTextColor: '#007a2b',
  orderLimitTextColor: '#fff',
  progressBarBg: '//img10.360buyimg.com/imgzone/jfs/t1/184949/20/45719/154598/66f3e058F1441747e/706c9ece7bbfe60c.png',
  topTextColor: '#ecbe5b',
  bottomTextColor: '#fff',
  choosePrizeTitleColor: '#1d48c7',
  prizeItemBg: '//img10.360buyimg.com/imgzone/jfs/t1/190499/26/48262/19422/66f67f04Fddb96c1d/a966a17a6584c192.png',
  prizeItemTitleBg: '//img10.360buyimg.com/imgzone/jfs/t1/166013/20/43931/1145/66e3b030Fd0b4ea76/ab158b7ff347d115.png',
  prizeItemTitleColor: '#00622a',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/240526/16/19110/7385/66f4c735F0b52b142/ad4ac6571138567a.png',
  getPrizeGrayBtn: '//img10.360buyimg.com/imgzone/jfs/t1/171631/20/47218/5761/66f6732bFbff7d85a/e5c7052e3b2ada26.png',
  noMorePrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/227323/25/26937/3714/66e3b02fFbc4c8836/e2f3be6204ca242d.png',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/104733/22/51635/880668/66f67d9eF70e7c588/74af086cedb6acc2.png',
  priceColor: '#f5c856',
  btnToTop: '//img10.360buyimg.com/imgzone/jfs/t1/176158/36/48036/76780/66f67d9aF392cce5a/c46f2ffadb80c842.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/163534/34/4106/101460/600e2292Ed3609887/824e50f6ac5477dd.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/123675/39/40734/23901/64feb230F9caecd1c/85d302f176ff72ad.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/101134/18/44826/69569/64feb231F72feb750/36145802ebe71525.png',
  jumpUrl: '',
  canNotCloseJoinPopup: '1',
  hotZoneList: [
  ],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '复购有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('decoData', a);
  app.provide('isPreview', true);
  app.mount('#app');
});
