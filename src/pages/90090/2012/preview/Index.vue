<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" />
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <!--          {{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtnRules.value" @click="showRulePop" />
          <div class="header-btn my-prizes" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop" />
        </div>
      </div>
    </div>
    <!--七月会员复购限定特权-->
    <div class="equitySelection" :style="furnishStyles.equitySelection.value">
      <div class="qualificationBg" :style="furnishStyles.qualificationBg.value">
        <!-- <div class="text" :style="furnishStyles.qualificationTextColor.value">
          {{ text }}
        </div> -->
        <div class="stepOne">
          <div class="beforeOrder" :style="furnishStyles.preOrderBg.value">
            xxxx.xx.xx-xxxx.xx.xx
            <img :src="furnish.orderFinishIcon" alt="" class="orderFinishIcon" />
          </div>
          <div class="afterOrder" :style="furnishStyles.postOrderBg.value">
            xxxx.xx.xx-xxxx.xx.xx
            <img :src="furnish.orderFinishIcon" alt="" class="orderFinishIcon" />
          </div>
          <img :src="furnish.orderIcon" class="and" alt="" />
        </div>
        <img :src="furnish.orderUnfinishedIcon" alt="" class="orderUnfinishedIcon" />
        <div class="text" :style="furnishStyles.qualificationTextColor.value">
          {{ text }}
        </div>
        <!-- <div class="toBuyBtn" v-click-track="'ljgm'" @click="buyNow" /> -->
      </div>
      <div class="progressBarBg" :style="furnishStyles.progressBarBg.value">
        <div :style="furnishStyles.topTextColor.value">
          <div class="num">×{{ itemTotal }}</div>
          <div class="get">得</div>
          <div class="progressLineBox">
            <div class="progress" />
            <div class="rate">0/{{ itemTotal }}</div>
          </div>
        </div>
        <div class="bottomText" :style="furnishStyles.bottomTextColor.value">{{ orderNum }}笔{{ orderNum > 1 ? '及以上' : '' }}订单购买{{ orderSkuisExposure === 0 ? '全店' : '指定' }}商品任意{{ itemTotal }}件，订单完成24小时后更新进度。</div>
      </div>
      <img class="toBuyBtn" :src="furnish.equitySelectionToBug" alt="" v-click-track="'ljgm'" @click="buyNow" />
      <!-- <div class="orderTipsText" :style="furnishStyles.orderLimitTextColor.value">
        <div>*{{ dayjs(formerOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss') }}-{{ dayjs(orderRestrainStartTime).format('YYYY年MM月DD日 HH:mm:ss') }}在伊利牛奶京东自营旗舰店有已完成的订单</div>
        <div>*{{ dayjs(formerOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss') }}-{{ dayjs(orderRestrainStartTime).format('YYYY年MM月DD日 HH:mm:ss') }}在{{ shopName }}有已完成的订单</div>
        <div>*在活动期间复购下单，订单完成48h内可来领取权益</div>
      </div> -->
    </div>
    <!--限时惊喜福利-->
    <div class="timeLimitedPrizeBg" :style="furnishStyles.timeLimitedPrizeBg.value">
      <div class="circle">
        <div class="text">+{{ prizeName }}100京豆</div>
      </div>
      <div class="timeLimitedText" :style="furnishStyles.timeLimitedTextColor.value">
        <div>
          <span class="fontLarge">
            为您准备1份惊喜福利<br />
            本月完成购买后<br />
          </span>
          <span class="fontSmall">可以额外领取{{ prizeName }}100京豆！</span>
        </div>
      </div>
      <div class="timeLimitedBtnBg" @click="buyNow" :style="furnishStyles.timeLimitedBtnBg.value">选购<br />商品</div>
    </div>
    <!--权益选择-->
    <div class="hotZoneBox">
      <img class="hotZone" :src="furnish.prizeBg" alt="" />
      <!-- <div class="chooseTitle">
        <div class="box" :style="furnishStyles.choosePrizeTitleColor.value">
          <div class="chooseTitleTextBefore">{{ multiplePrizeList.length }}</div>
          <div class="chooseTitleText">大权益任选其</div>
          <div class="chooseTitleTextAfter">{{ receiveNum }}</div>
        </div>
      </div> -->
      <div class="prizeBox">
        <div class="choosePrizes">
          <div class="choosePrizesBox">
            <div class="list-view" v-for="(item, index) in multiplePrizeList" :key="index">
              <div class="itemBg" :style="furnishStyles.prizeItemBg.value">
                <div class="equity_name">
                  <div>
                    <svg width="100%" height="100%" viewBox="0 0 300 300">
                      <defs>
                        <path id="semi" d="M55 100a50 25 0 1 1 225 0"></path>
                      </defs>
                      <use xlink:href="#semi" stroke="none" fill="none"></use>
                      <text text-anchor="middle" :style="[furnishStyles.prizeItemTitleColor.value, getFontSizeStyle(item.prizeName.length)]">
                        <textPath xlink:href="#semi" startOffset="50%">
                          {{ item.prizeName }}
                        </textPath>
                      </text>
                    </svg>
                  </div>
                </div>
                <img class="equity_img" :src="item.prizeImg ? item.prizeImg : '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png'" alt="" />
                <div class="equity_num">{{ item.sendTotalCount }}</div>
              </div>
              <div class="equity_btn noJurisdiction" :style="furnishStyles.getPrizeBtn.value" @click="ShowToast">领取奖励</div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="equity_tips">温馨提示：权益兑换会有专属的兑奖码，请正确输入您的手机号等信息，确保无误。 以免充值错误。兑换手机号需确保已注册平台账号，否则激活失败不负责补偿。 兑奖码有效期为xxxx-xx-xx~xxxx-xx-xx，过期无效，请及时兑换。</div> -->
    </div>
    <!--曝光商品-->
    <div ref="skuTitle" class="sku" v-if="isExposure === 1">
      <img :src="furnish.showSkuBg" class="sku-list-img" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="ShowToast">
          <img :src="item.showSkuImage" alt="">
          <div class="sku-price">
            {{ item.jdPrice }}
          </div>
        </div>
      </div>
    </div>
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize @close="showMyPrize = false" />
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick, computed } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultLadderPrizeList, defaultMultiplePrizeList, defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import html2canvas from 'html2canvas';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const total = ref(0);

const isLoadingFinish = ref(false);

const text = '尊敬的伊利老客，恭喜您获得特权资格，下单复购即可领取视频月卡，快去选购吧！';
// 订单笔数
const orderNum = ref(1);
// 件数
const itemTotal = ref(1);
const receiveNum = ref(1);

type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
};

const ladderPrizeList = ref<Prize>([]);
ladderPrizeList.value = defaultLadderPrizeList;
const registrationPrizeList = ref<Prize>([defaultStateList]);
const multiplePrizeList = ref<Prize>([]);
multiplePrizeList.value = defaultMultiplePrizeList;
const Size = [
  {
    10: '0.22rem',
  },
  {
    9: '0.25rem',
  },
  {
    8: '0.26rem',
  },
  {
    7: '0.28rem',
  },
  {
    6: '0.3rem',
  },
  {
    5: '0.3rem',
  },
  {
    4: '0.3rem',
  },
  {
    3: '0.3rem',
  },
  {
    2: '0.3rem',
  },
  {
    1: '0.3rem',
  },
];
const fontSizeCache = new Map<number, string>();

const FindFontSize = (length: number) => {
  if (!length || length === 0) {
    return null;
  }
  if (fontSizeCache.has(length)) {
    return fontSizeCache.get(length);
  }
  const item = Size.find((obj) => Object.keys(obj).includes(length.toString()));
  if (item) {
    const [key, value] = Object.entries(item)[0];
    fontSizeCache.set(length, value?.toString());
    console.log(value, 'value');
    return value;
  }
  return null;
};

const getFontSizeStyle = (length: number) => ({
  fontSize: FindFontSize(length),
});
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/273179/1/9460/47415/67e21addFefd30445/421fd6ef45fc1a05.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/273179/1/9460/47415/67e21addFefd30445/421fd6ef45fc1a05.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/273179/1/9460/47415/67e21addFefd30445/421fd6ef45fc1a05.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/273179/1/9460/47415/67e21addFefd30445/421fd6ef45fc1a05.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/273179/1/9460/47415/67e21addFefd30445/421fd6ef45fc1a05.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/273179/1/9460/47415/67e21addFefd30445/421fd6ef45fc1a05.png',
  },
]);
const orderSkuListPreview = ref<Sku[]>([]);
const nextStateAmount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);
const orderRestrainStartTime = ref(new Date().getTime());
const days = ref(180);
const formerOrderStartTime = ref();

const showRulePop = () => {
  showRule.value = true;
};

const showMyPrizePop = () => {
  showMyPrize.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

// const toSaveAddress = (id: string) => {
//   addressId.value = id;
//   showSaveAddress.value = true;
// };

const orderSkuisExposure = ref(1);
const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

const skuTitle = ref();
const buyNow = () => {
  skuTitle.value.scrollIntoView({ behavior: 'smooth' });
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  const list1 = data.ladderPrizeList;
  const list2 = data.registrationPrizeList;
  const list3 = data.multiplePrizeList;
  if (list1.prizeType !== 0) {
    ladderPrizeList.value = list1;
  }
  if (list2.prizeType !== 0) {
    registrationPrizeList.value = list2;
  }
  if (list3.prizeType !== 0) {
    multiplePrizeList.value = list3;
  }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  orderNum.value = data.orderStrokeStatus.toString();
  itemTotal.value = data.itemTotal;
  receiveNum.value = data.receiveNum;
  ruleTest.value = data.rules;
  orderSkuisExposure.value = data.orderSkuisExposure;
  isExposure.value = data.isExposure;
  orderRestrainStartTime.value = dayjs(data.orderRestrainStartTime).subtract(1, 'seconds').valueOf();
  days.value = data.days;
  formerOrderStartTime.value = dayjs(orderRestrainStartTime.value).subtract(days.value, 'days').valueOf();
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    const list1 = activityData.ladderPrizeList;
    const list2 = activityData.registrationPrizeList;
    const list3 = activityData.multiplePrizeList;
    if (list1.prizeType !== 0) {
      ladderPrizeList.value = list1;
    }
    if (list2.prizeType !== 0) {
      registrationPrizeList.value = list2;
    }
    if (list3.prizeType !== 0) {
      multiplePrizeList.value = list3;
    }
    ruleTest.value = activityData.rules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
    // orderSkuisExposure.value = activityData.orderSkuisExposure;
    isExposure.value = activityData.isExposure;
    orderRestrainStartTime.value = dayjs(activityData.orderRestrainStartTime).subtract(1, 'seconds').valueOf();
    days.value = activityData.days;
    formerOrderStartTime.value = dayjs(orderRestrainStartTime.value).subtract(days.value, 'days').valueOf();
    orderNum.value = activityData.orderStrokeStatus.toString();
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};

const goTop = () => {
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
};
</script>
<style>
@font-face {
  font-family: 'FZZZHJTFont';
  src: url('../style/fzzzhjt.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'FZZZHJTFont';
  letter-spacing: 0px;
}
</style>
<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  margin-bottom: 1.2rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.28rem;
    height: 0.45rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .my-prizes {
    position: relative;
    top: 1.9rem;
    right: 0.2rem;
    width: 0.8rem;
    height: 0.85rem;
    z-index: 100;
  }
}
.equitySelection {
  width: 7.5rem;
  height: 7.22rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  .qualificationBg {
    width: 7.5rem;
    height: 1.61rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top: 2.1rem;
    left: 0;
    right: 0;
    padding: 0.5rem 0 0 1.3rem;
    .text {
      padding-top: 0.1rem;
      font-size: 0.15rem;
      font-stretch: normal;
      letter-spacing: 0;
      text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
    }
    .toBuyBtn {
      width: 1rem;
      height: 1rem;
      border-radius: 100%;
      margin: 0 auto;
      background-repeat: no-repeat;
      background-size: 100%;
      position: absolute;
      top: 0.4rem;
      right: 0.5rem;
    }
    .stepOne {
      display: flex;
      align-items: center;
      position: relative;
      width: fit-content;
      .beforeOrder,
      .afterOrder {
        position: relative;
        width: 2.7rem;
        height: 0.58rem;
        background-repeat: no-repeat;
        background-size: 100%;
        font-size: 0.19rem;
        line-height: 0.58rem;
        padding: 0 0.1rem;
        .orderFinishIcon {
          position: absolute;
          top: -0.35rem;
          width: 0.88rem;
          left: 0.6rem;
        }
      }
      .beforeOrder {
        margin-right: -0.2rem;
      }
      .afterOrder {
        text-align: right;
      }
      .gary {
        filter: grayscale(0.8);
      }
      .and {
        position: absolute;
        top: -0.1rem;
        left: 50%;
        transform: translateX(-50%);
        width: 0.7rem;
      }
    }
    .orderUnfinishedIcon {
      position: absolute;
      top: 0.4rem;
      right: 0.2rem;
      width: 1.3rem;
    }
  }
  .progressBarBg {
    width: 7.5rem;
    height: 2.43rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top: 3.65rem;
    left: 0;
    right: 0;
    .num {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.35rem;
      font-size: 0.3rem;
    }
    .get {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.5rem;
      font-size: 0.3rem;
    }
    .progressLineBox {
      display: flex;
      margin: 0 auto;
      width: 6rem;
      position: relative;
      top: 0.6rem;
      .progress {
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/167858/31/38810/7626/66e4f188F4067c97f/c71efac3074ef1f3.png) no-repeat;
        background-size: 100%;
        width: 5.58rem;
        height: 0.23rem;
        position: relative;
        transform: translateY(25%);
        .bubble {
          background: url(//img10.360buyimg.com/imgzone/jfs/t1/37594/26/24692/1341/66eb9924Fba700108/a141ab2ae35e4d8b.png) no-repeat;
          background-size: 100% 100%;
          margin-top: 0.01rem;
          height: 0.23rem;
          position: relative;
          border-radius: 0.4rem;
        }
        .point {
          background: url(//img10.360buyimg.com/imgzone/jfs/t1/244014/34/19491/1043/66eb9fcfF008f5122/4dbe781631527f85.png) no-repeat;
          background-size: 100%;
          width: 0.23rem;
          height: 0.23rem;
          position: absolute;
          right: 0;
        }
      }
      .rate {
        text-align: right;
        font-size: 0.24rem;
        margin: 0 0 0 0.1rem;
      }
    }
    .bottomText {
      position: relative;
      margin: 0 auto;
      text-align: center;
      top: 0.45rem;
      font-size: 0.19rem;
      line-height: 0.6rem;
      text-shadow: -1px -1px 0 #fff, /* Top-left shadow */ 1px -1px 0 #fff, /* Top-right shadow */ -1px 1px 0 #fff, /* Bottom-left shadow */ 1px 1px 0 #fff; /* Bottom-right shadow */
    }
  }
  .orderTipsText {
    width: 7.5rem;
    height: 0.6rem;
    position: absolute;
    bottom: 0.4rem;
    font-size: 0.16rem;
    padding: 0 0.5rem;
  }
  .toBuyBtn {
    width: 1.8rem;
    position: absolute;
    top: 6rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
.timeLimitedPrizeBg {
  width: 7.5rem;
  height: 3.9rem;
  margin: 0.2rem auto;
  background-repeat: no-repeat;
  background-size: 100%;
  position: relative;
  .circle {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/186791/36/48536/6429/67052e84F806b21d7/ef2aba331f635d86.png) no-repeat;
    background-size: 100% 100%;
    width: 0.72rem;
    height: 0.72rem;
    color: #0069d0;
    font-size: 0.13rem;
    text-align: center;
    line-height: 0.15rem;
    word-break: break-all;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0.06rem;
    position: absolute;
    top: 1.15rem;
    left: 1.65rem;
  }
  .timeLimitedText {
    position: absolute;
    top: 1.45rem;
    left: 2.7rem;
    line-height: 0.4rem;
    .fontLarge {
      font-size: 0.28rem;
    }
    .fontSmall {
      font-size: 0.23rem;
    }
  }
  .timeLimitedShortText {
    position: absolute;
    top: 1.4rem;
    left: 2.5rem;
    line-height: 0.4rem;
    .fontLarge {
      font-size: 0.32rem;
    }
    .fontSmall {
      font-size: 0.23rem;
    }
  }
  .timeLimitedBtnBg {
    background-size: 100% 100%;
    width: 1.5rem;
    height: 1.54rem;
    position: absolute;
    top: 1.5rem;
    right: 0.5rem;
    text-align: center;
    box-sizing: border-box;
    padding-top: 0.3rem;
    /* line-height: 1.2; */
    font-size: 0.24rem;
  }
}
.hotZoneBox {
  width: 7.5rem;
  margin: 0 auto;
  position: relative;
  top: 0.2rem;
  .hotZone {
    width: 7.5rem;
  }
  .chooseTitle {
    position: absolute;
    top: 0.6rem;
    left: 50%;
    transform: translate(-50%);
    color: #fff;
    line-height: 0.6rem;
    box-sizing: border-box;
    white-space: nowrap;
    display: inline-block;
    width: auto;
    margin: 0 0 0 0.04rem;
    .box {
      display: flex;
      .chooseTitleText {
        margin: 0.08rem 0 0;
        font-size: 0.4rem;
        font-family: FZZZHJTFont, serif;
      }
      .chooseTitleTextBefore {
        font-size: 0.6rem;
        font-family: FZZZHJTFont, serif;
        width: auto;
        white-space: nowrap;
      }
      .chooseTitleTextAfter {
        margin: 0.08rem 0 0;
        font-size: 0.4rem;
        font-family: FZZZHJTFont, serif;
        width: auto;
        white-space: nowrap;
      }
    }
  }
  .prizeBox {
    position: absolute;
    top: 1rem;
    left: 50%;
    transform: translate(-50%);

    .choosePrizes {
      width: 6.7rem;
      background-repeat: no-repeat;
      background-size: 100%;
      margin: 0.1rem auto 0;
      .prizeLimit {
        text-align: center;
        height: 1rem;
        line-height: 1rem;
        margin-top: 1.3rem;
        color: #f0f8ff;
      }
      .choosePrizesBox {
        margin: 0 auto;
        width: 6.9rem;
        height: auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
        grid-gap: 0.1rem;
        box-sizing: border-box;
        overflow: hidden;
        .list-view {
          width: 100%;
          height: auto;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
          grid-gap: 0.1rem;
          box-sizing: border-box;
          overflow: hidden;
          margin-bottom: 0.05rem;
          .itemBg {
            width: 2.05rem;
            height: 2.97rem;
            border-radius: 0.25rem;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            color: #fff;
            padding: 0.13rem;
            // box-sizing: border-box;
            text-align: center;
            // display: flex;
            // align-items: center;
            // flex-direction: column;
            // justify-content: center;
            border: 1px solid hwb(0deg 100% 0/27%);
            position: relative;
            margin-bottom: 0.1rem;
            .equity_img {
              width: 1.5rem;
              height: 1.5rem;
              position: absolute;
              left: 50%;
              transform: translate(-50%);
              top: 0.7rem;
            }
            .equity_name {
              background-repeat: no-repeat;
              // height: 0.6rem;
              background-size: contain;
              position: absolute;
              top: 0;
              left: -0.2rem;
              right: 0;
              font-size: 0.22rem;
              line-height: 0.65rem;
              // 单行超出展示...
              word-break: break-all;
              display: -webkit-box;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
            .equity_num {
              position: absolute;
              left: 1.2rem;
              bottom: 0.15rem;
              font-size: 0.26rem;
              text-shadow: 0 0.03rem 0.04rem rgba(0, 0, 0, 0.5);
            }
          }
          .equity_btn {
            width: 1.62rem;
            height: 0.5rem;
            border-radius: 0.22rem;
            background-repeat: no-repeat;
            background-size: cover;
            font-size: 0.24rem;
            line-height: 1;
            z-index: 99;
            margin: 3.05rem 0 0 -0.95rem;
            color: #c88e2c;
            text-align: center;
            line-height: 0.45rem;
            position: relative; /* 确保伪元素定位相对于按钮 */
          }
          .noJurisdiction {
            filter: grayscale(1);
          }
          // .equity_btn::after {
          //   content: '';
          //   position: absolute;
          //   top: 0;
          //   left: 0;
          //   width: 100%;
          //   height: 100%;
          //   background-color: rgba(0, 0, 0, 0.5); /* 半透明灰色 */
          //   border-radius: 0.22rem; /* 与按钮的圆角保持一致 */
          //   pointer-events: none; /* 确保遮罩层不会阻止点击事件 */
          // }
        }
      }
    }
  }
  .equity_tips {
    position: absolute;
    bottom: 0.35rem;
    left: 50%;
    transform: translate(-50%);
    width: 6rem;
    font-size: 0.16rem;
    color: #fff;
    line-height: 0.3rem;
    text-align: left;
  }
}
.sku {
  width: 7.5rem;
  padding: 0.2rem 0;
  margin: 0rem auto;
  .sku-list-img {
    width: 7.12rem;
    height: auto;
    margin: 0 auto;
  }
  .sku-list {
    width: 7.25rem;
    margin: 0 auto;
    top: 1.9rem;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .sku-item {
    width: 3.6rem;
    overflow: hidden;
    position: relative;
    img{
      width: 3.6rem;
    }
    .go-sku-btn {
      position: absolute;
      bottom: 0.35rem;
      left: 1.85rem;
      width: 1.6rem;
      height: 0.53rem;
      //background-color: aliceblue;
    }
    .sku-price {
      position: absolute;
      font-size: 0.33rem;
      bottom: 0.7rem;
      left: 0.65rem;
      text-align: center;
      margin-top: 0.2rem;
      background: linear-gradient(to bottom, #fdf0d0, #f3c03e);/* 从上到下的渐变颜色 */
      -webkit-background-clip: text;/* 仅对文字应用背景 */
      -webkit-text-fill-color: transparent;/* 使文字本身透明 */
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
