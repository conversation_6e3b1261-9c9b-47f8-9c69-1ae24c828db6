<template>
  <div class="bg" v-if="isLoadingFinish">
    <div class="contentDiv">
      <div class="kvBgStyle" @click="onSelected(1)" :style="furnishStyles.actBg.value">
        <div class="goodDivAll">
          <div class="goodItem" v-for="(item,index) in skuList" :key="index">
            <div class="goodImgDiv">
              <img :src="item.skuMainPicture" alt="" />
            </div>
            <div class="goodLeftDiv">
              <div class="goodName">{{item.skuName}}</div>
              <div class="goodPrice">{{item.jdPrice}}</div>
            </div>
          </div>
        </div>
        <div class="prizeDivAll" :style="furnishStyles.prizeBg.value">
          <div class="prizeDiv">{{prizeList[0] && prizeList[0].prizeName ? prizeList[0].prizeName : '--'}}</div>
        </div>
      </div>
      <div class="closeDiv" :style="furnishStyles.drawBtn.value" @click="toast()">加购领好礼</div>
    </div>
  </div>
</template>
<script setup lang="ts">
import 'animate.css';
import { ref, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { PrizeInfo } from '../ts/type';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
// console.log(activityData, 'activityData');

const timer = ref(new Date().getTime());
const isLoadingFinish = ref(false);
// 装修时选择框
const selectedId = ref(1); // 装修时选择框序号
const pathParams = inject('pathParams') as any;
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);

const prizeList = ref<PrizeInfo[]>([]);
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};
const toast = () => {
  showToast('活动预览，仅供查看');
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'activity') {
    skuList.value = data.skuList ? data.skuList : [];
    prizeList.value = data.prizeList ? data.prizeList : [];
  } else if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'screen') {
    createImg();
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    skuList.value = activityData.skuList ? activityData.skuList : [];
    prizeList.value = activityData.prizeList ? activityData.prizeList : [];
  }
  if (decoData) {
    // console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>
<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  min-height: 100vh;
  position: fixed;
  background: rgba(0, 0, 0, .7);
  width: 100%;
  top:0;
  .contentDiv{
    width: 5rem;
    position: relative;
    //margin-left: calc(50% - 5rem / 2);
    //padding-top: calc(50% - 6.97rem / 2);
    //padding-top: 50%; /* 向上偏移50% */
    margin-left: 50%; /* 向左偏移50% */
    transform: translate(-50%); /* 向上和向左各自移动自身宽高的50%，实现居中 */
    padding-top:30%;
  }
  .kvBgStyle{
    //position: absolute;
    background-color: initial;
    background-repeat: no-repeat;
    background-size: 100%;
    z-index: 1;
    background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/112702/8/19136/73370/5f745840E0f0f7867/86a042369d7ceac3.png);
    width: 5rem;
    height: 6.97rem;
    position: relative;
    //left: calc(50% - 5rem / 2);
    //top: calc(50% - 6.97rem / 2);
    padding-top: 2.4rem;
    .goodDivAll{
      width: 100%;
      height: 2.3rem;
      overflow-y: scroll;
      margin: 0 auto;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      align-items: center;
      flex-direction: column;
      .goodItem{
        background: url(https://img10.360buyimg.com/imgzone/jfs/t1/115545/12/18913/2224/5f745840E0e45ea24/4215f6c3fb192091.png) no-repeat;
        background-size: 100%;
        width: 4.5rem;
        height: 1.1rem;
        flex-shrink: 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 0.24rem;
        color: #333333;
        margin-bottom: 0.1rem;
        .goodImgDiv{
          border: 0;
          vertical-align: middle;
          width: 1rem;
          height: 1rem;
          margin-left: 0.1rem;
          img{
            max-width: 100% !important;
            width:100%;
          }
        }
        .goodLeftDiv{
          margin-left: 0.2rem;
          width: 3.2rem;
          text-align: left;
          .goodName{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .goodPrice{
            color: #f23c3c;
          }
        }
      }
    }
    .prizeDivAll{
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/132303/35/11182/45009/5f745840Ea79b7769/178ca3855e559215.png) no-repeat;
      background-size: 100%;
      background-repeat: no-repeat;
      width: 5rem;
      height: 2.79rem;
      //padding-top: 1rem;
      margin-top: -0.5rem;
      z-index: 10;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      .prizeDiv{
        font-size: 0.36rem;
        width: 100%;
        text-align: center;
      }
    }
  }
  .closeDiv{
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/140721/23/9739/14379/5f74583fEb2530034/f24f4589f7b58b59.png) no-repeat;
    background-size: 100%;
    background-repeat: no-repeat;
    width: 4rem;
    height: 0.89rem;
    margin: 0rem auto 0;
    padding-top: .1rem;
    box-sizing: border-box;
    text-align: center;
    font-size: .42rem;
    color: #d01b35;
    margin-top:0.2rem;
  }
}
</style>
