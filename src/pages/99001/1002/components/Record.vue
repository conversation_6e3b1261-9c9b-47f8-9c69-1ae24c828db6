<template>
  <div class="bk" :style="furnishStyles.LockRecordBg.value">
    <div class="content">
      <!-- <div class="title">锁权记录</div> -->
      <div class="tab0">
        <div v-if="status === 0" style="line-height: 0.9rem" class="record-list">
          <div>{{ lockOrderId }}</div>
          <div class="prize-name">
            <div class="name" v-for="(item,index) in prizeName" :key="index">
              <span>{{ item }}</span>
            </div>
          </div>
          <div>{{ lockTime }}</div>
        </div>
        <div v-if="status === 1" style="text-align: center">暂无锁权记录~</div>
      </div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { ref, inject, onMounted } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useSendMessage from '@/hooks/useSendMessage';
import usePostMessage from '@/hooks/usePostMessage';
import { isPreview } from '@/utils';

const { registerHandler } = usePostMessage();
const decoData = inject('decoData') as any;
const isLoadingFinish = ref(false);

// 是否锁权   0 已锁权  1 已失效&未锁权
const status = ref(1);
const lockTime = ref('');
const lockOrderId = ref('');
const prizeName = ref('');
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const getMyRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99001/getUserLock');
    if (res.data === null) {
      status.value = 1;
    } else {
      status.value = 0;
      lockTime.value = res.data.lockTime ? dayjs(res.data.lockTime).format('YYYY/M/D HH:mm:ss') : '';
      prizeName.value = res.data.prizeName;
      lockOrderId.value = res.data.lockOrderId;
    }
    closeToast();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

!isPreview && getMyRecord();
// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
</script>
<style lang="scss">
@font-face {
  font-family: 'EnglishFont';
  src: url('../font/Cronos.ttf') format('truetype');
}

@font-face {
  font-family: 'ChineseFont';
  src: url('../font/fzxh.TTF') format('truetype');
}
</style>
<style scoped lang="scss">
.bk {
  width: 6.6rem;
  height: 8rem;
  // background: url(//img10.360buyimg.com/imgzone/jfs/t1/218610/26/40673/16968/662a4edbF05eaec23/a0db4e0611acfe3e.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
}
.content {
  font-family: 'EnglishFont', 'ChineseFont', sans-serif;
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  top: 2rem;
  .tab0 {
    padding: 0.5rem 0;
    width: 6.6rem;
    .record-list {
      display: flex;
      justify-content: space-around;
      align-items: center;
      div {
        text-align: center;
        font-size: 0.24rem;
        color: #000;
        font-weight: 600;
      }
      .prize-name {
        width: 1.8rem;
        overflow: hidden;
        .name {
          width: 1.8rem;
          white-space: nowrap;
          line-height: 0.4rem;
          /* 单行超出展示... */
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
.close {
  width: 0.49rem;
  height: 0.49rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/174703/25/39883/1360/66120180Ffcba11af/1727b01325d0590a.png') no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
