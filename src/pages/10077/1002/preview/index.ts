import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/222042/2/26711/135724/64fdb085Fb6df86cf/bede9c0df3ce9f3a.png',
  pageBg: '',
  actBgColor: '#ffcb46',
  shopNameColor: '#ffffff',
  btnColor: '#ffffff',
  btnBg: '',
  btnBorderColor: '#df4226',
  ruleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/223781/40/19203/7551/64f690f0Fbdd84c02/901f64be614e0a88.png',
  myPrizeBg: '',
  myOrderBg: '',
  cutDownBg: '',
  cutDownColor: '#f2280c',
  prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/193471/28/37934/7566/64fdb59cFc1a79856/fd1a2373c8f14075.png',
  prizeNameColor: '#000000',
  getPrizeBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/45668/31/21377/3750/63072ce9E149122d1/cafaa96f82228c46.png',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/224194/32/10394/37648/6585248fF68cd4fce/65654fd67c7ab3eb.png',
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/126001/30/40713/13988/64feccfeFf1c38c96/02bc7c3bfe65d7f7.png',
  btnToShop: 'https://img10.360buyimg.com/imgzone/jfs/t1/192572/12/27705/4069/6303376fE584f4c53/c1196beb18066383.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/90606/21/43856/63190/650c101fF8fbdabad/06a836f396ccf5e5.jpg',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/90606/21/43856/63190/650c101fF8fbdabad/06a836f396ccf5e5.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/90606/21/43856/63190/650c101fF8fbdabad/06a836f396ccf5e5.jpg',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '新单惊喜';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
