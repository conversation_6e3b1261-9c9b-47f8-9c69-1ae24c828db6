<template>
  <div :class="activityInfo.rightsType === 12 ? 'jyb-confirm-box' : 'confirm-box'">
    <div v-if="activityInfo.rightsType === 12" class='jyb-info-box'>
      需要花费<span class='num-box'>{{ exchangePoint }}</span>积分兑换
      <div class="phone-tips">请输入兑换权益手机号</div>
      <van-config-provider :theme-vars="inputPhoneNumber">
        <VanField v-model="phone" required maxlength="11" type="tel" class="phone-input"></VanField>
      </van-config-provider>
      <div class="jyb-tips">
        <p>特殊说明:</p>
        <p>①充值成功后无法退换</p>
        <p>②切勿写错手机号，如充错，责任自行承担</p>
        <p>③点击【立即兑换】后，权益领取手机号会无法修改，请确认无误后再点击确认</p>
      </div>
    </div>
    <div v-else class='info-box'>
      确定要花费<span class='num-box'>{{ exchangePoint }}</span>积分兑换吗
    </div>
    <div :class="activityInfo.rightsType === 12 ? 'jyb-btn-box' : 'btn-box'">
      <div class='cancel-btn' @click="close">取消</div>
      <div class='exchange-btn' @click="activityInfo.rightsType === 12 ? checkForm() : clickExchangeConfirm()">立即兑换</div>
    </div>
  </div>
  <div class='close-btn' @click="close" :style="{'margin-left':activityInfo.rightsType === 12 ?'2.7rem':'2.23rem'}"/>
  <!-- 兑换提示弹窗 -->
  <VanPopup teleport='body' v-model:show="showExchangeTips" :close-on-click-overlay="false">
    <ExchangeTips :exchangeTips="exchangeTips" :btnName="btnName" @close="tipsClose" @confirm="tipsClose" />
  </VanPopup>
</template>
<script lang="ts" setup>
import { onUnmounted, PropType, reactive, ref } from 'vue';
import ExchangeTips from './ExchangeTips.vue';
import _ from 'lodash';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { closeToast, showLoadingToast, showToast } from 'vant';

const props = defineProps({
  exchangePoint: {
    type: String,
    default: '',
  },
  exchangeNum: {
    type: Number,
    default: 0,
  },
  addressId: {
    type: String,
    default: '',
  },
  activityInfo: {
    type: Object as PropType<any>,
  },
});
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
const showExchangeTips = ref(false);
const exchangeTips = ref('活动太火爆,请稍后重试！');
const btnName = ref('知道了');
const currentState = ref('');
const phone = ref('');

const inputPhoneNumber = reactive({
  cellVerticalPadding: '0.02rem',
  cellHorizontalPadding: '0.02rem',
});
async function exchangeConfirm() {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data, code } = await httpRequest.post('/30002/receivePrize', { addressId: props.addressId, prizeNum: Number(props.exchangeNum), mobile: props.activityInfo.rightsType === 12 ? phone.value : '' });
    if (code === 200) {
      if (data.status === 2) {
        exchangeTips.value = `兑换成功${data.successNum}份,失败${data.failNum}份`;
      } else {
        exchangeTips.value = '兑换成功';
      }
      btnName.value = '知道了';
      currentState.value = data.status;
    }
    close(); // 关闭当前弹窗
    closeToast();
    showExchangeTips.value = true; // 打开二层提示弹窗
  } catch (error) {
    closeToast();
    exchangeTips.value = error.message;
    close(); // 关闭当前弹窗
    showExchangeTips.value = true; // 打开二层提示弹窗
  }
}
const clickExchangeConfirm = _.debounce(exchangeConfirm, 500, {
  leading: true, // 延长开始后调用
  trailing: false, // 延长结束前调用
});
// 移除组件时，取消防抖
onUnmounted(() => {
  clickExchangeConfirm.cancel();
});

const checkForm = () => { // 校验电话
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!phone.value) {
    showToast('请输入电话');
  } else if (!checkPhone.test(phone.value)) {
    showToast('请输入正确的电话');
  } else {
    clickExchangeConfirm();
  }
};
const tipsClose = () => {
  showExchangeTips.value = false;
};
</script>
<style scoped lang="scss">
.confirm-box {
  padding: 0rem 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 5rem;
  height: 3.5rem;
  background-color: #fff;
  border-radius: 0.2rem;
}
.jyb-confirm-box {
  padding: 0.3rem 0.5rem 0rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 6rem;
  background-color: #fff;
  border-radius: 0.2rem;
}
.jyb-info-box {
  margin-bottom: 0.86rem;
  font-family: PingFang-SC-Regular;
  font-size: 0.36rem;
  color: #333333;
}
.phone-tips {
  margin: 0.1rem 0rem;
  font-family: PingFang-SC-Regular;
  font-size: 0.3rem;
  color: #333333;
}
.phone-input {
  margin: 0.2rem 0rem;
  border: 0.01rem solid #9A9A9A;
  // height: 0.5rem;
}
.jyb-tips {
  margin-bottom: 0.5rem;
  font-family: PingFang-SC-Regular;
  font-size: 0.26rem;
  line-height: 0.4rem;
  color: #333333;
}
.info-box {
  margin-bottom: 0.86rem;
  font-family: PingFang-SC-Regular;
  font-size: 0.36rem;
  color: #333333;
  text-align: center;
}
.num-box {
  margin: 0rem 0.05rem;
  font-family: PingFang-SC-Regular;
  font-size: 0.36rem;
  color: #ff3333;
}
.jyb-btn-box {
  position: absolute;
  left: 0;
  bottom: 1.35rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 6rem;
  height: 0.86rem;
  cursor: pointer;
  .cancel-btn {
    width: 3rem;
    height: 0.86rem;
    line-height: 0.86rem;
    text-align: center;
    font-family: PingFang-SC-Regular;
    font-size: 0.3rem;
    color: #333333;
    border-radius: 0rem 0rem 0rem 0.2rem;
  }
  .exchange-btn {
    width: 3rem;
    height: 0.86rem;
    line-height: 0.86rem;
    text-align: center;
    font-family: PingFang-SC-Regular;
    font-size: 0.3rem;
    background-color: #3399ff;
    color: #fff;
    border-radius: 0rem 0rem 0.2rem 0rem;
  }
}
.btn-box {
  position: absolute;
  left: 0;
  top: 2.65rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 5rem;
  height: 0.86rem;
  cursor: pointer;
  .cancel-btn {
    width: 2.5rem;
    height: 0.86rem;
    line-height: 0.86rem;
    text-align: center;
    font-family: PingFang-SC-Regular;
    font-size: 0.3rem;
    color: #333333;
    border-radius: 0rem 0rem 0rem 0.2rem;
  }
  .exchange-btn {
    width: 2.5rem;
    height: 0.86rem;
    line-height: 0.86rem;
    text-align: center;
    font-family: PingFang-SC-Regular;
    font-size: 0.3rem;
    background-color: #3399ff;
    color: #fff;
    border-radius: 0rem 0rem 0.2rem 0rem;
  }
}
.close-btn {
  margin-top: 0.75rem;
  width: 0.6rem;
  height: 0.6rem;
  background: {
    image: url('//img10.360buyimg.com/imgzone/jfs/t1/110430/17/39957/1262/6459a770Fd709c84b/d608c15c4f784ed7.png');
    repeat: no-repeat;
    size: contain;
  }
  cursor: pointer;
}
</style>
