<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <!-- 奖品主图区域 -->
    <div class="prize-box">
      <img alt="" :src="furnishStyles.actBgURL.value.backgroundImage" :style="{'width' : '7.5rem','height':furnishStyles.actBgURL.value.backgroundImage ? '' : '2.4rem'}"/>
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
        <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
      </div>
      <div class="exchange-num-box" :style="furnishStyles.prizeNameTextColor.value">{{ exchangeGoodsName }}</div>
      <div class="exchange-consume-bg" :style="furnishStyles.exchangeConsumeBg.value">
        <div class="exchange-consume-box" :style="furnishStyles.exchangeConsumeBox.value">每份消耗<span>{{ exchangePoint }}</span>积分</div>
      </div>
    </div>
    <!-- 兑换产品信息区域 -->
    <div class="goods-info-box">
      <div>
        <div class="goods-info">
          <div class="goods-name">积分兑换{{ exchangeGoodsName }}</div>
          <div class="goods-exchange-num">
            <van-stepper v-model="exchangeNum"  integer button-size="0.44rem" input-width="0.44rem" :max="exchangeQuantityRemain === 'XX' ? 99 : exchangeQuantityRemain"/>
            <span class="goods-unit-box">份</span>
          </div>
        </div>
        <div class="exchange-info">
<!--          <div class="goods-num">每份数量:{{ unitCount }}</div>-->
<!--          <div class="goods-num">兑换剩余数量：{{ exchangeQuantityRemain }} <span class="unit-box">份</span></div>-->
        </div>
      </div>
      <div class="line-style" />
      <!-- 兑换产品所需积分区域 -->
      <div class="exchange-point">
        <div><span v-if="furnish.isShowInventory === 1" >奖品总数：{{sendTotalCount||0}}份</span></div>
        <div><span v-if="furnish.isShowInventory === 1" >剩余奖品份数：xx份</span></div>
        <div>
          <span>合计：消耗
          <span class="point-box" :style="furnishStyles.integralTextColor.value">{{ exchangePoint*exchangeNum ? exchangePoint*exchangeNum : 'xx'}}</span>积分
        </span>
        </div>
      </div>
    </div>
    <!-- 兑换条件区域 -->
    <div class="exchange-condition-big-box">
      <div class="title-box">兑换条件</div>
      <div class="exchange-condition-box">
        <div v-for="(item, index) in exchangeConditions" :key="index">
          <div :style="furnishStyles.exchangeConditions.value" class="exchange-condition">{{ item }}</div>
        </div>
      </div>
    </div>
    <!-- 兑换限制区域 -->
    <div class="exchange-condition-big-box">
      <div class="title-box">兑换限制</div>
      <div class="exchange-limit-tips">
        {{ exchangeLimitTips }}{{ sameTermOnce === true ? '且同期内的所有兑换活动仅可参与其中的1个' : '' }}
      </div>
    </div>
    <!-- 兑换地址区域 -->
    <div class="exchange-address-big-box" v-show="prizeType === 3">
      <div class="choose-address-title">送至</div>
      <div class="choose-address-btn">
        <span>点击新建收货地址</span>
      </div>
      <van-icon name="arrow" color="#a7a7a7"/>
    </div>
    <!-- 兑换规则区域 -->
    <div class="rules-box">
      <div class="title-box title-box-rule" :style="furnishStyles.ruleTitleBox.value">兑换规则:</div>
      <div class="rules-line" :style="furnishStyles.ruleContentBox.value">
        {{ exchangeRule }}
      </div>
    </div>
    <!-- 兑奖按钮区域 -->
    <div class="exchange-btn-box">
      <div class="exchange-record-btn" :style="furnishStyles.recordBtn.value">兑换记录</div>
      <div class="exchange-btn" :style="furnishStyles.exchangeBtn.value">立即兑换</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const exchangeNum = ref(1); // 兑换份数
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const exchangeGoodsName = ref('XXX积分兑换商品'); // 兑换商品名称
const exchangeConditions = ref(['一星会员', '二星会员', '三星会员', '四星会员', '五星会员', '关注店铺用户']); // 兑换条件
const exchangeQuantityRemain = ref('XX'); // 兑换剩余数量
const unitCount = ref(1); // 单位个数
const exchangePoint = ref(1); // 兑换积分
const exchangePointTotal = ref(1); // 兑换总积分
const exchangePrice = ref(' xx'); // 兑换价格
const exchangeLimitTips = ref('活动期内限领取1次');
const sameTermOnce = ref(false);// 同期内所有奖品是否限兑换1个
const exchangeRule = ref(`1.活动时间：xxxx-xx-xx xx:xx:xx至xxxx-xx-xx xx:xx:xx。
2.活动对象：店铺会员（一星会员，二星会员，三星会员，四星会员，五星会员）、关注店铺用户。
3.参与规则：仅活动对象可参与活动。
4.兑换说明：xx京豆，共xxx份，每日可兑最大份数：不限；可兑换数量不代表实际兑换数量，实际兑换数量以兑换成功数量为准
5.兑换所需积分：每次兑换单份兑换品需消耗xxx积分
6.兑换限制：活动期内不限制
7.【活动参与主体资格】
（1）每位自然人用户仅能使用一个京东账号参与活动，WX号、QQ、京东账号、手机号码等任一信息一致或指向同一用户的，视为同一个用户，则第一个参与本活动的账号参与结果有效，其余账号参与结果均视为无效。
（2）若发现同一位用户使用不同账号重复参与活动，承办方有权取消其参与资格。
8.【注意事项】
（1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格（如优惠已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利。
（2）如遇不可抗力(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的)，商家有权取消、修改或暂停本活动。
（3）是否获得优惠以活动发布者后台统计结果为准。
（4）因平台订单接口限流，部分订单可能有所延迟，请您耐心等待，订单状态会持续更新。
（5）法律允许范围内，本活动最终解释权归商家所有。
（6）活动商品数量有限，先到先得。
（7）兑换品兑换成功后，积分不予退回。`); // 兑换规则
const prizeType = ref(2); // 奖品类型 默认京豆
const sendTotalCount = ref(0);
// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage({
    from: 'C',
    type: 'deco',
    event: 'changeSelect',
    data: id,
  });
  selectedId.value = id;
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
const setDataInfo = (data:any) => { // 数据赋值
  console.log('数据赋值', data);
  exchangeConditions.value = data.gradeLabel;
  const newExchangePoint = data.exchangePoint[0];
  exchangeGoodsName.value = data.prizeInfo?.prizeName;
  exchangePoint.value = newExchangePoint;
  exchangeQuantityRemain.value = data.prizeInfo?.sendTotalCount;
  unitCount.value = data.prizeInfo?.unitCount;
  prizeType.value = data.prizeInfo?.prizeType;
  sameTermOnce.value = data.sameTermOnce;
  sendTotalCount.value = data.prizeInfo.sendTotalCount;
  if (data.exchangeLimit === 0) {
    exchangeLimitTips.value = '活动期内不限制';
  } if (data.exchangeLimit === 1) {
    exchangeLimitTips.value = `活动期限内可兑换${data.exchangeNum}次`;
  } if (data.exchangeLimit === 2) {
    exchangeLimitTips.value = `活动期内每日可兑换${data.exchangeNum}次`;
  }
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    setDataInfo(data);
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage({
    from: 'C',
    type: 'mounted',
    event: 'sendMounted',
    data: true,
  });
  if (activityData) {
    setDataInfo(activityData);
    console.log(activityData, 'activityData');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
img[src=""]
,img:not([src])
{
  opacity:0;
}
.bg {
  padding-bottom: 1rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}
.unit-box {
  margin-left: 0.05rem;
}
.exchange-info {
  display: flex;
  justify-content: space-between;
}
.prize-box {
  width: 7.5rem;
  position: relative;
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .exchange-num-box {
    position: absolute;
    top: 0.56rem;
    left: 0.65rem;
    margin-bottom: 0.15rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.36rem;
    color: #ffffff;
    .exchange-num {
      font-family: PingFang-SC-Bold;
      font-size: 0.9rem;
    }
  }
  .exchange-consume-bg {
    position: absolute;
    top: 1.39rem;
    left: 0.65rem;
    border-radius: 0.1rem;
    width: 42%;
    .exchange-consume-box {
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      height: 0.45rem;
      line-height: 0.45rem;
      text-align: center;
      font-family: PingFang-SC-Medium;
      font-size: 0.24rem;
    }
  }
}
.exchange-address-big-box  {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0rem 0.3rem;
  margin-bottom: 0.2rem;
  width: 7.5rem;
  height: 0.88rem;
  background-color: #ffffff;
  .choose-address-title {
    flex: 1;
    font-family: PingFang-SC-Medium;
    font-size: 0.3rem;
    color: #333333;
  }
  .choose-address-btn {
    flex: 2;
    overflow: hidden;
    text-align: right;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-family: PingFang-SC-Medium;
    font-size: 0.24rem;
    color: #a7a7a7;
  }
}
.goods-info-box {
  padding: 0.4rem 0.3rem 0.2rem;
  width: 7.5rem;
  background-color: #ffffff;
  .goods-info {
    display: flex;
    justify-content: space-between;
    text-align: right;
    .goods-unit-box {
      margin-left: 0.15rem;
      font-family: PingFang-SC-Bold;
      font-size: 0.3rem;
      height: 0.44rem;
      line-height: 0.44rem;
      color: #333333;
    }
    .goods-name {
      text-align: left;
      flex: 0.9;
      font-family: PingFang-SC-Bold;
      font-size: 0.3rem;
      color: #333333;
    }
    .goods-exchange-num {
      display: flex;
      margin-bottom: 0.25rem;
    }
  }
  .goods-num {
    margin-bottom: 0.23rem;
    font-family: PingFang-SC-Medium;
    font-size: 0.24rem;
    color: #999999;
  }
  .line-style {
    margin-bottom: 0.05rem;
    height: 0.04rem;
    border-top: solid 0.01rem #e6e6e6;
  }
  .exchange-point {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: PingFang-SC-Medium;
    font-size: 0.24rem;
    color: #666666;
    .point-box {
      margin: 0rem 0.05rem;
      font-family: PingFang-SC-Bold;
      font-size: 0.24rem;
      color: #ff3333;
    }
  }
}
.exchange-condition-big-box {
  margin: 0.2rem 0rem;
  padding: 0.3rem;
  width: 7.5rem;
  background-color: #ffffff;
  .exchange-condition-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 0.17rem;
    .exchange-condition {
      margin: 0.1rem 0.1rem 0rem 0rem;
      padding: 0rem 0.1rem;
      border-radius: 0.02rem;
      height: 0.34rem;
      line-height: 0.34rem;
      text-align: center;
      color: #0083ff;
      font-size: 0.2rem;
      background-color: #e5f2ff;
    }
  }
}
.exchange-limit-tips {
  margin-top: 0.26rem;
  font-family: PingFang-SC-Medium;
  font-size: 0.24rem;
  color: #666666;
}
.rules-box {
  margin-bottom: 0.2rem;
  padding: 0.3rem;
  width: 7.5rem;
  min-height: 2.4rem;
  background-color: #ffffff;
  .rules-line {
    margin-top: 0.3rem;
    font-family: PingFang-SC-Medium;
    font-size: 0.24rem;
    color: #666666;
    white-space: pre-wrap;
  }
}
.title-box-rule{
  font-weight: bold;
}
.title-box {
  font-family: PingFang-SC-Bold;
  font-size: 0.3rem;
  color: #333333;
}
.exchange-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  width: 100vw;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  font-family: PingFang-SC-Medium;
  font-size: 0.3rem;
  cursor: pointer;
  .exchange-btn {
    flex: 1;
    color: #fff;
    background-color: #0083ff;
    border-style: solid;
    border-width: 0.01rem;
    border-color: #0083ff;
  }
  .exchange-record-btn {
    flex: 1;
    color: #0083ff;
    background-color: #fff;
    border-color: #fff;
    border-style: solid;
    border-width: 0.01rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
