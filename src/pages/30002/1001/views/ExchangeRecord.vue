<template>
  <div class="big-box">
    <!-- 选择条件框 -->
    <div class="choose-conditions-box" @click="selectTimeShow = true">
      <div>{{ exchangeShowTime[0] }}年{{ exchangeShowTime[1] }}月<van-icon name="arrow-down" class="arrow-icon" /></div>
    </div>
    <div v-show="exchangeRecordList.length === 0" class="no-data-tip">暂无兑换记录</div>
    <!-- 信息展示区域 -->
    <div v-show="exchangeRecordList.length !== 0" v-for="(item, index) in exchangeRecordList" :key="index">
      <div class="show-info-box">
        <div class="info-top-box">
          <div class="exchange-type">{{ item.activityTypeName }}</div>
          <div class="exchange-time">
            <span class="exchange-num">兑换份数{{ item.rightsNum }}份</span>
            {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
          </div>
        </div>
        <div class="line-style" />
        <div class="info-bottom-box">
          <div class="goods-img" :style="{ 'background-image': 'url(' + item.rightsImg + ')' }" @click="showPrizeImg(item.rightsImg)" />
          <div class="goods-info-box">
            <div class="goods-title-box">
              <div :class="item.rightsType === 1 ? 'coupon-title' : 'goods-title'">{{ item.rightsName }}</div>
              <div class="coupon-limit" v-show="item.rightsType === 1">订单满{{ item.couponQuota }}元使用</div>
            </div>
            <div class="goods-state-box" v-show="item.rightsType === 3">
              <div :class="`goods-state state-${item.inKindStatus}`">{{ stateTextList[item.inKindStatus] }}</div>
              <div class="goods-receiver">
                收货人：{{ item.receiverName }}<span class="goods-mobile">{{ item.mobile }}</span>
              </div>
            </div>
            <div class="coupon-state-box" v-show="item.rightsType === 1">
              <div class="coupon-time">有效期至：{{ dayjs(item.couponValidityPeriod).format('YYYY-MM-DD HH:mm:ss') }}</div>
            </div>
            <div class="tips-box">
              <span v-if="item.rightsType === 7">兑换请查看礼品卡详情</span>
              <span v-else-if="item.rightsType === 12">{{ `权益已兑换至${item.mobile}` }}</span>
              <span v-else-if="[3, 9, 10].includes(item.rightsType)"></span>
              <span v-else-if="item.rightsType === 1">
                可在<span class="tips-red-text" @click='gotoMyCoupon'>{{ tipsList[item.rightsType] }}</span>
                中查看
              </span>
              <span v-else
                >可在<span class="tips-red-text" >{{ tipsList[item.rightsType] }}</span
                >中查看</span
              >
            </div>
          </div>
          <div class="state-info-box" v-if="item.rightsType === 3" @click="goToStateInfo(item)">
            详情
            <van-icon name="arrow" color="#3388ff" />
          </div>
          <div class="state-info-box" v-else-if="item.rightsType === 7" @click="goToGiftCardInfo(item)">
            详情
            <van-icon name="arrow" color="#3388ff" />
          </div>
          <div class="state-info-box" v-else-if="[9, 10].includes(item.rightsType)" @click="exchangePlusOrAiqiyi">
            详情
            <van-icon name="arrow" color="#3388ff" />
          </div>
          <div class="state-info-box-success" v-else>已获取</div>
        </div>
      </div>
    </div>
    <!-- 时间弹出 -->
    <VanPopup teleport="body" v-model:show="selectTimeShow" position="bottom">
      <van-date-picker @confirm="getDate" @cancel="closePop" :formatter="formatter" :columns-type="columnsType" v-model="exchangeTime" />
    </VanPopup>
    <!-- 图片放大 -->
    <VanPopup teleport="body" v-model:show="prizeImgPopup">
      <img class="prize-img-big" :src="prizeImg" alt="" />
    </VanPopup>
  </div>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { onUnmounted, ref } from 'vue';
import _ from 'lodash';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

const prizeImgPopup = ref(false);
const prizeImg = ref('');

const showPrizeImg = (url: string) => {
  prizeImg.value = url;
  prizeImgPopup.value = true;
};

const router = useRouter();
// const stateTextList = ['未发货', '已发货', '已取消'];
const stateTextList = {
  0: '未发货',
  1: '已发货',
  9: '已完成',
  '-1': '已取消',
};
const tipsList = {
  12: '权益已兑换至',
  2: '京东APP-我的-京豆',
  1: '京东APP-我的-优惠券',
  3: '',
  9: '',
  10: '',
  7: '兑换请查看礼品卡详情',
  8: '京东APP-我的-我的钱包-礼品卡',
  6: '京东APP-我的-我的钱包-红包',
};
const exchangeRecordList = ref([]);
const selectTimeShow = ref(false);
const exchangeShowTime = ref([dayjs().format('YYYY'), dayjs().format('MM')]);
const exchangeTime = ref([dayjs().format('YYYY'), dayjs().format('MM')]);
const goToStateInfo = (item: any) => {
  // 去查看实物奖品详情
  router.push({
    path: '/send/detail',
    query: { stateInfo: JSON.stringify(item) },
  });
};
const goToGiftCardInfo = (item: any) => {
  // 去查看礼品卡详情
  router.push({
    path: '/gift/card/detail',
    query: { id: item.id },
  });
};
const closePop = () => {
  exchangeTime.value = exchangeShowTime.value;
  selectTimeShow.value = false;
};
const columnsType = ref(['year', 'month']);
const formatter = (type: string, option: []) => {
  if (type === 'year') {
    option.text += '年';
  }
  if (type === 'month') {
    option.text += '月';
  }
  return option;
};
async function searchRecord(v: any) {
  try {
    const { data } = await httpRequest.post('/30002/getReceiveRecord', {
      exchangeYear: v[0],
      exchangeMonth: v[1],
    });
    exchangeRecordList.value = data;
    exchangeShowTime.value = v;
    selectTimeShow.value = false;
  } catch (error) {
    showToast(error.message);
    selectTimeShow.value = false;
  }
}
const getDate = ({ selectedValues }: any) => {
  searchRecord(selectedValues);
};
const gotoMyCoupon = () => {
  window.jmfe.toMyCoupon()
};
const init = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });

    await searchRecord([dayjs().format('YYYY'), dayjs().format('MM')]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>
<style scoped lang="scss">
.big-box {
  padding: 0.2rem 0.3rem;
  min-height: 100vh;
  background-color: #f2f2f2;
  .no-data-tip {
    line-height: 60vh;
    text-align: center;
  }
  .choose-conditions-box {
    margin-bottom: 0.2rem;
    padding: 0rem 0.3rem;
    width: 6.9rem;
    height: 0.78rem;
    line-height: 0.78rem;
    background-color: #ffffff;
    border-radius: 0.1rem;
    .arrow-icon {
      margin-left: 0.15rem;
      color: #c5c5c5;
    }
  }
  .show-info-box {
    margin-bottom: 0.2rem;
    padding: 0.19rem 0rem;
    width: 6.9rem;
    // height: 2.01rem;
    background-color: #ffffff;
    border-radius: 0.1rem;
    .info-top-box {
      margin: 0rem 0.3rem 0.16rem;
      display: flex;
      justify-content: space-between;
      .exchange-type {
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #333333;
      }
      .exchange-num {
        margin-right: 0.1rem;
        font-family: PingFang-SC-Medium;
        font-size: 0.2rem;
        color: #333333;
      }
      .exchange-time {
        height: 0.16rem;
        font-family: PingFang-SC-Medium;
        font-size: 0.2rem;
        color: #999999;
      }
    }
    .line-style {
      height: 0.04rem;
      border-top: solid 0.01rem #e6e6e6;
    }
    .tips-box {
      margin-top: 0.05rem;
      font-family: PingFang-SC-Medium;
      font-size: 0.2rem;
      color: #666666;
      .tips-red-text {
        color: #ff3333;
      }
    }
    .info-bottom-box {
      margin-top: 0.15rem;
      padding: 0rem 0.1rem 0rem 0.3rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .goods-img {
        background-image: url(http://img10.360buyimg.com/n0/jfs/t1/168955/10/7661/77119/6035ad67E795ecdb8/7ca8cf96259f6397.jpg);
        margin-right: 0.45rem;
        width: 1rem;
        height: 1rem;
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .goods-info-box {
        margin-right: 0.2rem;
        flex: 4.5;
        .goods-title-box {
          display: flex;
          justify-content: space-between;
          .coupon-limit {
            font-family: PingFang-SC-Medium;
            font-size: 0.2rem;
            color: #666666;
          }
        }
        .goods-title {
          font-family: PingFang-SC-Bold;
          font-size: 0.24rem;
          color: #333333;
          word-break: break-all;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .coupon-title {
          width: 2rem;
          font-family: PingFang-SC-Bold;
          font-size: 0.24rem;
          color: #333333;
          word-break: break-all;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
        .goods-state-box {
          margin-top: 0.2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .goods-state {
            padding: 0rem 0.04rem;
            height: 0.3rem;
            line-height: 0.3rem;
            text-align: center;
            font-family: PingFang-SC-Medium;
            font-size: 0.18rem;
            background-color: #e5f8ef;
            width: 1rem;
            margin-right: 0.15rem;
          }
          .state-0 {
            color: red; /* 状态 0 对应红色 */
          }
          .state-1 {
            color: #00bb66; /* 状态 1 对应绿色 */
          }
          .state-2 {
            color: rgb(91, 91, 96); /* 状态 2 对应蓝色 */
          }
          .goods-receiver {
            font-family: PingFang-SC-Medium;
            font-size: 0.2rem;
            color: #666666;
            flex: 1;
            word-break: break-all;
          }
          .goods-mobile {
            margin-left: 0.1rem;
          }
        }
        .coupon-state-box {
          margin-top: 0.02rem;
          .coupon-quota {
            font-family: PingFang-SC-Medium;
            font-size: 0.2rem;
            color: #666666;
          }
          .coupon-time {
            font-family: PingFang-SC-Medium;
            font-size: 0.2rem;
            color: #666666;
          }
        }
      }
      .state-info-box {
        flex: 1;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #3388ff;
      }
      .state-info-box-success {
        flex: 1;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #00bb66;
      }
    }
  }
}
.prize-img-big {
  width: 6rem;
}
</style>
