<template>
  <div :class="dataSource.length === 0 ? 'bg' : 'address-list-bg'">
    <van-config-provider :theme-vars="customAddressList">
      <div class="no-data-tip" v-show="dataSource.length === 0">暂无收货地址</div>
      <van-address-list v-model="chosenAddressId" :list="dataSource" default-tag-text="默认地址" @add="addAddress" @edit="onEdit" @select="selectAddress" add-button-text="+添加新地址" right-icon="//img10.360buyimg.com/imgzone/jfs/t1/228920/12/6980/1143/6572b2cbF9f9f4521/52a4babaffe7cbef.png" />
    </van-config-provider>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const customAddressList = reactive({
  tagRoundRadius: '0.02rem',
  tagPrimaryColor: '#ffa033',
  addressListPadding: '0.2rem 0rem 0rem',
  radiusLg: '0rem',
  addressListItemTextColor: '#666666',
  addressListItemPadding: '15px',
  radioLabelColor: '#666666',
  fontSizeLg: '0.24rem',
  paddingXs: '0.4rem',
  paddingMd: '0rem',
  buttonRoundRadius: '0rem',
});

const dataSource = ref([]);
const chosenAddressId = ref(route.query.id ? route.query.id : 1);
// 查询地址信息列表
const getAddressList = async () => {
  try {
    const { data } = await httpRequest.post('/30002/getAddressList');
    dataSource.value = data;
    if (!route.query.id) {
      const defaultAddressInfo = data.find((item) => item.isDefault === true);
      if (defaultAddressInfo) {
        chosenAddressId.value = defaultAddressInfo.id;
      }
    }
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};
const addAddress = () => {
  router.push({
    path: '/edit/address',
    query: { type: 'add' },
  });
};
const onEdit = (item: any, index: number) => {
  router.push({
    path: '/edit/address',
    query: { addressInfo: JSON.stringify(item), index, type: 'edit' },
  });
};
const selectAddress = (item: any, index: number) => {
  router.push({
    path: '/index',
    query: { address: item.address, id: item.id, index },
  });
};
const init = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getAddressList();
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.bg {
  background-color: #fff;
  max-width: 7.5rem;
  min-height: 100vh;
}
.address-list-bg {
  height: 90vh;
  background-color: #f2f2f2;
  overflow-y: scroll;
}
.no-data-tip {
  margin-top: 1rem;
  background-color: #fff;
  text-align: center;
  line-height: 60vh;
}
</style>
<style>
.van-address-item__address {
  word-break: break-all;
}
</style>
