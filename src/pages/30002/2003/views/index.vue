<template>
  <div class="bg" :style="furnishStyles.actBgColor.value">
    <!-- 奖品主图区域 -->
    <div class="kv-box">
      <img alt="" :src="furnishStyles.actBgURL.value.backgroundImage" :style="{ width: '7.5rem', height: furnishStyles.actBgURL.value.backgroundImage ? '' : '2.4rem' }" />
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
        <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
      </div>
      <div class="user-points" :class="{ itemGap: userPoints.length <= 3 }">
        <div class="item" v-for="item in userPoints" :key="item" :style="pointsStyle(userPoints.length)">{{ item }}</div>
      </div>
    </div>
    <!-- 兑换产品信息区域 -->
    <div class="content" :style="furnishStyles.prizeInfoBg.value">
      <div class="block">
        <div class="goods-info">
          <img :src="activityInfo.prizeImg" alt="" class="goods-img" />
          <div class="info" :style="furnishStyles.prizeNameTextColor.value">
            <div class="name">{{ activityInfo.rightsName }}</div>
            <div class="points">
              消耗<span :style="furnishStyles.integralTextColor.value">{{ activityInfo.exchangePointNum }}</span
              >积分
            </div>
          </div>
        </div>
        <!--rightsNum 为-1的时候表示库存为0不显示该条-->
        <div v-if="activityInfo.rightsNum !== -1">
          <div class="goods-num" v-if="exchangeLimit === 1">
            活动期内限剩余兑换次数：<span :style="furnishStyles.exchangeNumTextColor.value">{{ activityInfo.rightsNum }}次</span>
          </div>
          <div class="goods-num" v-if="exchangeLimit === 2">
            当日剩余兑换次数：<span :style="furnishStyles.exchangeNumTextColor.value">{{ activityInfo.rightsNum }}次</span>
          </div>
        </div>
        <div class="exchange-limit">
          <span class="red-bk">{{ exchangeLimitTips }}</span
          ><span v-if="sameTermOnce">且同期内的所有兑换活动仅可参与其中的1个</span>
        </div>
        <div class="exchange-address-big-box" v-show="activityInfo.rightsType === 3" v-threshold-click="goChooseAddress">
          <div class="choose-address-title">送至</div>
          <div class="choose-address-btn">
            <span>{{ addressInfo ? addressInfo : activityInfo.defAddressDetail ? activityInfo.defAddressDetail : '点击新建收货地址' }}</span>
          </div>
          <van-icon name="arrow" color="#a7a7a7" />
        </div>
      </div>
    </div>
    <!-- 兑换规则区域 -->
    <div class="rules-box">
      <div class="title-box title-box-rule" :style="furnishStyles.ruleTitleBox.value">兑换规则:</div>
      <div class="rules-line" :style="furnishStyles.ruleContentBox.value">
        {{ activityInfo.rules }}
      </div>
    </div>
    <!-- 兑奖按钮区域 -->
    <div class="exchange-btn-box">
      <img class="exchange-record-btn" :src="furnish.recordImg" alt="" @click="goExchangeRecord" />
      <img class="exchange-btn" :class="{ gray: activityInfo.status !== 0 }" :src="furnish.exchangeImg" alt="" v-threshold-click="clickExchange" />
    </div>
  </div>
  <div>
    <!-- 确认兑换弹窗 -->
    <VanPopup teleport="body" v-model:show="showExchangeConfirm" :close-on-click-overlay="false" position="bottom">
      <ExchangeConfirm :exchangePoint="activityInfo.exchangePointNum" :addressId="addressId" @close="confirmClose" :activityInfo="activityInfo" />
    </VanPopup>
    <!-- 兑换提示弹窗 -->
    <VanPopup teleport="body" v-model:show="showExchangeTips" :close-on-click-overlay="false">
      <ExchangeTips :exchangeTips="exchangeTips" :btnName="btnName" @close="tipsClose" @confirm="tipsConfirm" />
    </VanPopup>
    <!-- 兑换记录弹窗 -->
    <VanPopup teleport="body" v-model:show="showExchangeRecord">
      <ExchangeRecord />
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { ref, reactive, onMounted, onUnmounted, nextTick, inject, watch } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import ExchangeConfirm from '../components/ExchangeConfirm.vue';
import ExchangeTips from '../components/ExchangeTips.vue';
import ExchangeRecord from './ExchangeRecord.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import dayjs from 'dayjs';
import useThresholdLength from '@/hooks/useThresholdLength';

const pointsStyle = (strLength: number) => {
  if (strLength <= 4) {
    return {
      width: '0.7rem',
      fontSize: '0.47rem',
    };
  }

  return {
    width: `${0.7 - 0.0967 * (strLength - 4)}rem`,
    fontSize: `${0.79 - 0.12 * (strLength - 4)}rem`,
  };
};

const router = useRouter();
const route = useRoute();
const addressInfo = ref(route.query.address ? route.query.address : '');
const addressId = ref(route.query.id ? route.query.id : ''); // 地址id
const goExchangeRecord = () => {
  router.push('/record');
};
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;
const activityInfo = ref({}); // 活动相关数据
const exchangeLimit = ref(1); // 兑换限制 0不限制 1限制次数 2限制每日次数
const shopName = ref(baseInfo.shopName);
const userPoints = ref('0');
const showExchangeConfirm = ref(false);
const showExchangeRecord = ref(false);
const showExchangeTips = ref(false);
const exchangeTips = ref('');
const btnName = ref('知道了');
const exchangeLimitTips = ref('');
const sameTermOnce = ref(false); // 同期内所有奖品是否限兑换1个

const myLucky = ref();
const btnDisabled = useThresholdLength();
const goChooseAddress = () => {
  router.push({
    path: '/address/list',
    query: { id: addressId.value ? addressId.value : activityInfo.value.defAddressId },
  });
};
// 获取活动信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/30002/getActivityInfo');
    userPoints.value = String(data.userPoint);
    activityInfo.value = data;
    // 用户状态  0:可以领取；1：已经达到领取份数上限；2：不可领取
    if (data.status === 1 || data.status === 2) {
      exchangeTips.value = data.detail;
      if (data.detail.includes('未到今日兑换周期')) {
        const now = dayjs().format('YYYY-MM-DD');
        // 时间差值
        const diff = dayjs(`${now} ${data.exchangeCycleStartTime}`).diff(dayjs(), 'ms');
        setTimeout(() => {
          window.location.reload();
        }, diff);
      }
    }
    sameTermOnce.value = data.sameTermOnce;
    exchangeLimit.value = data.exchangeLimit;
    if (data.exchangeLimit === 0) {
      exchangeLimitTips.value = '活动期内不限制';
    }
    if (data.exchangeLimit === 1) {
      exchangeLimitTips.value = `活动期限内可兑换${data.exchangeNum}次`;
    }
    if (data.exchangeLimit === 2) {
      exchangeLimitTips.value = `活动期内每日可兑换${data.exchangeNum}次`;
    }
  } catch (error) {
    showToast(error.message);
    console.error(error);
  }
};
const clickExchange = () => {
  if (activityInfo.value.status === 0) {
    addressId.value = addressId.value ? addressId.value : activityInfo.value.defAddressId;
    showExchangeConfirm.value = true;
  } else {
    showExchangeTips.value = true;
  }
};

const tipsConfirm = () => {
  // 用户状态  0:可以领取；1：已经达到领取份数上限；2：不可领取
  if (activityInfo.value.status === 1 || activityInfo.value.status === 2) {
    showExchangeTips.value = false;
  }
};
const tipsClose = () => {
  showExchangeTips.value = false;
};
const confirmClose = () => {
  showExchangeConfirm.value = false;
};
watch(
  () => showExchangeConfirm,
  (newShowExchangeConfirm, oldShowExchangeConfirm) => {
    if (newShowExchangeConfirm.value === false) {
      getActivityInfo();
    }
  },
  { deep: true },
);
watch(
  () => showExchangeTips,
  (newShowExchangeTips, oldShowExchangeTips) => {
    if (newShowExchangeTips.value === false) {
      getActivityInfo();
    }
  },
  { deep: true },
);
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getActivityInfo();
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
img[src=''],
img:not([src]) {
  opacity: 0;
}
.bg {
  padding-bottom: 1rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}
.kv-box {
  width: 7.5rem;
  position: relative;
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .user-points {
    position: absolute;
    top: 4.85rem;
    left: 0;
    right: 0;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 0 1.9rem;
    .item {
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/154332/24/29826/2044/66c40543Ff63f56ca/e484d216d4f568ec.png') no-repeat;
      background-size: 100%;
      background-position-y: center;
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 0.71rem;
      height: 1rem;
      color: #f7e7d1;
      width: 0.7rem;
    }
  }
  .itemGap {
    justify-content: center;
    .item {
      margin: 0 0.1rem;
    }
  }
}
.content {
  width: 6.9rem;
  padding: 1rem 0.22rem 0.35rem;
  margin: 0 auto 0;
  background-repeat: no-repeat;
  background-size: 100% 1rem, 100% 0.5rem, 100% calc(100% - 1.5rem);
  background-position-y: top, bottom, 1rem;
  .block {
    position: relative;
    width: 100%;
    background-color: #ffffff;
    border-radius: 0.2rem;
    padding: 0.7rem 0.33rem 0.2rem;
    overflow: hidden;
    .goods-info {
      display: flex;
      align-items: center;
      .goods-img {
        width: 1.98rem;
        height: 1.98rem;
        object-fit: cover;
        border-radius: 50%;
      }
      .info {
        padding-left: 0.25rem;
        .name {
          font-size: 0.36rem;
        }
        .points {
          font-size: 0.3rem;
        }
      }
    }
    .goods-num {
      text-align: right;
      font-size: 0.24rem;
      span {
        font-size: 0.3rem;
      }
    }
    .exchange-address-big-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.2rem;
      height: 0.6rem;
      border-top: solid 0.01rem #dcdcdc;
      .choose-address-title {
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #333333;
      }
      .choose-address-btn {
        flex: 1;
        overflow: hidden;
        text-align: right;
        text-overflow: ellipsis;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #a7a7a7;
        padding-left: 0.25rem;
        white-space: nowrap;
      }
    }
    .exchange-limit {
      position: absolute;
      top: 0;
      left: 0;
      border-bottom-right-radius: 0.2rem;
      height: 0.45rem;
      line-height: 0.45rem;
      background-color: #48341f;
      color: #fff;
      font-size: 0.2rem;
      span {
        padding: 0 0.1rem;
      }
      .red-bk {
        display: inline-block;
        height: 0.45rem;
        line-height: 0.45rem;
        border-bottom-right-radius: 0.2rem;
        background-color: #d73b35;
      }
    }
  }
}
.rules-box {
  margin-bottom: 0.2rem;
  padding: 0.3rem;
  width: 7.5rem;
  min-height: 2.4rem;
  .rules-line {
    margin-top: 0.3rem;
    font-family: PingFang-SC-Medium;
    font-size: 0.24rem;
    color: #666666;
    white-space: pre-wrap;
  }
}
.exchange-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  width: 100vw;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  font-family: PingFang-SC-Medium;
  font-size: 0.3rem;
  cursor: pointer;
  .exchange-btn {
    width: 5.1rem;
    height: 1rem;
  }
  .exchange-record-btn {
    width: 2.4rem;
    height: 1rem;
  }
}
.gray {
  filter: grayscale(1);
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
