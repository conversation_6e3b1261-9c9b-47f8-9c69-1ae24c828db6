import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

// const _decoData = {
//   actBg: '//img10.360buyimg.com/imgzone/jfs/t1/230721/18/25797/208675/66c40543F67bc7097/a647041c83c7f462.png',
//   actBgColor: '#ffe6c6',
//   prizeInfo1: '//img10.360buyimg.com/imgzone/jfs/t1/149865/32/42031/8410/66c57eb6Fd123fd9d/dfa5eeba086776a9.png',
//   prizeInfo2: '//img10.360buyimg.com/imgzone/jfs/t1/104963/21/48363/3499/66c57eb8F87202376/197bee22113235d1.png',
//   prizeInfo3: '//img10.360buyimg.com/imgzone/jfs/t1/28855/10/21202/2555/66c57eb8F3234497f/6d073d2aca3eedcc.png',
//   prizeNameShow: true,
//   prizeNameTextColor: '#000',
//   integralTextColor: '#e40000',
//   exchangeNumTextColor: '#ba6b12',
//   recordImg: '//img10.360buyimg.com/imgzone/jfs/t1/102212/24/49977/4171/66c5a1fbF72826ae4/534cb1ea394b4862.png',
//   exchangeImg: '//img10.360buyimg.com/imgzone/jfs/t1/56956/31/26625/4979/66c5a1fbFaa41efaf/593ce9db5d7f42be.png',
//   ruleTitleBoxColor: '#79410c',
//   ruleContentBoxColor: '#371c0d',
//   cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/234496/36/5201/40547/65684b0cF86a3f8c5/48f6fafbe24138de.png',
//   h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/229342/19/5250/7561/65684b0dFd452f014/8e4d320027a59372.png',
//   mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/189167/39/42119/43061/65684b0dFb1bf3337/e9ddaa808e7801e3.png',
// };

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '积分兑换';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
