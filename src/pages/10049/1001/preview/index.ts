import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
// const a = {
//   actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/122028/9/23207/492466/623ac1e6E00ca6e8c/8a680419e6f51254.png',
//   pageBg: '',
//   actBgColor: '#3a4cc0',
//   shopNameColor: '#000',
//   btnColor: '#eb3c3c',
//   btnBg: '#ffffff',
//   btnBorderColor: '#ffffff',
//   prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/190355/22/35908/2468/64c8d793Faf0f4e43/a8c99dbf1ba2cf32.png',
//   ruleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/223325/19/30633/2333/64c8d792F91a73c84/83fdb7e2996dacf3.png',
//   wheelBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/128060/34/35394/270085/64c9c0e1F7cc0fd6a/bd13aa9907ac1a16.png',
//   countDown: 'https://img10.360buyimg.com/imgzone/jfs/t1/153836/22/21269/27200/623ac9e4E5ca034e4/e61786328470b549.png',
//   wheelTextColor: '',
//   drawsNum: '#000000',
//   prizeListImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/205000/25/22540/56298/6295a8aeE539e8fd0/cb70c28f8503aa7d.png',
//   winnersBg: '//img10.360buyimg.com/imgzone/jfs/t1/219646/3/4738/272448/61946f6dE6e104c93/a65c01e498d00589.png',
//   mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/96906/9/28593/207057/62a988b3E4f0b281e/b456c2b4a61e56f9.png',
//   cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/96906/9/28593/207057/62a988b3E4f0b281e/b456c2b4a61e56f9.png',
//   h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/96906/9/28593/207057/62a988b3E4f0b281e/b456c2b4a61e56f9.png',
// };
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '完善信息有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
