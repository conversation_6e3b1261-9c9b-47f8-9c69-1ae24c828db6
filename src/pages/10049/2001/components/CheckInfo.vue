<template>
  <div class="box">
    <div class="bg">
      <div class="close"></div>
      <div class="bottom">
        <span>确认提交完善的信息？</span>
        <div class="btn" @click="submit"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

const emits = defineEmits(['confirm']);
const submit = () => {
  emits('confirm', true);
};
</script>

<style scoped lang="scss">
.box{
  width: 6.45rem;
  margin: 60% auto;
  text-align: center;
  height: 100vh;
  .bg{
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/236915/1/1375/13040/6541c0c7F4df7548f/ab127bdd1b690ba1.png");
    color: #ffffff;
    height: 5rem;
    line-height: 1rem;
    background-repeat: round;
    width: 6.45rem;
    margin-top: -2px;
  }
  .close{
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/132288/19/40128/231/6541c0c7Fba7e193b/4e2e34bbfb4a8cb2.png");
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    top:5rem;
    left:3rem;
  }
  .bottom{
    padding-top: 1.9rem;
    padding-bottom: 0.3rem;
    border-radius: 0 0 0.2rem 0.2rem;
    span{
      margin-top: 0.9rem;
      color:#000;
      font-size: 0.4rem;
    }
  }
  .btn{
    position: absolute;
    top:8.4rem;
    left:1.26rem;
    width: 5rem;
    height: 100%;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/97508/20/45361/7616/6541c0c7F2d5af7a0/ad4abe447165c013.png");
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
</style>
