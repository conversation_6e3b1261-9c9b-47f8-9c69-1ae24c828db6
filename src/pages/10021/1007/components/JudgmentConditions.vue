<template>
  <div class="bk">
    <div class="title"></div>
    <div class="content">
      <div>
        <img class="avatar" :src="avatar" alt="" />
      </div>
      <div class="tip-text">{{ tipContent }}</div>
      <div class="shop-name">{{ baseInfo.shopName }}</div>
      <img :src="btnMap[tipBtn]" alt="" class="btn" @click="toComplete" />
      <!-- <div class="btn" @click="join">{{ tipBtnText }}</div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { inject, ref } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

const props = withDefaults(defineProps<{ tipContent: string; tipBtn: string, thresholdLevelsStatus: number }>(), {
  tipContent: '',
  tipBtn: '',
  thresholdLevelsStatus: 0,
});

const btnMap = {
  进店逛逛: '//img10.360buyimg.com/imgzone/jfs/t1/100045/22/42373/5545/6501ac40F92b8a8f6/e9a137361683f875.png',
  关注店铺: '//img10.360buyimg.com/imgzone/jfs/t1/85235/39/36644/6411/6501ad52F81f7ddfe/5cb5b971bd194510.png',
  立即入会: '//img10.360buyimg.com/imgzone/jfs/t1/111830/22/42370/6272/6501ac40F997f9883/899d8992ed80e536.png',
  关注店铺且立即入会: '//img10.360buyimg.com/imgzone/jfs/t1/195921/33/36801/7720/6501ac40F194a19eb/970ad5edf1f0686e.png',
};

const emits = defineEmits(['toComplete']);

const avatar = ref(window.sessionStorage.getItem(constant.LZ_JD_USER_AVATAR) as string);
const userName = ref(window.sessionStorage.getItem(constant.LZ_JD_USER_NAME) as string);

const toComplete = () => {
  emits('toComplete');
};

// if (!followQualify && !levelQualify) {
//   tipText.value = memberLevel > 0 ? '未达到会员等级要求无法参加' : '未关注店铺并且不是会员无法参加';
//   tipBtnText.value = memberLevel > 0 ? '//img10.360buyimg.com/imgzone/jfs/t1/100045/22/42373/5545/6501ac40F92b8a8f6/e9a137361683f875.png' : '//img10.360buyimg.com/imgzone/jfs/t1/195921/33/36801/7720/6501ac40F194a19eb/970ad5edf1f0686e.png';
//   tipBtn.value = memberLevel > 0 ? '进店逛逛' : '关注店铺且立即入会';
// } else if (!followQualify) {
//   tipText.value = '未关注店铺无法参加';
//   tipBtnText.value = '//img10.360buyimg.com/imgzone/jfs/t1/85235/39/36644/6411/6501ad52F81f7ddfe/5cb5b971bd194510.png';
//   tipBtn.value = '关注店铺';
// } else if (!levelQualify) {
//   tipText.value = memberLevel > 0 ? '未达到会员等级要求无法参加' : '不是会员无法参加';
//   tipBtnText.value = memberLevel > 0 ? '//img10.360buyimg.com/imgzone/jfs/t1/100045/22/42373/5545/6501ac40F92b8a8f6/e9a137361683f875.png' : '//img10.360buyimg.com/imgzone/jfs/t1/111830/22/42370/6272/6501ac40F997f9883/899d8992ed80e536.png';
//   tipBtn.value = memberLevel > 0 ? '进店逛逛' : '立即入会';
// } else {
//   showPopup.value = false;
// }
</script>

<style scoped lang="scss">
.bk {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/103350/6/34399/24546/6501aa36Fe08a5fdd/91bbbcdeb08df485.png);
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6rem;
  height: 7rem;
  padding-top: 1.5rem;
}
.content {
  text-align: center;
  .avatar {
    display: inline-block;
    width: 2.3rem;
    height: 2.3rem;
    border-radius: 50%;
  }
  .user-name {
    color: black;
    font-size: 0.2rem;
    font-weight: bold;
    margin-top: 0.2rem;
  }
  .tip-text {
    width: 5.5rem;
    margin: 0 auto;
    margin-top: 0.4rem;
    color: #fff;
    font-size: 0.34rem;
    font-weight: bolder;
  }
  .shop-name {
    color: #fff;
    font-size: 0.2rem;
    font-weight: bold;
    margin-top: 0.2rem;
  }
  .btn {
    height: 0.66rem;
    margin: 0.4rem auto 0;
  }
}
</style>
