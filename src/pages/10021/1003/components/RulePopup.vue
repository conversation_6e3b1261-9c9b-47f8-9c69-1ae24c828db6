<template>
  <div class="rule-bk">
    <div class="title"></div>
    <div class="content" v-html="rule"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 5.9rem;
  height: 8.55rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/223512/12/28939/34903/64f69302F71478258/05491d0c0435c82e.png) no-repeat;
  background-size: 100%;
  border-radius: 0.2rem 0.2rem 0 0;

  .title {
    position: relative;
    height: 1.14rem;
  }

  .content {
    height: 6.55rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
  }
  .close {
    width: 0.53rem;
    height: 0.53rem;
    margin: 0 auto;
  }
}
</style>
