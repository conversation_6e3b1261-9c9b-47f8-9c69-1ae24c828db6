<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <div class="kv"></div>
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <img class="header-btn" :src="furnish.ruleBg" alt="" @click="showRule = true" />
          <img class="header-btn" :src="furnish.prizeBg" alt="" @click="showMyPrize = true" />
          <!-- <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRule = true"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showMyPrize = true"><div>我的奖品</div></div> -->
        </div>
      </div>
    </div>
    <div class="select-hover" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)">
      <div class="wheel">
        <lucky-wheel ref="myLucky" width="86vw" height="86vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
        <!-- <img :src="wheelImg" alt="" class="wheel-img" /> -->
      </div>
      <div class="draws-num" :style="furnishStyles.drawsNum.value">当前还有 1次 抽奖机会</div>
      <div class="draw-btn" v-if="isShowTask === 1">
        <img :src="furnish.drawBtn ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/223338/24/2898/19533/61946f11E40826e26/a2e11c29a67b4717.png'" alt="" @click="showTask = true" />
      </div>
    </div>
    <div v-if="furnish.showWinnersBg === 1" class="winners select-hover" :style="furnishStyles.winnersBg.value" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
      <div class="winners-content">
        <div class="winner-list swiper-container" ref="swiperRef">
          <div class="swiper-wrapper" v-if="activityGiftRecords?.length != 0">
            <div class="winner swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
              <div>
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" v-if="!item.userImg" />
                <img v-else :src="item.userImg" alt="" />
                <span>{{ item.nickName }}</span>
              </div>
              <span>{{ item.giftName }}</span>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div v-if="!isCreateImg">
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 做任务弹窗 -->
    <VanPopup teleport="body" v-model:show="showTask" position="bottom">
      <DoTask :times="times" :tasks="tasks" @close="showTask = false"></DoTask>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" :chanceNum="chanceNum" @drawAgain="drawAgain" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import DoTask from '../components/DoTask.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import { Task } from '../ts/type';
import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);
const chanceNum = ref(1);
const shopName = ref('xxx旗舰店');

const isLoadingFinish = ref(false);
const isShowTask = ref(1);
const showRule = ref(false);
const ruleTest = ref('');

const showMyPrize = ref(false);

const tasks = reactive([] as Task[]);
const showTask = ref(false);
const times = ref(0);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

const activityGiftRecords = reactive([
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
]);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const myLucky = ref();
const startCallback = async () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = Math.floor(Math.random() * 8);
    const _award = prizeInfo[index];
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index);
  }, 2000);
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};
// 再抽一次
const drawAgain = () => {
  showAward.value = false;
  startCallback();
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showTask.value = false;
  showAward.value = false;
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    if (data.prizeList.length) {
      prizeInfo.splice(0);
      prizeInfo.push(...data.prizeList);
    }
    tasks.splice(0);
    tasks.push(...data.taskList);
    ruleTest.value = data.rules;
    isShowTask.value = data.isShowTask;
    shopName.value = data.shopName;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showMyPrize.value = false;
    showAward.value = false;
    showRule.value = false;
    if (data.isShowTask === 1) {
      showTask.value = data;
    }
  } else if (type === 'shop') {
    shopName.value = data;
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeInfo.splice(0);
    prizeInfo.push(...activityData.prizeList);
    tasks.splice(0);
    tasks.push(...activityData.taskList);
    ruleTest.value = activityData.rules;
    isShowTask.value = activityData.isShowTask;
    shopName.value = activityData.shopName;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  if (activityGiftRecords.length > 4) {
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: {
          delay: 1000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
        direction: 'vertical',
        loop: true,
        slidesPerView: 5,
        loopedSlides: 8,
        centeredSlides: true,
      });
    });
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  .kv {
    height: 4.78rem;
  }

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    height: 0.7rem;
    margin-bottom: 0.1rem;
  }
}

.wheel {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}

.draws-num {
  text-align: center;
  font-size: 0.26rem;
  line-height: 0.54rem;
  margin-top: 1.41rem;
  margin-bottom: 0.5rem;
}

.draw-btn {
  width: 3.5rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 6.5rem;
  margin: 0.49rem auto 0;
  padding-top: 1.3rem;

  .winners-content {
    width: 6.3rem;
    height: 4.7rem;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem 0;
  border-bottom: 1px dashed rgb(230, 230, 230);

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
