<template>
  <div class='confirm-box'>
    <div class='info-box'>
      确定要花费<span class='num-box'>{{ exchangePoint }}</span
    >积分兑换吗
    </div>
    <div class='btn-box'>
      <div class='cancel-btn' @click="close">取消</div>
      <div class='exchange-btn' @click.stop="clickExchangeConfirm">立即兑换</div>
    </div>
  </div>
  <div class='close-btn' @click="close" />
</template>
<script lang="ts" setup>
import { PropType, ref } from 'vue';

const props = defineProps({
  exchangePoint: {
    type: Number,
    default: '',
  },
});
const emits = defineEmits(['close', 'clickExchangeConfirm']);
const close = () => {
  emits('close');
};
const showExchangeTips = ref(false);
const exchangeTips = ref('活动太火爆,请稍后重试！');
const btnName = ref('知道了');
const currentState = ref('');

const isExchanging = false;

const clickExchangeConfirm = () => {
  emits('clickExchangeConfirm');
};
</script>
<style scoped lang="scss">
.confirm-box {
  padding: 0rem 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 5rem;
  height: 3.5rem;
  background-color: #fff;
  border-radius: 0.2rem;
  .info-box {
    margin-bottom: 0.86rem;
    font-family: PingFang-SC-Regular;
    font-size: 0.36rem;
    color: #333333;
    text-align: center;
  }
  .num-box {
    margin: 0rem 0.05rem;
    font-family: PingFang-SC-Regular;
    font-size: 0.36rem;
    color: #ff3333;
  }
  .btn-box {
    position: absolute;
    left: 0;
    top: 2.65rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 5rem;
    height: 0.86rem;
    cursor: pointer;
    .cancel-btn {
      width: 2.5rem;
      height: 0.86rem;
      line-height: 0.86rem;
      text-align: center;
      font-family: PingFang-SC-Regular;
      font-size: 0.3rem;
      color: #333333;
      border-radius: 0rem 0rem 0rem 0.2rem;
    }
    .exchange-btn {
      width: 2.5rem;
      height: 0.86rem;
      line-height: 0.86rem;
      text-align: center;
      font-family: PingFang-SC-Regular;
      font-size: 0.3rem;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      color: #fff;
      border-radius: 0rem 0rem 0.2rem 0rem;
    }
  }
}
.close-btn {
  margin: 0.75rem 0rem 0rem 2.23rem;
  width: 0.6rem;
  height: 0.6rem;
  background: {
    image: url('//img10.360buyimg.com/imgzone/jfs/t1/110430/17/39957/1262/6459a770Fd709c84b/d608c15c4f784ed7.png');
    repeat: no-repeat;
    size: contain;
  }
  cursor: pointer;
}
</style>
