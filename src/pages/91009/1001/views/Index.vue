<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="levelDivAll">
      <div class="levelPrizeDivALL">
        <div class="levelPrizeBg" :style="{width: furnish.levelImgList[currentLevelIndex - 1].width / 100 + 'rem', height: furnish.levelImgList[currentLevelIndex - 1].height / 100 + 'rem'}">
          <img :src="furnish.levelImgList[currentLevelIndex - 1].prizeImg ? furnish.levelImgList[currentLevelIndex - 1].prizeImg : levelArr[currentLevelIndex - 1].prizeImg" alt="" />
        </div>
      </div>
      <div class="levelRuleDiv">
        <div class="contentDiv" v-html="levelArr[currentLevelIndex - 1].memberRules"></div>
      </div>
      <div class="bottomBtnDiv" @click.stop="getPrizeClick(levelArr[currentLevelIndex - 1])"></div>
    </div>
  </div>
  <div>
    <!-- 聚合弹窗活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
    <!-- 非会员 -->
    <VanPopup teleport="body" v-model:show="showToJoinVip" position="center" :close-on-click-overlay="false">
      <ToJoinDialog @close="showToJoinVip=false"></ToJoinDialog>
    </VanPopup>
    <!-- 不满足条件-->
    <VanPopup teleport="body" v-model:show="showNotJoin" position="center">
      <NotJoin @close="showNotJoin=false"></NotJoin>
    </VanPopup>
    <!--  领取成功窗-->
    <VanPopup teleport="body" v-model:show="showAward" position="center">
      <Award :prizeData="prizeData" @writeAddress="writeAddress" @close="showAward=false"></Award>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress v-if="showSaveAddress" :itemData="selectItemData" :activityPrizeId="activityPrizeId" :userPrizeId="userPrizeId" @close="addressClose"></SaveAddress>
    </VanPopup>
    <!-- 选择生日-->
    <VanPopup teleport="body" v-model:show="showBirthDayPop" position="center">
      <BirthDay :itemData="selectItemData" @close="closeBirthDayPop"></BirthDay>
    </VanPopup>
    <!-- 去完善信息 -->
    <VanPopup teleport="body" v-model:show="toImproveInformationPopup" position="center">
      <ToImproveInformation :link="furnish.actLink" @close="toImproveInformationPopup = false"></ToImproveInformation>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue';
import furnishStyles, { furnish, taskRequestInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import SaveAddress from '../components/SaveAddress.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import BirthDay from '../components/birthdayPop.vue';
import ToImproveInformation from '../components/ToImproveInformation.vue';
import useThreshold from '@/hooks/useThreshold';
import { gotoSkuPage } from '@/utils/platforms/jump';
import '../Threshold2/CPBStyle.scss';
import Threshold2 from '../Threshold2/ThresholdCPB.vue';
import ToJoinDialog from '../components/ToJoinDialog.vue';
import NotJoin from '../components/NotJoin.vue';
import Award from '../components/Award.vue';

const showAward = ref(false);
const showNotJoin = ref(false);
const showToJoinVip = ref(false);
const levelArr = reactive([
  {
    name: '丹粉',
    value: 1,
    prizeType: 11,
    memberRules: '',
  },
  {
    name: '普卡会员',
    value: 2,
    prizeType: 11,
    memberRules: '',
  },
  {
    name: '银卡会员',
    value: 3,
    prizeType: 11,
    memberRules: '',
  },
  {
    name: '金卡会员',
    value: 4,
    prizeType: 3,
    memberRules: '',
  },
  {
    name: '黑钻卡',
    value: 5,
    prizeType: 3,
    memberRules: '',
  },
]);
// 当前选择对的会员等级，默认为等级1
const currentLevelIndex = ref(1);
const showBirthDayPop = ref(false);
const toImproveInformationPopup = ref(false);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

// 保存实物地址相关
const userPrizeId = ref('');
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const selectItemData = ref(null);

// 填写地址
const writeAddress = () => {
  showSaveAddress.value = true;
};
const addressClose = () => {
  showSaveAddress.value = false;
  showAward.value = false;
};
// 展示门槛显示弹框
const showLimit = ref(false);
const openCardArr = baseInfo.thresholdResponseList.filter((item) => item.type === 1);
if (openCardArr.length > 0) {
  showToJoinVip.value = true;
} else {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList.filter((item) => item.type !== 1),
    className: 'common-message-cpb',
  });
}
const prizeId = ref(''); // 奖品id
const prizeData = ref(null);
// 获取页面数据
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/91009/activityInfo');
    console.log(data, '主接口数据');
    prizeId.value = data.prizeId;
    currentLevelIndex.value = data.memberLevel;
    levelArr.forEach((item, index) => {
      item.memberRules = data.memberRules;
      item.id = data.prizeId;
      item.skuId = data.skuId;
      item.prizeImage = data.prizeImg;
    });
    console.log(levelArr, 'levelArr====');
  } catch (error) {
    console.error();
  }
};
// 手机生日信息 调用领取奖品接口，根据接口返回字段判断接下来的操作
const getPrizeClick = async (itemData: any) => {
  console.log(itemData, '领取奖品');
  const openCardArr = baseInfo.thresholdResponseList.filter((item) => item.type === 1);
  if (openCardArr.length > 0) {
    showToJoinVip.value = true;
    return;
  }
  const openCardArr1 = baseInfo.thresholdResponseList.filter((item) => item.type !== 1);
  if (baseInfo.thresholdResponseList.length > 0 && openCardArr1.length > 0) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList.filter((item) => item.type !== 1),
      className: 'common-message-cpb',
    });
    // showLimit.value = true;
    return;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  selectItemData.value = itemData;
  try {
    const { data } = await httpRequest.post('/91009/sendPrize', {
      activityPrizeId: prizeId.value,
    });
    closeToast();
    // console.log(data, '调用领取奖品接口');
    if (data.status === 0) {
      // showToast('申请成功');
      if (data.prizeType === 3) {
        // 手机生日信息成功 关闭弹窗 铂钻卡和黑金卡显示填写地址弹窗
        prizeData.value = data;
        showAward.value = true;
      } else {
        showToast({
          message: '申请成功',
          duration: 1500,
          onClose: (() => {
            if (itemData.skuId) {
              // 令牌已经领取过toast提示后跳转商详页面，不管令牌是否已经使用
              gotoSkuPage(itemData.skuId);
            }
          }),
        });
      }
    } else {
      showToast('领取失败');
    }
  } catch (errMag) {
    console.log(errMag, '调用领取奖品接口错误信息');
    if (errMag.message === '未填写生日信息') {
      closeToast();
      if (furnish.actLink) {
        toImproveInformationPopup.value = true;
      } else {
        showBirthDayPop.value = true;
      }
    } else if (errMag.message === '已领取,未填写地址') {
      closeToast();
      showSaveAddress.value = true;
      activityPrizeId.value = '';
      userPrizeId.value = '';
    } else if (errMag.message === '您已领取过生日礼') {
      showToast({
        message: errMag.message,
        duration: 1500,
        onClose: (() => {
          if (itemData.skuId) {
            // 令牌已经领取过toast提示后跳转商详页面，不管令牌是否已经使用
            gotoSkuPage(itemData.skuId);
          }
        }),
      });
    } else {
      showToast(errMag.message);
    }
  }
};

// 关闭填写生日弹窗
const closeBirthDayPop = async (data: boolean, itemData: any) => {
  console.log(data, '关闭手机生日信息弹窗');
  if (data) {
    // 填写生日信息成功后领取继续领取奖品
    await getPrizeClick(itemData);
  }
  showBirthDayPop.value = false;
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityInfo()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 2.1rem;
  .levelPrizeDivALL {
    margin-top: 0.46rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .levelPrizeBg {
      background-size: 100% 100%;
      background-repeat: no-repeat;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .levelRuleDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/282913/31/25543/4775/680a1bd9Fa611fdbc/068247c1bafa8dcd.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.77rem;
    height: 3.44rem;
    margin-left: calc(50% - 6.77rem / 2);
    padding: 0.5rem 0.2rem;
    color: #876634;
    font-size: 0.24rem;
    margin-top: 0.58rem;
    .contentDiv {
      height: 2.06rem;
      overflow-y: scroll;
      white-space: pre-wrap;
    }
  }
  .bottomBtnDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/237424/9/37078/11185/680a1f1fF34b16d24/80ba8342629f89e6.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 2.80rem;
    height: 0.55rem;
    margin-left: calc(50% - 2.80rem / 2);
    margin-top: 0.24rem;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
