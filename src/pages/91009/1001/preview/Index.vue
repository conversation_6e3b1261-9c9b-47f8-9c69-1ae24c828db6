<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="levelDivAll">
      <div class="levelPrizeDivALL">
        <div class="levelPrizeBg" :style="{width: furnish.levelImgList[currentLevelIndex - 1].width / 100 + 'rem', height: furnish.levelImgList[currentLevelIndex - 1].height / 100 + 'rem'}">
          <img :src="furnish.levelImgList[currentLevelIndex - 1].prizeImg ? furnish.levelImgList[currentLevelIndex - 1].prizeImg : levelArr[currentLevelIndex - 1].prizeImg" alt="" />
        </div>
      </div>
      <div class="levelRuleDiv">
        <div class="contentDiv" v-html="levelArr[currentLevelIndex - 1].memberRules"></div>
      </div>
      <div class="bottomBtnDiv" @click.stop="toast()"></div>
    </div>
  </div>
  <div v-if="!isCreateImg">
    <VanPopup teleport="body" v-model:show="showBirthDayPop" position="center">
      <BirthDay :itemData="null" @close="closeBirthDayPop"></BirthDay>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish, taskRequestInfo } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';
import BirthDay from '../components/birthdayPop.vue';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';

const showBirthDayPop = ref(false);
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const levelArr = reactive([
  {
    name: '丹粉',
    value: 1,
    prizeType: 11,
    memberRules: '',
    prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/271886/22/25510/34940/680a0495F4047eedb/6d6ad801939316dc.png',
  },
  {
    name: '普卡会员',
    value: 2,
    prizeType: 11,
    memberRules: '',
    prizeImg: '',
  },
  {
    name: '银卡会员',
    value: 3,
    prizeType: 11,
    memberRules: '',
    prizeImg: '',
  },
  {
    name: '金卡会员',
    value: 4,
    prizeType: 3,
    memberRules: '',
    prizeImg: '',
  },
  {
    name: '黑钻卡',
    value: 5,
    prizeType: 3,
    memberRules: '',
    prizeImg: '',
  },
]);
const isLoadingFinish = ref(false);
const closeOnClickOverlay = ref(false);

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
// 当前选择对的会员等级，默认为等级1
const currentLevelIndex = ref(1);
// 选择等级
const selectLevel = (itemData: any, index: any) => {
  console.log(itemData, index);
  currentLevelIndex.value = Number(itemData.value);
};
// 手机生日信息
const birthDay = () => {
  console.log('手机生日信息');
  showBirthDayPop.value = true;
};
// 关闭手机生日信息弹窗
const closeBirthDayPop = (data: boolean) => {
  console.log(data, '关闭手机生日信息弹窗');
  showBirthDayPop.value = false;
  if (data) {
    // 手机生日信息成功 关闭弹窗 铂钻卡和黑金卡显示填写地址弹窗
    showSaveAddress.value = true;
  }
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const toast = () => {
  showToast('活动预览，仅供查看');
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);

};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  // console.log(res.data, '装修实时数据修改');
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    if (data.memberLevelRequest && data.memberLevelRequest.length > 0) {
      const { memberLevelRequest } = data;
      memberLevelRequest.forEach((itemDeco: any, index1: number) => {
        levelArr[index1].name = itemDeco.gradeName;
        levelArr[index1].value = itemDeco.gradeLevel;
        levelArr[index1].prizeType = itemDeco.prizeType;
        levelArr[index1].prizeImg = itemDeco.prizeRequest.prizeImg;
        levelArr[index1].memberRules = itemDeco.memberRules;
      });
    }
    // console.log(levelArr, 'levelArr=========');
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  }
};

// 设置活动后的预览
onMounted(() => {
  console.log('onMounted装修实时数据修改');
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    if (activityData.memberLevelRequest && activityData.memberLevelRequest.length > 0) {
      const { memberLevelRequest } = activityData;
      memberLevelRequest.forEach((itemDeco: any, index1: number) => {
        levelArr[index1].name = itemDeco.gradeName;
        levelArr[index1].value = itemDeco.gradeLevel;
        levelArr[index1].prizeType = itemDeco.prizeType;
        levelArr[index1].prizeImg = itemDeco.prizeRequest.prizeImg;
        levelArr[index1].memberRules = itemDeco.memberRules;
      });
    }
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 2.1rem;
  .levelPrizeDivALL {
    margin-top: 0.46rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .levelPrizeBg {
      background-size: 100% 100%;
      background-repeat: no-repeat;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .levelRuleDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/282913/31/25543/4775/680a1bd9Fa611fdbc/068247c1bafa8dcd.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 6.77rem;
    height: 3.44rem;
    margin-left: calc(50% - 6.77rem / 2);
    padding: 0.5rem 0.2rem;
    color: #876634;
    font-size: 0.24rem;
    margin-top: 0.58rem;
    .contentDiv {
      height: 2.06rem;
      overflow-y: scroll;
      white-space: pre-wrap;
    }
  }
  .bottomBtnDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/237424/9/37078/11185/680a1f1fF34b16d24/80ba8342629f89e6.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 2.80rem;
    height: 0.55rem;
    margin-left: calc(50% - 2.80rem / 2);
    margin-top: 0.24rem;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
