<template>
  <div class="birthDayBg">
    <div class="btn"></div>
    <!--    <div class="closeDiv" @click="close"></div>-->
  </div>
</template>

<script lang="ts" setup>
// const props = defineProps({
//   link: {
//     type: String,
//     required: true,
//   },
// });
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

// const toLink = () => {
//   window.location.href = props.link;
// };
</script>

<style scoped lang="scss">
.birthDayBg {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/229933/29/35508/16775/680af744F5802ae77/7c35d3f1160b0045.png') no-repeat;
  background-size: 100%;
  //width: 100vw;
  width: 5.89rem;
  height: 3.84rem;
  padding-top: 2.1rem;
  .btn {
    width: 4rem;
    height: 0.6rem;
    margin: 0 auto;
    background: red;
  }
  .closeDiv {
    position: absolute;
    width: 0.46rem;
    height: 0.46rem;
    bottom: 0rem;
    left: calc(50% - 0.23rem);
  }
}
</style>
