<template>
  <div class="birthDayBg">
    <div class="btn" @click="toLink"></div>
<!--    <div class="closeDiv" @click="close"></div>-->
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  link: {
    type: String,
    required: true,
  },
});
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const toLink = () => {
  window.location.href = props.link;
};
</script>

<style scoped lang="scss">
.birthDayBg {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/284684/18/26411/16147/680af402F15e4fe3d/d576d8aa2a533f8d.png') no-repeat;
  background-size: 100%;
  //width: 100vw;
  width: 5.89rem;
  height: 3.84rem;
  padding-top: 2.1rem;
  .btn {
    width: 2.8rem;
    height: 0.6rem;
    margin: 0 auto;
    //background: red;
  }
  .closeDiv {
    position: absolute;
    width: 0.46rem;
    height: 0.46rem;
    bottom: 0rem;
    left: calc(50% - 0.23rem);
  }
}
</style>
