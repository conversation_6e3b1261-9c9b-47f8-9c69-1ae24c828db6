<template>
  <div class="birthDayBg">
    <div class="birthDayDiv">
      <div class="birthDayDiv1">
        <div>生日：</div>
        <div @click="selectBirthDay()">{{ birthday ? birthday : '请选择生日>' }}</div>
      </div>
      <div class="tip">注：填写后不可修改</div>
      <div class="submitDiv" @click="submitBirthDay()"></div>
    </div>
    <div class="closeDiv" @click="close"></div>
  </div>

  <VanPopup teleport="body" v-model:show="birthDaySelects" position="bottom">
    <van-date-picker v-model="currentDate" title="选择日期" :min-date="minDate" :max-date="maxDate" @confirm="confirmPicker" @cancel="cancelPicker" />
  </VanPopup>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const emits = defineEmits(['close']);
const props = defineProps({
  itemData: {
    type: Object,
    required: true,
  },
});
const birthDaySelects = ref(false);
const currentDate = ref([dayjs().year(), dayjs().month() + 1, dayjs().date()]);
const birthday = ref('');
const minDate = ref(new Date(1924, 1, 1));
const maxDate = ref(new Date(dayjs().format('YYYY-MM-DD')));
const selectBirthDay = () => {
  birthDaySelects.value = true;
};
const close = () => {
  emits('close', false, null);
};

// 提交生日信息
const submitBirthDay = async () => {
  console.log('提交生日信息');
  if (birthday.value) {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    try {
      const res = await httpRequest.post('/90001/addInfo', {
        birthday: birthday.value,
      });
      if (res.code === 200) {
        closeToast();
        emits('close', true, props.itemData);
      } else {
        showToast(res.message);
      }
    } catch (errMag) {
      showToast(errMag.message);
    }
  } else {
    showToast('请选择生日');
  }
};
const confirmPicker = (dateData: any) => {
  currentDate.value = dateData.selectedValues;
  birthday.value = `${currentDate.value[0].toString()}年${currentDate.value[1].toString()}月${currentDate.value[2].toString()}日`;
  console.log(birthday.value);
  birthDaySelects.value = false;
};
const cancelPicker = () => {
  birthDaySelects.value = false;
};
</script>
<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.birthDayBg {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/227617/32/11289/68446/65960d8dFf3ed771f/e45599769439b3ad.png) no-repeat;
  background-size: 100%;
  //width: 100vw;
  width: 5.54rem;
  height: 4rem;
  padding-top: 1.55rem;
  .birthDayDiv {
    width: 100%;
    .birthDayDiv1 {
      border: 2px solid #916c4a;
      height: 0.53rem;
      margin: 0 0.22rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 0.23rem;
      color: #a78c64;
    }
    .submitDiv {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/225679/17/11618/5142/65960d8eFa9360b95/813679b04113ebd6.png) no-repeat;
      background-size: 100%;
      width: 2.4rem;
      height: 0.63rem;
      margin-left: calc(50% - 2.4rem / 2);
      margin-top: 0.05rem;
    }
  }
  .tip {
    margin-top: 0.05rem;
    font-size: 0.18rem;
    color: #916c4a;
    text-align: center;
  }
  .closeDiv {
    position: absolute;
    width: 0.46rem;
    height: 0.46rem;
    bottom: 0;
    left: calc(50% - 0.46rem / 2);
  }
}
</style>
