<template>
  <div class="birthDayBg">
    <div class="prizeImgDiv">
      <img :src="prizeData.prizeImg" alt="" />
    </div>
    <div class="prizeName">{{prizeData.prizeName}}</div>
    <div class="textDiv">
<!--      <div class="text" v-if="prizeData.prizeType === 3">领奖成功，请在1小时内填写地址，奖品将在X个工作日内发放，会员积分将在48小时内发放到账</div>-->
    </div>
    <div class="btnDiv">
      <div class="btn" v-if="prizeData.prizeType === 3" @click="addressClick()"></div>
      <div class="btn11" v-else-if="prizeData.prizeType === 11 && prizeData.skuId" @click="goBugClick()"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { gotoSkuPage } from '@/utils/platforms/jump';

const props = defineProps({
  prizeData: {
    type: Object,
    required: true,
  },
});
// console.log(props.prizeData, 'prizeData========');
const emits = defineEmits(['close', 'writeAddress']);

const close = () => {
  emits('close');
};
// 填写地址
const addressClick = () => {
  emits('writeAddress');
};
// 立即下单
const goBugClick = async () => {
  console.log('立即下单');
  await gotoSkuPage(props.prizeData.skuId);
};
</script>

<style scoped lang="scss">
.birthDayBg {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/275120/22/27726/9104/680af89bF34f40206/73382a96318a6a84.png') no-repeat;
  background-size: 100%;
  //width: 100vw;
  width: 4.79rem;
  height: 5.90rem;
  padding-top: 1.3rem;
  .btnDiv{
    .btn {
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/281976/13/26453/8157/680af89bFdfddcb9c/4ea08b9d930f7822.png') no-repeat;
      width: 2.03rem;
      height: 0.44rem;
      margin: 0 auto;
      position: absolute;
      bottom: 0.3rem;
      left: calc( 50% - 2.03rem / 2);
      background-size: 100% 100%;
    }
    .btn11{
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/275051/5/27690/7449/680af89cF96e771d2/fb3e44c776103530.png') no-repeat;
      width: 2.03rem;
      height: 0.44rem;
      margin: 0 auto;
      position: absolute;
      bottom: 0.3rem;
      left: calc( 50% - 2.03rem / 2);
      background-size: 100% 100%;
    }
  }
  .prizeImgDiv{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2rem;
    img {
       height: 100%;
    }
  }
  .prizeName{
    text-align: center;
    color: #000;
    font-size: 0.24rem;
    margin: 0.2rem;
  }
  .textDiv{
    text-align: center;
    color: #000;
    font-size: 0.18rem;
    margin: 0.2rem;
  }
  //.closeDiv {
  //  position: absolute;
  //  width: 0.46rem;
  //  height: 0.46rem;
  //  bottom: 0rem;
  //  left: calc(50% - 0.23rem);
  //}
}
</style>
