<template>
  <div v-if="isJoin" class="home pageBg" :style="furnishStyles.pageBg.value" >
    <div class="head-btn">
      <!-- 活动规则按钮 -->
      <div class="handle-side-btn" v-click-track="'hdgz'" @click="getRule()"></div>
      <!-- 我的奖品按钮 -->
      <div class="handle-side-btn" v-click-track="'wdjp'" style="top: 6.5rem" @click="showMyPrize = true"></div>
    </div>
    <!-- 已报名 -->
    <div v-if="isSignUp === 1" class="phone-info-view">
      <div class="signUp-view-all">
        <div class="signUp-view"></div>
      </div>
    </div>
    <!-- 未报名手机号验证码信息收集 -->
    <div class="phone-info-view" v-else>
      <div class="phone-input">
        <input v-model="mobile" type="tel" maxlength="11" placeholder="请输入您的手机号码">
      </div>
      <div class="phone-input">
        <input v-model="verificationCode" type="tel" maxlength="6" placeholder="请输入短信验证码">
        <div class="send-verification-btn" :class="{gray:false}" @click="getVerificationCode()">
          {{buttonText}}
        </div>
      </div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/271640/17/24660/7058/68070c3fFa9b5c491/1962f5648dae25e5.png"
           @click="commitCheck()" class="commit-check-btn" alt="">
    </div>
    <!-- 奖品规格标题-->
    <div class="prize-info-view">
      <div class="prize-info-title"></div>
      <div class="prize-info-text">首次购买(规格1{{prizeList.length === 2 ? '/规格2' : ''}})试用产品一听，在活动期间内复购同系列、指定罐数正装，可获得【对应规格】的【对应奖品】</div>
      <div class="prize-list-view">
        <div class="prize-item-view" v-for="(item,index) in prizeList" :key="index">
          <div class="prize-index">规格{{index+1}}</div>
          <div class="prize-content-view">
            <div class="prize-left-view">
              <img :src="item.prizeImg" alt="" />
            </div>
            <div class="prize-right-view">
              <div class="prize-name-view">{{item.prizeName}}</div>
              <div class="prize-rest-view">剩余库存：{{item.sendRaminCount}}份</div>
              <!--  status 奖品状态 1 已领取 2 奖品已发光 3 立即领取 4 立即领取置灰-->
              <div class="prize-btn-view">
                <div class="prize-can-btn" v-if="item.status === 1" :style="{'backgroundImage': 'url(//img10.360buyimg.com/imgzone/jfs/t1/271317/35/24555/6432/68072aafF5559e7c1/b71a869c8f67fb6d.png)'}">已领取</div>
                <div class="prize-can-btn" v-else-if="item.status === 2" :style="{'backgroundImage': 'url(//img10.360buyimg.com/imgzone/jfs/t1/282874/9/23101/6955/68070c40F691674bd/a985f729445e921b.png)'}">奖品已发光</div>
                <div class="prize-can-btn" @click="getPrizeClick(item)" v-else-if="item.status === 3" :style="{'backgroundImage': 'url(//img10.360buyimg.com/imgzone/jfs/t1/284303/34/24844/9618/68070c3cFd192aa18/70890df993c39148.png)'}">立即领取高亮</div>
                <div class="prize-can-btn" v-else>立即领取置灰</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="prize-remark-view">注：<br> 1、用户需先报名，后续下单才可计入统计；<br> 2、首购试用装和复购正装需在本页面显示的产品中进行下单；需是相同系列奖品，且订单皆确认收货后才有资格领取奖品；<br> 3、两档规格类奖品仅可选择其中一个奖品进行领取。</div>
    </div>
    <!-- 系列商品-->
    <div class="sku-info-view">
      <!--      :style="seriesPrizeList.length >= 3 ? {} : { justifyContent: 'space-around' }"-->
      <div class="series-tab-view-all" :style="seriesSkuList.length >= 3 ? {} : { justifyContent: 'space-around' }">
        <div class="series-tab-view" v-for='(item,index) in seriesSkuList' :key='index'>
          <div class="series-tab1">
            <div :class="[currentSeriesTab === index ? 'series-active-tab' : 'series-noActive-tab']" @click='seriesTabClick(item,index)'>{{item.seriesName ? item.seriesName : '系列1'}}</div>
          </div>
        </div>
      </div>
      <div class="step1-sku-view-all">
        <div class="step1-view">
          <div class="step1-title"></div>
        </div>
        <div class="specification-tab-view" v-if="prizeList.length === 2">
          <div class="specification-tab" v-for='(item1,index1) in 2' :key='index1'>
            <div :class="[currentSpecificationTab === index1 ? 'specification-active-tab' : 'specification-noActive-tab']" @click='specificationTabClick(item1,index1)'>规格{{index1 + 1}}</div>
          </div>
        </div>
        <div class="specification-oneTab-view" v-if="prizeList.length === 1">规格1</div>
        <div class="sku-view-all">

          <div class="sku-view" v-if="currentSpecificationTab === 0 && seriesSkuListData.skuList1 && seriesSkuListData.skuList1.length > 0">
            <div class="sku-item-view" v-for="(item3,index3) in seriesSkuListData.skuList1" :key="index3">
              <div class="skuPictureDiv" @click="gotoSkuPage(item3.skuId)">
                <img :src='item3.skuImg' alt=''>
              </div>
            </div>
          </div>

          <div class="sku-view" v-if="currentSpecificationTab === 1 && seriesSkuListData.skuList2 && seriesSkuListData.skuList2.length > 0">
            <div class="sku-item-view" v-for="(item3,index3) in seriesSkuListData.skuList2" :key="index3">
              <div class="skuPictureDiv" @click="gotoSkuPage(item3.skuId)">
                <img :src='item3.skuImg' alt=''>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="step2-sku-view-all">
        <div class="step2-view">
          <div class="step2-title"></div>
        </div>
        <div class="sku-view-all">
          <div class="sku-view">
            <div class="sku-item-view" v-for="(item3,index3) in seriesSkuListData.formalSkuList" :key="index3">
              <div class="skuPictureDiv" @click="gotoSkuPage(item3.skuId)">
                <img :src='item3.skuImg' alt=''>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <div class="result">
      <img class="logo" src="../assets/img/shop-icon.png" alt="">

      <img class="result-title" src="../assets/img/sorry-title.png" alt="">

      <img class="result-banner" :src="furnish.canNotJoinKv ? furnish.canNotJoinKv : '//img10.360buyimg.com/imgzone/jfs/t1/276006/24/25084/85861/6807498aFcd7d62c1/59deddb9b71ff7f8.png'" alt="">

      <img class="result-btn" src="../assets/img/joinOtherBtn.png" @click="handleResult()" alt="">
      <!--    <img class="bottom" src="../assets/img/bottom-text.png" alt="">-->
    </div>
  </div>
  <div>
    <VanPopup teleport="body" v-model:show="showOpenCard" :close-on-click-overlay="false">
      <ToJoinDialog @closeDialog="showOpenCard = false"/>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showRule" :close-on-click-overlay="false">
      <RuleDialog @closeDialog="showRule = false" :rule="rule"/>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" :close-on-click-overlay="false">
      <MyRecordDialog @closeDialog="showMyPrize = false"/>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showSureDraw" :close-on-click-overlay="false">
      <SureDrawDialog :isHc="mutualExclusion" @closeDialog='showSureDraw= false' @sureDraw='sureDraw' />
    </VanPopup>
  </div>
</template>

<script lang='ts' setup>
import { ref, inject, onUnmounted, computed, watch, watchEffect } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import ToJoinDialog from '../components/ToJoinDialog.vue';
import SureDrawDialog from '../components/SureDrawDialog.vue';

/* ---------------------------------  弹窗  ------------------------------ */
import RuleDialog from '../components/RuleDialog.vue';
import MyRecordDialog from '../components/MyRecordDialog.vue';

/* ---------------------------------  接口  ------------------------------- */
import { DecoData } from '@/types/DecoData';
import dayjs from 'dayjs';
import { httpRequest } from '@/utils/service';
import { defaultSeriesList, defaultStateList } from '../ts/default';
import { gotoSkuPage } from '@/utils/platforms/jump';

const isJoin = ref(true);
const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const prizeList = ref([]);
const currentSeriesTab = ref(0); // 默认选中系列下标
const currentSpecificationTab = ref(0); // 默认规格tab下标
const isSignUp = ref(false); // 是否报名 true  已报名 false 未报名
const seriesSkuList = ref([]);
const seriesSkuListData = ref([]);

const showSureDraw = ref(false);
const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};

const checkActTime = () => {
  if (!isStart.value && !isEnd.value) {
    showToast('活动未开始');
    return false;
  }
  if (isEnd.value) {
    showToast('活动已结束');
    return false;
  }
  return true;
};

/* -------------------------------------------------------------------------- */
const showOpenCard = ref(false);
const showRule = ref(false);
const rule = ref('');
const showMyPrize = ref(false);

const getRule = async () => {
  try {
    if (!rule.value) {
      const { data } = await httpRequest.get('/common/getRule');
      rule.value = data;
      closeToast();
    }
    showRule.value = true;
  } catch (error) {
    closeToast();
  }
};

// 定时器对象
const timer = ref();
const countdown = ref(0);
// 计算属性：是否正在倒计时
const isCounting = computed(() => countdown.value > 0);
// 计算属性：按钮文字
const buttonText = computed(() => (isCounting.value ? `重新获取(${countdown.value}s)` : '获取验证码'));

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  timer.value = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(timer.value);
    }
  }, 1000);
};

// 电话号码
const mobile = ref();
// 监听 mobile 的变化
watch(mobile, (newVal) => {
  // 过滤非数字字符
  mobile.value = newVal.replace(/\D/g, '');
});

// 验证码
const verificationCode = ref();
// 监听 verificationCode 的变化
watch(verificationCode, (newVal) => {
  // 过滤非数字字符
  verificationCode.value = newVal.replace(/\D/g, '');
});

// 发送验证码请求
const getVerificationCode = async () => {
  if (!checkActTime()) {
    return;
  }
  // 校验电话号码
  console.log('校验号码');
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!mobile.value) {
    showToast('请输入您的手机号码');
    return;
  } if (!checkPhone.test(mobile.value)) {
    showToast('请输入正确的手机号码');
    return;
  }
  // 检查是否正在倒计时
  if (isCounting.value) return;
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    // console.log(mobile.value, 'czczxc========');
    const res = await httpRequest.post('/90021/sendSms', { mobile: mobile.value });
    closeToast();
    if (res.data) {
      // 开始倒计时
      startCountdown();
    } else {
      showToast('发送失败');
    }
  } catch (e) {
    showToast(e.message);
  }
};

const getSeriesSkuData = async (seriesId) => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const res = await httpRequest.post('/90021/series', {
      seriesId,
    });
    console.log(res.data, '系列商品数据');
    res.data.specsResponses.forEach((item) => {
      if (item.specs === 1) {
        res.data.skuList1 = item.trialSkuList;
      }
      if (item.specs === 2) {
        res.data.skuList2 = item.trialSkuList;
      }
    });
    seriesSkuListData.value = res.data;
    console.log(seriesSkuListData.value, '系列商品数据11');
    closeToast();
  } catch (e) {
    showToast(e.message);
  }
};
const mutualExclusion = ref(true); // 是否与新客2.0互斥
const selectPrizeItemData = ref(null); // 选择领取的奖品的数据
// 主接口
const activityContent = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90021/getActivityInfo');
    console.log(data, '主接口数据');
    isSignUp.value = data.status; // 0 未报名 1 已报名
    mutualExclusion.value = data.mutualExclusion;
    prizeList.value = data.prizeInfoList;
    seriesSkuList.value = data.seriesListResponses;
    // closeToast();
    await getSeriesSkuData(data.seriesListResponses[0].seriesId);
  } catch (e) {
    showToast(e.message);
  }
  // console.log('主接口', res.data);
};
// 立即提交查询资格
const commitCheck = async () => {
  // router.replace({ path: '/uploadImage' });
  if (!checkActTime()) {
    return;
  }
  // 未输入手机号
  if (!mobile.value) {
    showToast('请输入您的手机号码');
    return;
  }
  // 未输入验证码
  if (!verificationCode.value) {
    showToast('请输入短信验证码');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90021/checkSmsCode', { mobile: mobile.value, smsCode: verificationCode.value });
    closeToast();
    showSureDraw.value = true;
  } catch (error) {
    showToast(error.message);
  }
};
// 报名
const signUp = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90021/getUserQualification', { mobile: mobile.value, smsCode: verificationCode.value });
    closeToast();
    console.log('立即提交查询资格', data);
    // 满足领取条件按 userIdentity 1 满足资格 2 不满足
    showSureDraw.value = false;
    if (data.userIdentity === 2) {
      isJoin.value = false;
    } else {
      isJoin.value = true;
      await activityContent();
    }
  } catch (error) {
    showSureDraw.value = false;
    if (error.message === '手机号验证码未通过') {
      showToast(error.message);
    } else {
      isJoin.value = false;
      closeToast();
    }
  }
};
// 点击系列tab
const seriesTabClick = async (itemData, tabIndex) => {
  if (currentSeriesTab.value === tabIndex) {
    return;
  }
  currentSeriesTab.value = tabIndex;
  currentSpecificationTab.value = 0;
  await getSeriesSkuData(itemData.seriesId);
};
// 点击规格tab
const specificationTabClick = (itemData, tabIndex) => {
  currentSpecificationTab.value = tabIndex;
};
// 领取奖品
const getPrizeClick = async (itemData) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90021/sendPrize', {
      prizeId: itemData.prizeId,
    });
    console.log(data, '领取奖品接口数据');
    showToast({
      message: '领奖成功',
      duration: 1500,
      onClose: (() => {
        showSureDraw.value = false;
        activityContent();
      }),
    });
  } catch (e) {
    console.log('领取奖品接口数据');
    showToast({
      message: e.message,
      duration: 1500,
      onClose: (() => {
        showSureDraw.value = false;
        activityContent();
      }),
    });
  }
};

// 是否互斥弹窗确认的回调
const sureDraw = async () => {
  await signUp();
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  // 检查是否开卡
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  getTime();
  await Promise.all([activityContent()]);
  checkActTime();
};

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer.value) clearInterval(timer.value);
});

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
const handleResult = () => {
  window.location.href = furnish.jumpLink;
};
init();
</script>

<style>
*::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.pageBg {
  background-size: 100%;
  min-height:100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
  position: relative
}
.header-kv{
  padding: 0 0 11rem 0;
  .btnAllClass{
    margin-top: 4rem;
    .btnClass{
      width: 0.61rem;
      height: 1.61rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.5rem 0 0 0;
      background-color: rgba(154, 79, 79, 0.41);
    }
  }
}
.home {
  //width: 100%; /* 宽度自适应 */
  //min-height: 44.28rem;
  //padding-top: 11.86rem;
  //position: relative;
  ////background-image: url('../assets/img/home-kv.jpg'), /* 上方图片 */
  ////url('../assets/img/home-bg.jpg'); /* 下方图片 */
  //background-position: top left, bottom left;
  //background-size: contain, 100% 100%; /* 强制等比缩放填满容器 */
  //background-repeat: no-repeat;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-top: 11.86rem;

  .handle-side-btn {
    width: 0.6rem;
    height: 1.8rem;
    position: absolute;
    right: 0;
    top: 4.4rem;
  }

  .phone-info-view {
    width: 7.18rem;
    height: 2.9rem;
    margin: 0rem auto 0;
    padding: .35rem 0;
    text-align: center;
    background: {
      image: url("../assets/img/phone-info-view.png");
      repeat: no-repeat;
      size: contain;
    };

    .phone-input {
      width: 4rem;
      height: .6rem;
      line-height: .56rem;
      padding-left: .15rem;
      margin: 0 auto .2rem;
      border: 1px solid #ce9e26;
      border-radius: .5rem;
      position: relative;

      input {
        width: 3.8rem;
        font-size: .28rem;
        line-height: .56rem;
        color: #434343;
        letter-spacing: .01rem;
        background-color: transparent !important;
        border: transparent !important;
      }

      input::placeholder {
        color: #999; /* 占位文字颜色 */
        font-size: .24rem; /* 字体大小 */
        letter-spacing: -.01rem;
      }

      .send-verification-btn {
        width: 1.73rem;
        height: 0.56rem;
        line-height: 0.56rem;
        text-align: center;
        font-size: 0.22rem;
        color: #a06431;
        position: absolute;
        right: -0.1rem;
        top: 0;
        cursor: pointer;
        background: {
          image: url("//img10.360buyimg.com/imgzone/jfs/t1/284605/13/25083/5889/68070f9cFdbc23916/8bfcca0ec36a8440.png");
          size: contain;
          repeat: no-repeat;
        };
      }
    }

    .commit-check-btn {
      width: 3.04rem;
      margin: .1rem auto 0;
    }
    .signUp-view-all{
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .signUp-view{
        width: 5.13rem;
        height: 1.83rem;
        background: {
          image: url("../assets/img/signUp.png");
          repeat: no-repeat;
          size: 100% 100%;
        };
      }
    }
  }

  .prize-info-view{
    width: 7.18rem;
    border: 0.02rem solid #d0a12d;
    border-radius: 0.36rem;
    background: rgba(255, 255, 255, 0.3);
    margin:0.34rem auto 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 0.4rem;
    .prize-info-title{
      width:3.78rem;
      height:0.49rem;
      //margin-left: calc(50% - 3.74rem / 2);
      background: {
        image: url("../assets/img/prize-info-title.png");
        size: 100% 100%;
        repeat: no-repeat;
      };
    }
    .prize-info-text{
      margin: 0.36rem auto 0;
      width:6rem;
      font-size: 0.24rem;
      color: #606060;
    }
    .prize-list-view{
      margin-top: 0.36rem;
      .prize-item-view{
        position: relative;
        width: 6.80rem;
        height:2.33rem;
        margin-bottom: 0.16rem;
        background: {
          image: url("../assets/img/prize-item-bg.png");
          size: 100% 100%;
          repeat: no-repeat;
        };
        .prize-index{
          width: 1.12rem;
          position: absolute;
          left:0.2rem;
          top: 0.04rem;
          color: #c42728;
          font-size: 0.22rem;
        }
        .prize-content-view{
          display: flex;
          padding: 0.32rem 0.24rem 0 0.5rem;
          .prize-left-view{
            img {
              width: 2rem;
            }
          }
          .prize-right-view{
            margin-left:0.5rem;
            .prize-name-view{
              font-size: 0.28rem;
              color: #3f3f3f;
              span{
                color: #264fbe;
                margin-right: 0.18rem;
              }
            }
            .prize-rest-view{
              font-size: 0.22rem;
              color: #a06431;
              margin-top: 0.2rem;
            }
            .prize-btn-view{
              position: absolute;
              right: 0.4rem;
              bottom: 0.2rem;
              .prize-can-btn{
                font-size: 0;
                width: 1.75rem;
                height: 0.71rem;
                background: {
                  image: url("../assets/img/prize-can-gray-btn.png");
                  size: 100% 100%;
                  repeat: no-repeat;
                };
              }
            }
          }
        }
      }
    }
    .prize-remark-view{
      font-size: 0.18rem;
      color: #878d97;
      width: 6.40rem;

    }
  }
  .sku-info-view{
    width: 7.20rem;
    border: 0.02rem solid #d0a12d;
    border-radius: 0.36rem;
    background: rgba(255, 255, 255, 0.3);
    padding: 0.2rem 0;
    margin: 0.36rem auto;
    .series-tab-view-all{
      display: flex;
      align-items: center;
      overflow-x: scroll;
      width: 6.6rem;
      margin: auto;
      .series-tab-view{
        text-align: center;
        color: #0033be;
        .series-tab1{
          display: flex;
          .series-active-tab{
            color: #a0642e;
            font-size: 0.28rem;
            width:2.2rem;
            height:0.8rem;
            line-height: 0.6rem;
            background: {
              image: url("../assets/img/sku-activie-series-tab.png");
              repeat: no-repeat;
              size: 100% 100%;
            };
          }
          .series-noActive-tab{
            color: #848484;
            font-size: 0.28rem;
            width:2.2rem;
            height:0.8rem;
            line-height: 0.6rem;
            background: {
              image: url("../assets/img/sku-noActivie-series-tab.png");
              repeat: no-repeat;
              size: 100% 100%;
            };
          }
        }
      }
    }
    .step1-sku-view-all{
      .step1-view{
        display: flex;
        justify-content: center;
        .step1-title{
          width: 4.1rem;
          height: 0.57rem;
          background: {
            image: url("../assets/img/sku-try-title.png");
            repeat: no-repeat;
            size: 100% 100%;
          };
        }
      }
      .specification-oneTab-view{
        text-align: center;
        line-height: 0.7rem;
        color: #a0642e;
        font-size: 0.3rem;
        width: 6.77rem;
        height: 0.80rem;
        margin-left: calc(50% - 6.77rem / 2);
        background: {
          image: url("../assets/img/sku-oneSpe-active-tab.png");
          repeat: no-repeat;
          size: 100% 100%;
        };
      }
      .specification-tab-view{
        display: flex;
        width: 6.9rem;
        height: 0.89rem;
        margin-left: calc(50% - 6.9rem / 2);
        background: {
          image: url("../assets/img/sku-spe-noActive-tab.png");
          repeat: no-repeat;
          size: 100% 100%;
        };
        .specification-tab{
          color: #908f8f;
          font-size: 0.3rem;
          .specification-active-tab{
            text-align: center;
            line-height: 0.7rem;
            color: #a0642e;
            font-size: 0.3rem;
            width: 3.39rem;
            height: 0.8rem;
            background: {
              image: url("../assets/img/sku-twoSpe-active-tab.png");
              repeat: no-repeat;
              size: 100% 100%;
            };
          }
          .specification-noActive-tab{
            width: 3.39rem;
            height: 0.8rem;
            text-align: center;
            line-height: 0.7rem;
          }

        }
      }
      .sku-view-all{
        margin-top: 0.24rem;
        width: 100%;
        display: flex;
        justify-content: center;
        max-height: 6.7rem;
        min-height: 3rem;
        overflow-y: scroll;
        .sku-view{
          width: 6.7rem;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .sku-item-view{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom:0.3rem;
            .skuPictureDiv{
              width:3.2rem;
              height:3.2rem;
              border: 1px solid #d0a12d;
              border-radius: 0.24rem;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #fff;
              img{
                height:3rem;
                border-radius: 0.24rem;
              }
            }
          }
        }
      }
    }
    .step2-sku-view-all{
      .step2-view{
        display: flex;
        justify-content: center;
        margin-top: 0.3rem;
        .step2-title{
          width: 6.04rem;
          height: 0.58rem;
          background: {
            image: url("../assets/img/sku-formal-title.png");
            repeat: no-repeat;
            size: 100% 100%;
          };
        }
      }
      .sku-view-all{
        margin-top: 0.24rem;
        width: 100%;
        display: flex;
        justify-content: center;
        max-height: 6.7rem;
        min-height: 3rem;
        overflow-y: scroll;
        .sku-view{
          width: 6.7rem;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .sku-item-view{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom:0.3rem;
            .skuPictureDiv{
              width:3.2rem;
              height:3.2rem;
              border: 1px solid #d0a12d;
              border-radius: 0.24rem;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #fff;
              img{
                height:3rem;
                border-radius: 0.24rem;
              }
            }
          }
        }
      }
    }
  }
}
.result {
  width: 100%; /* 宽度自适应 */
  min-height: 100vh;
  height: 18.80rem;
  padding-top: 2.2rem;
  position: relative;
  text-align: center;
  //padding-bottom: 1.2rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/277144/4/25403/12461/68070efbF29e79ab2/3b242d5fa3fc3a8e.png");
    repeat: no-repeat;
    size: 100% 100%;
  };

  .logo {
    width: 1.54rem;
    height: 0.75rem;
    position: absolute;
    top: 0.8rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .result-title {
    width: 4.14rem;
    margin: 0 auto 1.25rem auto;
  }

  .result-banner {
    width: 6.48rem;
    margin: 1.25rem auto 0;
  }

  .result-btn {
    height: 0.65rem;
    margin: 1.3rem auto 0;
  }
  .bottom{
    margin: 3rem auto 0;
    width: 7.2rem;
  }
}

</style>
