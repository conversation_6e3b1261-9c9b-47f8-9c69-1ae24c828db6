<template>
  <!-- background -->
  <div v-if="isJoin" class="home pageBg" :style="furnishStyles.pageBg.value" >
    <!-- 活动规则按钮 -->
    <div class="handle-side-btn" @click="showRule = true"></div>
    <!-- 我的奖品按钮 -->
    <div class="handle-side-btn" style="top: 6.5rem" @click="showMyPrize = true"></div>
    <!-- 未报名手机号验证码信息收集 -->
    <div class="phone-info-view" v-if="!isSignUp">
      <div class="phone-input">
        <input type="tel" maxlength="11" placeholder="请输入您的手机号码">
      </div>
      <div class="phone-input">
        <input type="tel" maxlength="6" placeholder="请输入短信验证码">
        <div class="send-verification-btn" :class="{gray:false}" @click="toast()">
          发送验证码
        </div>
      </div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/281095/20/21773/22440/6801b7f8F8c308032/f5120b9211731e71.png"
           @click="toast()" class="commit-check-btn" alt="">
    </div>
    <!-- 已报名 -->
    <div v-else class="phone-info-view">
      <div class="signUp-view-all">
        <div class="signUp-view"></div>
      </div>
    </div>
    <!-- 奖品规格标题-->
    <div class="prize-info-view">
      <div class="prize-info-title"></div>
      <div class="prize-info-text">首次购买(规格1{{prizeList.length === 2 ? '/规格2' : ''}})试用产品一听，在活动期间内复购同系列、指定罐数正装，可获得【对应规格】的【对应奖品】</div>
      <div class="prize-list-view">
        <div class="prize-item-view" v-for="(item,index) in prizeList" :key="index">
          <div class="prize-index">规格{{item.specs === 1 ? 1 : 2}}</div>
          <div class="prize-content-view">
            <div class="prize-left-view">
              <img :src="item.prizeImg" alt="" />
            </div>
            <div class="prize-right-view">
              <div class="prize-name-view">{{item.prizeName}}</div>
              <div class="prize-rest-view">剩余库存：{{item.sendTotalCount}}份</div>
              <div class="prize-btn-view">
                <div class="prize-can-btn" @click="toast()"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="prize-remark-view">注：<br> 1、用户需先报名，后续下单才可计入统计；<br> 2、首购试用装和复购正装需在本页面显示的产品中进行下单；需是相同系列奖品，且订单皆确认收货后才有资格领取奖品；<br> 3、两档规格类奖品仅可选择其中一个奖品进行领取。</div>
    </div>
    <!-- 系列商品-->
    <div class="sku-info-view">
<!--      :style="seriesPrizeList.length >= 3 ? {} : { justifyContent: 'space-around' }"-->
      <div class="series-tab-view-all" :style="seriesSkuList.length >= 3 ? {} : { justifyContent: 'space-around' }">
        <div class="series-tab-view" v-for='(item,index) in seriesSkuList' :key='index'>
          <div class="series-tab1">
            <div :class="[currentSeriesTab === index ? 'series-active-tab' : 'series-noActive-tab']" @click='currentSeriesTabClick(index)'>{{item.seriesName ? item.seriesName : '系列1'}}</div>
          </div>
        </div>
      </div>
      <div class="step1-sku-view-all">
        <div class="step1-view">
          <div class="step1-title"></div>
        </div>
        <div class="specification-tab-view" v-if="prizeList.length === 2">
          <div class="specification-tab" v-for='(item1,index1) in prizeList' :key='index1'>
            <div :class="[currentSpecificationTab === index1 ? 'specification-active-tab' : 'specification-noActive-tab']" @click='currentSpecificationTab=index1'>规格{{index1 + 1}}</div>
          </div>
        </div>
        <div class="specification-oneTab-view" v-else>规格1</div>
        <div class="sku-view-all">
          <div class="sku-view" v-if="currentSpecificationTab === 0 && seriesSkuList[currentSeriesTab] && seriesSkuList[currentSeriesTab].skuList1">
            <div class="sku-item-view" v-for="(item3,index3) in seriesSkuList[currentSeriesTab].skuList1" :key="index3">
              <div class="skuPictureDiv" @click="toast()">
                <img :src='item3.showSkuImage' alt=''>
              </div>
            </div>
          </div>
          <div class="sku-view" v-if="currentSpecificationTab === 1 && seriesSkuList[currentSeriesTab] && seriesSkuList[currentSeriesTab].skuList2">
            <div class="sku-item-view" v-for="(item3,index3) in seriesSkuList[currentSeriesTab].skuList2" :key="index3">
              <div class="skuPictureDiv" @click="toast()">
                <img :src='item3.showSkuImage' alt=''>
              </div>
            </div>
          </div>

        </div>
      </div>
      <div class="step2-sku-view-all">
        <div class="step2-view">
          <div class="step2-title"></div>
        </div>
        <div class="sku-view-all">
          <div class="sku-view" v-if="seriesSkuList[currentSeriesTab] && seriesSkuList[currentSeriesTab].formalSkuList">
            <div class="sku-item-view" v-for="(item3,index3) in seriesSkuList[currentSeriesTab].formalSkuList" :key="index3">
              <div class="skuPictureDiv" @click="toast()">
                <img :src='item3.showSkuImage' alt=''>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-view"></div>
  </div>
  <div v-else>
    <div class="result">
      <img class="logo" src="../assets/img/shop-icon.png" alt="">

      <img class="result-title" src="../assets/img/sorry-title.png" alt="">

      <img class="result-banner" :src="furnish.canNotJoinKv ? furnish.canNotJoinKv : '//img10.360buyimg.com/imgzone/jfs/t1/281630/6/21198/148068/67ff7731F55c098cc/b8b5ac78321e861e.png'" alt="">

      <img class="result-btn" src="../assets/img/joinOtherBtn.png" @click="handleResult(btnRes)" alt="">
      <!--    <img class="bottom" src="../assets/img/bottom-text.png" alt="">-->
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showRule" :close-on-click-overlay="false">
    <RuleDialog @closeDialog="showRule = false" :rule="rule"/>
    </VanPopup>
  <VanPopup teleport="body" v-model:show="showMyPrize" :close-on-click-overlay="false">
    <MyRecordDialog @closeDialog="showMyPrize = false"/>
  </VanPopup>
</template>
<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useCountdown from '@/hooks/useCountdown';
import { showToast } from 'vant';
import { dialogName } from '../ts/dialog';
import RuleDialog from '../components/RuleDialog.vue';
import MyRecordDialog from '../components/MyRecordDialog.vue';
import { defaultSeriesList, defaultStateList } from '../ts/default';

const activityData = inject('activityData') as any;

const isPreview = ref(false);
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();
// 默认设置结束时间戳为1小时后
const endTime = ref(dayjs().add(30, 'day').valueOf());
const countdownTime = useCountdown(endTime);

const isLoadingFinish = ref(true);

const isExposure = ref(0);

const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const rule = ref('');
const showMyPrize = ref(false);
const prizeList = ref(defaultStateList);
const currentSeriesTab = ref(0); // 默认选中系列下标
const currentSpecificationTab = ref(0); // 默认规格tab下标
const isSignUp = ref(false); // 是否报名 true  已报名 false 未报名
const isJoin = ref(true); // 是否满足参与条件
const seriesSkuList = ref(defaultSeriesList);

// 切换谢列tab的时候规格从1开始
const currentSeriesTabClick = (clickIndex: number) => {
  currentSeriesTab.value = clickIndex;
  currentSpecificationTab.value = 0;
};
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  console.log(data, '活动数据监听====');
  prizeList.value = data.prizeList.filter((item) => item.prizeType > 0);
  seriesSkuList.value = data.seriesSkuList;
  isExposure.value = data.isExposure;
  rule.value = data.rules;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    rule.value = activityData.rules;
    endTime.value = dayjs(activityData.endTime).valueOf();
    shopName.value = activityData.shopName;
    prizeList.value = activityData.prizeList.filter((item) => item.prizeType > 0);
    seriesSkuList.value = activityData.seriesSkuList;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
const handleResult = (res: string) => {
  window.location.href = furnish.jumpLink;
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.pageBg {
  background-size: 100%;
  min-height:100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
}
.header-kv{
  padding: 0 0 11rem 0;
  .btnAllClass{
    margin-top: 4rem;
    .btnClass{
      width: 0.61rem;
      height: 1.61rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.5rem 0 0 0;
      background-color: rgba(154, 79, 79, 0.41);
    }
  }
}
.home {
  //width: 100%; /* 宽度自适应 */
  //min-height: 45.8rem;
  //padding-top: 11.86rem;
  //position: relative;
  //background-position: top left, bottom left;
  //background-size: contain, 100% 100%; /* 强制等比缩放填满容器 */
  //background-repeat: no-repeat;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-top: 11.86rem;
  position: relative;
  padding-bottom: 1rem;
  .handle-side-btn {
    width: 0.6rem;
    height: 1.8rem;
    position: absolute;
    right: 0;
    top: 4rem;
  }

  .phone-info-view {
    width: 7.18rem;
    height: 2.9rem;
    margin: 0rem auto 0;
    padding: .35rem 0;
    text-align: center;
    background: {
      image: url("../assets/img/phone-info-view.png");
      repeat: no-repeat;
      size: contain;
    };

    .phone-input {
      width: 4rem;
      height: .6rem;
      line-height: .56rem;
      padding-left: .15rem;
      margin: 0 auto .2rem;
      border: 2px solid #1d2e81;
      border-radius: .5rem;
      position: relative;

      input {
        width: 3.8rem;
        font-size: .28rem;
        line-height: .56rem;
        color: #434343;
        letter-spacing: .01rem;
        background-color: transparent !important;
        border: transparent !important;
      }

      input::placeholder {
        color: #999; /* 占位文字颜色 */
        font-size: .24rem; /* 字体大小 */
        letter-spacing: -.01rem;
      }

      .send-verification-btn {
        width: 1.7rem;
        height: .587rem;
        line-height: .61rem;
        text-align: center;
        font-size: .26rem;
        color: #FFFFFF;
        position: absolute;
        right: -.01rem;
        top: -.01rem;
        cursor: pointer;
        background: {
          image: url("//img10.360buyimg.com/imgzone/jfs/t1/254993/26/29046/1831/67c7eb98F7e1c6b00/f7a7f44f7518d57f.png");
          size: contain;
          repeat: no-repeat;
        };
      }
    }

    .commit-check-btn {
      width: 3.04rem;
      margin: .1rem auto 0;
    }
    .signUp-view-all{
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .signUp-view{
        width: 4.47rem;
        height: 1.79rem;
        background: {
          image: url("../assets/img/signUp.png");
          repeat: no-repeat;
          size: 100% 100%;
        };
      }
    }
  }

  .prize-info-view{
    width: 7.18rem;
    border: 2px solid #88a8f1;
    border-radius: 0.36rem;
    background: rgba(255, 255, 255, 0.3);
    margin:0.34rem auto 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 0.4rem;
    .prize-info-title{
      width:3.74rem;
      height:0.49rem;
      //margin-left: calc(50% - 3.74rem / 2);
      background: {
        image: url("../assets/img/prize-info-title.png");
        size: 100% 100%;
        repeat: no-repeat;
      };
    }
    .prize-info-text{
      margin: 0.36rem auto 0;
      width:6rem;
      font-size: 0.24rem;
      color: #606060;
    }
    .prize-list-view{
      margin-top: 0.36rem;
      .prize-item-view{
        position: relative;
        width: 6.52rem;
        height:2.44rem;
        margin-bottom: 0.16rem;
        background: {
          image: url("../assets/img/prize-item-bg.png");
          size: 100% 100%;
          repeat: no-repeat;
        };
        .prize-index{
          width: 1.44rem;
          position: absolute;
          left:0.2rem;
          top: 0.04rem;
          color: #fff;
          font-size: 0.28rem;
        }
        .prize-content-view{
          display: flex;
          padding: 0.32rem 0.24rem 0 0.5rem;
          .prize-left-view{
            img {
              width: 2rem;
            }
          }
          .prize-right-view{
            margin-left:0.5rem;
            .prize-name-view{
              font-size: 0.28rem;
              color: #3f3f3f;
              span{
                color: #264fbe;
                margin-right: 0.18rem;
              }
            }
            .prize-rest-view{
              font-size: 0.22rem;
              color: #345ecb;
              margin-top: 0.2rem;
            }
            .prize-btn-view{
              position: absolute;
              right: 0.4rem;
              bottom: 0.2rem;
              .prize-can-btn{
                width: 1.52rem;
                height: 0.54rem;
                background: {
                  image: url("../assets/img/prize-can-gray-btn.png");
                  size: 100% 100%;
                  repeat: no-repeat;
                };
              }
            }
          }
        }
      }
    }
    .prize-remark-view{
      font-size: 0.18rem;
      color: #878d97;
      width: 6.40rem;

    }
  }
  .sku-info-view{
    width: 7.20rem;
    border: 2px solid #88a8f1;
    border-radius: 0.36rem;
    background: rgba(255, 255, 255, 0.3);
    padding: 0.2rem 0;
    margin: 0.36rem auto;
    .series-tab-view-all{
      display: flex;
      align-items: center;
      overflow-x: scroll;
      width: 6.6rem;
      margin: auto;
      .series-tab-view{
        text-align: center;
        color: #0033be;
        .series-tab1{
          display: flex;
          .series-active-tab{
            color: #a0642e;
            font-size: 0.28rem;
            width:2.2rem;
            height:0.8rem;
            line-height: 0.6rem;
            background: {
              image: url("../assets/img/sku-activie-series-tab.png");
              repeat: no-repeat;
              size: 100% 100%;
            };
          }
          .series-noActive-tab{
            color: #848484;
            font-size: 0.28rem;
            width:2.2rem;
            height:0.8rem;
            line-height: 0.6rem;
            background: {
              image: url("../assets/img/sku-noActivie-series-tab.png");
              repeat: no-repeat;
              size: 100% 100%;
            };
          }
        }
      }
    }
    .step1-sku-view-all{
      .step1-view{
        display: flex;
        justify-content: center;
        .step1-title{
          width: 4.1rem;
          height: 0.57rem;
          background: {
            image: url("../assets/img/sku-try-title.png");
            repeat: no-repeat;
            size: 100% 100%;
          };
        }
      }
      .specification-oneTab-view{
        text-align: center;
        line-height: 0.7rem;
        color: #a0642e;
        font-size: 0.3rem;
        width: 6.77rem;
        height: 0.80rem;
        margin-left: calc(50% - 6.77rem / 2);
        background: {
          image: url("../assets/img/sku-oneSpe-active-tab.png");
          repeat: no-repeat;
          size: 100% 100%;
        };
      }
      .specification-tab-view{
        display: flex;
        width: 6.9rem;
        height: 0.89rem;
        margin-left: calc(50% - 6.9rem / 2);
        background: {
          image: url("../assets/img/sku-spe-noActive-tab.png");
          repeat: no-repeat;
          size: 100% 100%;
        };
        .specification-tab{
          color: #908f8f;
          font-size: 0.3rem;
          .specification-active-tab{
            text-align: center;
            line-height: 0.7rem;
            color: #a0642e;
            font-size: 0.3rem;
            width: 3.39rem;
            height: 0.8rem;
            background: {
              image: url("../assets/img/sku-twoSpe-active-tab.png");
              repeat: no-repeat;
              size: 100% 100%;
            };
          }
          .specification-noActive-tab{
            width: 3.39rem;
            height: 0.8rem;
            text-align: center;
            line-height: 0.7rem;
          }

        }
      }
      .sku-view-all{
        margin-top: 0.24rem;
        width: 100%;
        display: flex;
        justify-content: center;
        max-height: 6.7rem;
        min-height: 3rem;
        overflow-y: scroll;
        .sku-view{
          width: 6.7rem;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .sku-item-view{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom:0.3rem;
            .skuPictureDiv{
              width:3.2rem;
              height:3.2rem;
              border: 1px solid #2852c1;
              border-radius: 0.24rem;
              display: flex;
              align-items: center;
              justify-content: center;
              img{
                height:3rem;
                border-radius: 0.24rem;
              }
            }
          }
        }
      }
    }
    .step2-sku-view-all{
      .step2-view{
        display: flex;
        justify-content: center;
        margin-top: 0.3rem;
        .step2-title{
          width: 6.04rem;
          height: 0.58rem;
          background: {
            image: url("../assets/img/sku-formal-title.png");
            repeat: no-repeat;
            size: 100% 100%;
          };
        }
      }
      .sku-view-all{
        margin-top: 0.24rem;
        width: 100%;
        display: flex;
        justify-content: center;
        max-height: 6.7rem;
        min-height: 3rem;
        overflow-y: scroll;
        .sku-view{
          width: 6.7rem;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .sku-item-view{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom:0.3rem;
            .skuPictureDiv{
              width:3.2rem;
              height:3.2rem;
              border: 1px solid #2852c1;
              border-radius: 0.24rem;
              display: flex;
              align-items: center;
              justify-content: center;
              img{
                height:3rem;
                border-radius: 0.24rem;
              }
            }
          }
        }
      }
    }
  }
  .bottom-view{
    width: 6.93rem;
    height: 0.70rem;
    position: absolute;
    bottom:0.24rem;
    left: calc(50% - 6.93rem / 2);
    background:  {
      image: url("../assets/img/dibu.png");
      repeat: no-repeat;
      size: 100% 100%;
    };
  }
}
.result {
  width: 100%; /* 宽度自适应 */
  min-height: 100vh;
  height: 18.80rem;
  padding-top: 2.2rem;
  position: relative;
  text-align: center;
  //padding-bottom: 1.2rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/279479/31/22638/16534/68021d99F5f180ace/25dfc1721af06b8e.png");
    repeat: no-repeat;
    size: 100% 100%;
  };

  .logo {
    width: 1.68rem;
    height: 0.60rem;
    position: absolute;
    top: 0.8rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .result-title {
    width: 4.14rem;
    margin: 0 auto 1.25rem auto;
  }

  .result-banner {
    width: 6.48rem;
    margin: 1.25rem auto 0;
  }

  .result-btn {
    height: 0.65rem;
    margin: 1.3rem auto 0;
  }
  .bottom{
    margin: 3rem auto 0;
    width: 7.2rem;
  }
}

</style>
