<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/98470/25/28559/486672/629ffee1E86f337ae/7a3efc92de801c7c.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div class="header-btn-all">
          <div class="header-btn"  :style="{'backgroundImage':'url(' + furnish.ruleBtn + ')'}"  v-click-track="'hdgz'" @click="showRulePopup()"><div>活动规则</div></div>
          <div class="header-btn" :style="{'backgroundImage':'url(' + furnish.prizeBtn + ')'}" v-click-track="'wdjp'" @click="showMyPrize = true"><div>我的奖品</div></div>
        </div>
      </div>
    </div>
    <div v-if="!isAcEnd">
      <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart"></CountDown>
    </div>
    <div id="box" :class="['dateTitleDivAll', isAcEnd || !isStart? 'dateTitleDivAll1' : 'dateTitleDivAll2']">
      <div :id="`dateTitleItemDiv${index}`" class="dateTitleItemDiv" v-for="(item,index) in taskDateList" :key="index">
        <div v-if="isSelectDate === index" :class="['dateTitleItemSelectDiv' ? item.status === 1 ? 'dateTitleItemSelect1Div' : item.status === 3 ? 'dateTitleItemSelect3Div' : 'dateTitleItemSelectDiv' : '' ]" @click="dateClick(item, index)">
          <div class="dateDiv">{{item.startTime}} ~ {{item.endTime}}</div>
          <div class="dateStatusDiv">
            <div class="dateStatusDiv1Select" v-if="item.status === 1">即将开始</div>
            <div class="dateStatusDiv2Select" v-else-if="item.status === 2">疯抢中</div>
            <div class="dateStatusDiv3" v-else-if="item.status === 3">今日已结束</div>
          </div>
        </div>
        <div v-else :class="['dateTitleItemDiv3',item.status === 2 ? 'dateTitleItemSelectDiv2' : item.status === 1 ? 'dateTitleItemSelectDiv1' : '']" @click="dateClick(item, index)">
          <div class="dateDiv">{{item.startTime}} ~ {{item.endTime}}</div>
          <div class="dateStatusDiv">
            <div class="dateStatusDiv1" v-if="item.status === 1">即将开始</div>
            <div class="dateStatusDiv2" v-else-if="item.status === 2">疯抢中</div>
            <div class="dateStatusDiv3" v-else-if="item.status === 3">今日已结束</div>
          </div>
        </div>
      </div>
    </div>
    <div class="prizeDivAll">
      <div class="prizeListDivAll" v-if="taskDateList.length > 0">
        <div class="prizeItemDivAll" v-for="(items,indexs) in taskDateList[isSelectDate].prizeResponses" :key="indexs" :style="{'backgroundImage':'url('+furnish.prizeItemBg+')'}">
          <div class="leftDiv">
            <div class="prizeInfoDiv">￥<span>{{items.couponDiscount ? items.couponDiscount : 0}}</span></div>
            <div class="messageDiv">
              <div class="prizeNameDiv">{{items.prizeName}}</div>
              <div class="textDiv">满{{items.couponQuota ? items.couponQuota : 0}}元使用</div>
            </div>
          </div>
          <div class="rightBtnDiv">
            <div class="canBtnDiv1" v-if="taskDateList[isSelectDate].status === 3">
              <div class="getGrayBtnDiv" v-if="items.status === 1" :style="furnishStyles.prizeGrayBtn.value">今日已结束</div>
              <div class="getGrayBtnDiv" v-if="items.status === 2" :style="furnishStyles.prizeGrayBtn.value">此券已领</div>
            </div>
            <div class="canBtnDiv2" v-else-if="taskDateList[isSelectDate].status === 2">
              <div class="getBtnDiv" v-if="items.status === 1" :style="furnishStyles.prizeCanBtn.value" v-threshold-click="() => lotteryDraw(items)">立即抢券</div>
              <div class="getGrayBtnDiv" v-if="items.status === 2" :style="furnishStyles.prizeGrayBtn.value">此券已领</div>
            </div>
            <div class="canBtnDiv1" v-else-if="taskDateList[isSelectDate].status === 1">
              <div class="getGrayBtnDiv" v-if="items.status === 1" :style="furnishStyles.prizeGrayBtn.value">即将开始</div>
              <div class="getGrayBtnDiv" v-if="items.status === 2" :style="furnishStyles.prizeGrayBtn.value">此券已领</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sku" v-if="skuList && skuList.length > 0">
      <img class="title-img" :src="furnish.winnersBg" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" v-click-track="'ljgm'"  @click="gotoSkuPage(item.skuId)">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns">
            <div class="price">￥{{ item.jdPrice }}</div>
            <div class="to-bug">抢购</div>
          </div>
        </div>
      </div>
      <div class="more-btn-all">
        <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div>
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @savePhone="showSavePhone" @showCardNum="showCardNum"></MyPrize>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @savePhone="showSavePhone" @showCardNum="showCardNum"></AwardPopup>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, nextTick } from 'vue';
import furnishStyles, { furnish, prizeInfo, PrizeTypeName } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { PrizeInfo } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import CountDown from '../components/CountDown.vue';
import dayjs from 'dayjs';
import { gotoSkuPage } from '@/utils/platforms/jump';

const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const shopName = ref(baseInfo.shopName);
const prizeList = ref<PrizeInfo[]>([]); // 奖品列表
const endTime = ref(0);
const startTime = ref(0);
const isStart = ref(false);
const isAcEnd = ref(false); // 活动是否已经结束
const taskDateList = ref([]);
const isSelectDate = ref(0); // 选中的那个时间段的下标
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const showMyPrize = ref(false);
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 活动倒计时
const getTime = () => {
  startTime.value = dayjs(baseInfo.startTime).valueOf();
  endTime.value = dayjs(baseInfo.endTime).valueOf();
  const now = dayjs().valueOf();
  if (now > endTime.value) {
    // 活动已结束
    isAcEnd.value = true;
    isStart.value = true;
  } else if (now > startTime.value && now <= endTime.value) {
    // 活动进行中
    isAcEnd.value = false;
    isStart.value = true;
  } else if (now < startTime.value) {
    // 活动未开始
    isStart.value = false;
  }
};
// 主接口
const getPrizesData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10048/activityInfo');
    closeToast();
    data.forEach((itemData, index1) => {
      // 获取当前日期的开始（午夜）
      const todayStart = dayjs().startOf('day');
      // 假设我们有两个时间字符串
      const timeStr1 = itemData.startTime;
      const timeStr2 = dayjs().format('HH:mm');
      const timeStr1Hour = timeStr1.split(':')[0];
      const timeStr1Minute = timeStr1.split(':')[1];

      const timeStr2Hour = timeStr2.split(':')[0];
      const timeStr2Minute = timeStr2.split(':')[1];

      // 需要比对的时间
      const timeStr1Time = todayStart.hour(timeStr1Hour).minute(timeStr1Minute).format();
      // 当前时间
      const timeStr2Time = todayStart.hour(Number(timeStr2Hour)).minute(Number(timeStr2Minute)).format();
      console.log(timeStr1Time, timeStr2Time, '假设我们有两个时间字符串');
      // 现在我们可以比较这两个时间的总分钟数来确定哪个时间更早或更晚
      if (dayjs(timeStr1Time).isAfter(dayjs(timeStr2Time))) {
        console.log(`${timeStr1} 在 ${timeStr2} 之前`);
        // 未开始
        itemData.status = 1;
      } else {
        // 当前时间在开始时间之后
        console.log(`${timeStr1} 在 ${timeStr2} 之后`);
        const timeEndStr = itemData.endTime;
        const timeEndHour = timeEndStr.split(':')[0];
        const timeEndMinute = timeEndStr.split(':')[1];
        const timeEndTime = todayStart.hour(Number(timeEndHour)).minute(Number(timeEndMinute)).format();
        if (dayjs(timeStr2Time).isBefore(dayjs(timeEndTime))) {
          // 进行中
          itemData.status = 2;
          isSelectDate.value = index1;
        } else {
          // 已结束
          itemData.status = 3;
        }
      }
    });
    taskDateList.value = data;
    nextTick(() => {
      console.log(isSelectDate.value, 'isSelectDate.value');
      const boxEl = document.getElementById('box');
      const { clientWidth } = boxEl;
      const dateTitleItemDiv = document.getElementById(`dateTitleItemDiv${isSelectDate.value}`);
      const { offsetLeft } = dateTitleItemDiv;
      if (offsetLeft >= clientWidth) {
        boxEl.scrollLeft = offsetLeft - clientWidth / 2 + dateTitleItemDiv.clientWidth / 2;
      } else {
        boxEl.scrollLeft = isSelectDate.value === 3 ? clientWidth / 2 + dateTitleItemDiv.clientWidth / 2 : 0;
      }
    });
    console.log(data, '主接口数据=======');
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};
// 点击时间段
const dateClick = (itemData:any, indexs:number) => {
  console.log(itemData, '点击时间段');
  isSelectDate.value = indexs;

};
const getExposureData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10048/getExposureSku', {
      pageNum: pageNum.value,
      pageSize: 10,
      type: 0,
      x: 'exposure',
    });
    closeToast();
    if (res.data.records && res.data.records.length > 0) {
      skuList.value.push(...res.data.records);
    }
    pagesAll.value = res.data.pages;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
// 曝光商品加载更多
const loadMore = async () => {
  pageNum.value++;
  await getExposureData();
};

// 抽奖接口
const lotteryDraw = async (itemData:any) => {
  try {
    lzReportClick('kslj');
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10048/sendPrize', {
      prizeId: itemData.prizeId,
    });
    closeToast();
    award.value = res.data;
    showAward.value = true;
    await getPrizesData();
  } catch (error) {
    console.error(error);
    showToast({
      message: error.message,
      duration: 2000,
      onClose: (() => {
        getPrizesData();
      }),
    });
  }
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    await Promise.all([getPrizesData(), getExposureData()]);
    // closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color:#f2f2f2
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }
  .header-btn-all{
    .header-btn {
      width: 1.18rem;
      line-height: 0.44rem;
      margin-bottom: 0.1rem;
      font-size: 0.2rem;
      text-align: center;
      border-radius: 0.22rem;
      border: 0.01rem;
      border-style: solid;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background:rgb(255, 255, 255);
      border-color: rgb(255, 201, 46);
      color:rgb(242, 39, 12);
    }
  }
}

.dateTitleDivAll{
  width: 6.90rem;
  height: 1.4rem;
  background: rgb(255, 255, 255);
  border-radius: 0.2rem;
  margin-top: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-left: 50%;
  transform: translate(-50%);
  overflow: scroll;
  .dateTitleItemDiv{
    height: 100%;
    color: rgb(191, 191, 191);
    font-size: 0.3rem;
    min-width: 30%;
    flex: 1 1 0%;
    .dateTitleItemDiv3{
      height: 100%;
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .dateTitleItemDiv2{
      height: 100%;
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #f2270c;
    }
    .dateTitleItemDiv1{
      color: #262626 !important;
    }
    .dateTitleItemSelectDiv{
      height: 100%;
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #f2270c;
      background: #ffcdd5;
    }
    .dateTitleItemSelect1Div{
      height: 100%;
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #ffcdd5;
      color: #262626;
    }
    .dateTitleItemSelect3Div{
      height: 100%;
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #ffcdd5;
      color: #bfbfbf;
    }
    .dateDiv3{
      font-weight: 700;
    }
    .dateStatusDiv{

      .dateStatusDiv1Select{
        display: inline-block;
        min-width: 1.1rem;
        height: 0.4rem;
        font-size: 0.2rem;
        border-radius: 0.2rem;
        line-height: 0.38rem;
        margin-top: 0.1rem;
        text-align: center;
        background: #262626 !important;
        color: #fff !important;
      }
      .dateStatusDiv2Select{
        display: inline-block;
        min-width: 1.3rem;
        height: 0.4rem;
        border: 1px solid rgb(242, 39, 12);
        font-size: 0.2rem;
        border-radius: 0.2rem;
        line-height: 0.4rem;
        margin-top: 0.1rem;
        text-align: center;
        background: #f2270c;
        color: #fff !important;
      }
      .dateStatusDiv1{
        display: inline-block;
        min-width: 1.3rem;
        height: 0.4rem;
        color: #262626 !important;
        border: 1px solid #262626 !important;
        font-size: 0.2rem;
        border-radius: 0.2rem;
        line-height: 0.4rem;
        margin-top: 0.1rem;
        text-align: center;
      }
      .dateStatusDiv2{
        display: inline-block;
        min-width: 1.3rem;
        height: 0.4rem;
        border: 1px solid rgb(242, 39, 12);
        font-size: 0.2rem;
        border-radius: 0.2rem;
        line-height: 0.4rem;
        margin-top: 0.1rem;
        background: #f2270c;
        color: #fff !important;
        text-align: center;
      }
      .dateStatusDiv3{
        display: inline-block;
        min-width: 1.3rem;
        height: 0.4rem;
        color: rgb(191, 191, 191);
        border: 1px solid rgb(191, 191, 191);
        font-size: 0.2rem;
        border-radius: 0.2rem;
        line-height: 0.4rem;
        margin-top: 0.1rem;
        text-align: center;
      }
    }
  }
}
.prizeDivAll{
  .prizeListDivAll{
    .prizeItemDivAll{
      margin-left: 50%;
      transform: translate(-50%);
      width: 6.90rem;
      height: 2.35rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/78449/18/19217/65461/62a00531E2b6fb2c1/ef31a127b56a101a.png);
      background-size: 100%;
      background-repeat: no-repeat;
      box-sizing: border-box;
      padding: 0.6rem 0.2rem 0.2rem;
      margin-top: -0.2rem;
      display: flex;
      justify-content: space-between;
      .leftDiv{
        display: flex;
        width: 4.5rem;
        position: relative;
        .prizeInfoDiv{
          color: rgb(242, 39, 12);
          display: flex;
          align-items: center;
          span{
            font-size: 0.68rem;
            font-weight: 400;
            line-height: 1.5rem;
            text-align: center;
          }
        }
        .messageDiv{
          //width: calc(100% - 1.8rem);
          height: 1.5rem;
          display: flex;
          align-items: start;
          flex-direction: column;
          justify-content: center;
          padding-left: 0.05rem;
          position: absolute;
          left: 2.2rem;
          .prizeNameDiv{
            font-size: 0.36rem;
            color: rgb(38, 38, 38);
            display: block;
            font-weight: 700;
            text-align: left;
          }
          .textDiv{
            font-weight: 400;
            font-size: 0.24rem;
            color: rgb(38, 38, 38);
          }
        }
      }
      .rightBtnDiv{
        height: 100%;
        display: flex;
        align-items: center;
        .canBtnDiv2{
          min-width: 1.5rem;
          height: 0.68rem;
          //background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
          border-radius: 0.4rem;
          font-size: 0.24rem;
          color: rgb(255, 246, 238);
          line-height: 0.68rem;
          text-align: center;
          display: block;
          cursor: pointer;
        }
        .canBtnDiv1{
          min-width: 1.5rem;
          height: 0.68rem;
          //background: #bfbfbf !important;
          border-radius: 0.4rem;
          font-size: 0.24rem;
          color: rgb(255, 246, 238);
          line-height: 0.68rem;
          text-align: center;
          display: block;
          cursor: pointer;
        }
        .getGrayBtnDiv{
          border-radius: 0.4rem;
        }
        .getBtnDiv{
          border-radius: 0.4rem;
        }
      }
    }
  }
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  margin-top:0.2rem;
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .more-btn-all {
    width:6.9rem;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 0.27rem;
        color: #262626;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.2rem 0;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug {
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

</style>
