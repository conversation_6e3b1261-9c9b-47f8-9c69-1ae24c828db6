<template>
  <div class="task-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>获得更多抽奖机会</div>
      <div class="rightLineDiv"></div>
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="times">
      今天还有 <span>{{ times }}</span> 次抽奖机会
    </div>
    <div class="content">
      <div v-for="(item, index) in tasks" :key="item.id" class="task">
        <img :src="taskInfo[item.taskType].icon" alt="" class="icon" />
        <div class="info">
          <div class="name">{{ taskInfo[item.taskType].label }}</div>
          <div class="rule">{{ taskRule[item.taskType](item) }}</div>
        </div>
        <div v-if="!isPreview">
          <div class="button" v-if="item.taskFinishCount < item.limit || item.taskType === 8" v-threshold-click="() => doTask(index)">{{ taskInfo[item.taskType].button }}</div>
          <div class="button button-dis" v-else>{{ taskInfo[item.taskType].buttonDIs }}</div>
        </div>
        <div v-else><div class="button button-dis">{{ taskInfo[item.taskType].buttonDIs }}</div></div>
      </div>
    </div>
  </div>
  <!-- 活动门槛 -->
<!--  <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />-->
  <VanPopup teleport="body" v-model:show="showSku" position="bottom">
    <ShowSku v-if="showSku" :detail="tasks[taskDetailIndex]" @close="showSku = false" @refreshTask="refreshTask"></ShowSku>
  </VanPopup>
</template>

<script lang="ts" setup>
import { PropType, inject, ref } from 'vue';
import { showToast, showLoadingToast, closeToast } from 'vant';
import ShowSku from './ShowSku.vue';
import { callShare } from '@/utils/platforms/share';
import { httpRequest } from '@/utils/service';
import { Task } from 'pages/10054/1001/ts/type';
import constant from '@/utils/constant';
// import Threshold2 from '@/components/Threshold2/index.vue';
// import useThreshold from '@/hooks/useThreshold';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
// // 展示门槛显示弹框
// const showLimit = ref(false);
const isPreview = (inject('isPreview') as boolean) ?? false;

const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) as string);

const pros = defineProps({
  times: {
    type: Number,
    default: 0,
  },
  tasks: {
    type: Array as PropType<Task[]>,
    default: () => [],
    required: true,
  },
  shareImg: {
    type: String,
    default: '',
  },
  shareTitle: {
    type: String,
    default: '',
  },
  shopId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close', 'refreshTask']);

const close = () => {
  emits('close');
};

const refreshTask = () => {
  emits('refreshTask');
};

const showSku = ref(false);
const taskDetailIndex = ref(0);

// 上报任务完成
const reportTask = async (taskId: number, skuId = '') => {
  try {
    const apis = {
      9: '/10054/shareSku',
      10: '/10054/shareShop',
      12: '/10054/shareActivity',
    };
    const res = await httpRequest.post(apis[taskId], { skuId });
    refreshTask();
    if (res.code === 200) {
      showToast({
        message: '分享成功',
        duration: 1500,
        onClose: (() => {
          console.log('分享成功');
        }),
      });
    } else {
      showToast(res.msg);
    }
  } catch (error) {
    console.error(error);
    showToast({
      message: error.message,
      duration: 2000,
      onClose: (() => {
        console.error(error);
      }),
    });
  }
};

const checkTaskTimeLimit = async (taskType: any) => {
  try {
    const res = await httpRequest.post('/10054/checkTask', { taskType });
    return !(res.code === 200);
  } catch (e) {
    showToast(e);
    return true;
  }
};

const doTask = async (index: number) => {
  if (isPreview) return;
  // if (baseInfo.thresholdResponseList.length) {
  //   showLimit.value = useThreshold({
  //     thresholdList: baseInfo.thresholdResponseList,
  //   });
  //   return;
  // }
  const isTimeLimitExceeded = await checkTaskTimeLimit(pros.tasks[index].taskType);
  if (isTimeLimitExceeded) {
    return;
  }
  if (pros.tasks[index].taskType <= 9) {
    taskDetailIndex.value = index;
    showSku.value = true;
  } else if (pros.tasks[index].taskType === 10) {
    callShare({
      title: shareConfig.shareTitle,
      shareUrl: `https://shop.m.jd.com/?shopId=${pros.shopId}`,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  } else if (pros.tasks[index].taskType === 12) {
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareTitle,
      shareUrl: window.location.href,
      imageUrl: shareConfig.shareImage,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  }
};

const taskInfo = {
  5: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '关注商品',
    button: '去关注',
    buttonDIs: '已关注',
  },
  6: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/136611/18/26554/7346/625fb568E150817e6/886f863f7b0e5e57.png',
    label: '预约商品',
    button: '去预约',
    buttonDIs: '已预约',
  },
  7: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/215894/39/17895/6930/625fb569E7b0ddbd2/71a5d597757641c7.png',
    label: '加购商品',
    button: '去加购',
    buttonDIs: '已加购',
  },
  8: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/215894/39/17895/6930/625fb569E7b0ddbd2/71a5d597757641c7.png',
    label: '购买商品',
    button: '去购买',
    buttonDIs: '已购买',
  },
  9: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/107474/38/27014/7339/625fb569Ec2dbbb5b/a79a2be8591b1c76.png',
    label: '分享商品',
    button: '去分享',
    buttonDIs: '已分享',
  },
  10: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/107474/38/27014/7339/625fb569Ec2dbbb5b/a79a2be8591b1c76.png',
    label: '分享店铺',
    button: '去分享',
    buttonDIs: '已分享',
  },
  12: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/107474/38/27014/7339/625fb569Ec2dbbb5b/a79a2be8591b1c76.png',
    label: '分享活动',
    button: '去分享',
    buttonDIs: '已分享',
  },
};

const taskRule = {
  5: (info: any) => {
    if (info.optWay === 1) {
      return `每关注${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`;
    }
    return `成功关注全部商品，可获得${info.lotteryCount}次抽奖机会`;
  },
  6: (info: any) => `每预约${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`,
  7: (info: any) => {
    if (info.optWay === 1) {
      return `每加购${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`;
    }
    return `成功加购全部商品，可获得${info.lotteryCount}次抽奖机会`;
  },
  8: (info: any) => `每成功下${info.perOperateCount}单，可获得${info.perLotteryCount}次抽奖机会`,
  9: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  10: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  12: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
};
</script>

<style scoped lang="scss">
.task-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .times {
    text-align: center;
    color: #262626;
    font-size: 0.24rem;
    margin-top: 0.3rem;

    span {
      color: rgb(242, 39, 12);
    }
  }

  .content {
    height: 8.5rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;

    .task {
      background-color: #fff;
      margin-bottom: 0.1rem;
      border-radius: 0.1rem;
      padding: 0.2rem 0.3rem;
      display: flex;
      align-items: center;

      .icon {
        width: 0.83rem;
      }

      .info {
        flex: 1;
        padding: 0 0.39rem;
      }

      .name {
        font-size: 0.3rem;
        color: rgb(255, 153, 0);
      }

      .rule {
        font-size: 0.24rem;
        color: #8c8c8c;
      }

      .button {
        width: 1.36rem;
        height: 0.46rem;
        background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
        font-size: 0.2rem;
        color: rgb(255, 255, 255);
        border-radius: 0.23rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .button-dis {
        background: #ffe3e3;
      }
    }
  }
}
</style>
