<template>
  <div class="order-bk">
    <!-- <div class="close" @click="close" /> -->
    <div class="content">
      <div class="order-info" v-for="(item, index) in orderList" :key="index">
        <div class="order-list">
          <div class="order-top">
            <div class="order-id-title">订单编号</div>
            <div class="order-start-time">下单时间{{ dayjs(item.orderStartTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="order-bottom">
            <div class="order-id">{{ item.orderId }}</div>
            <div class="order-status">已完成</div>
          </div>
<!--          <div>{{ item.orderId }}</div>-->
<!--          <div>{{ item.orderAmount }}</div>-->
<!--          <div>{{ dayjs(item.orderStartTime).format('YYYY-MM-DD HH:mm:ss') }}</div>-->
        </div>
      </div>
      <div v-if="!orderList.length" class="no-data">暂无订单记录哦~</div>
    </div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject } from 'vue';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';

interface orderInfo {
  orderId: string;
  orderAmount: string;
  orderStartTime: string;
  status: string;
}

const orderList = ref([] as orderInfo[]);
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
const getMyOrder = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90202/getUserOrders');
    orderList.value = data;
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const isPreview = inject('isPreview') as boolean;
if (!isPreview) {
  getMyOrder();
}
</script>

<style scoped lang="scss">
.order-bk {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/277844/28/26153/25600/6808b256F7b4206a8/5480f6803326861c.png');
  background-size: 100%;
  width: 6.13rem;
  height: 7.68rem;
  background-repeat: no-repeat;
  position: relative;
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
  }
  .no-data {
    text-align: center;
    line-height: 3.95rem;
    font-size: 0.24rem;
    color: #814534;
  }
  .content {
    position: absolute;
    left: 50%;
    top: 1.7rem;
    transform: translate(-50%);
    width: 5.3rem;
    height: 3.95rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    .order-list {
      width: 5.02rem;
      height: 1.27rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/258840/22/22726/7884/67b83c13F5ffbabee/013d1474bb5388d6.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin: 0 auto;
      .order-top{
        width:4.86rem;
        height:0.5rem;
        display: flex;
        justify-content: space-between;
        line-height: 0.5rem;
        padding: 0 .1rem;
      }
      .order-bottom{
        width:4.86rem;
        height:0.7rem;
        display: flex;
        justify-content: space-between;
        line-height: 0.7rem;
        padding: 0 .1rem;
      }
      //padding: 0.2rem 0;
      //color: #814534;
      //display: flex;
      //align-items: center;
      //div {
      //  width: 33.33%;
      //  text-align: center;
      //  // word-break: break-all;
      //  // overflow: hidden;
      //  // text-overflow: ellipsis;
      //  // white-space: nowrap;
      //}
    }
  }
}
</style>
