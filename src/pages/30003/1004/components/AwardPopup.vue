<template>
  <div class="bk" v-if="prize.prizeType > 0">
    <div class="close" @click="close"></div>
    <img :src="prize.showImg" alt="" class="prize-img" />
    <div class="content">
      <p class="p1">恭喜您获得:</p>
      <p class="prize-name">{{ prize.prizeName }}</p>
      <div>
        <p class="p3" v-if="prize.prizeType === 5">仅中奖人可享受优惠,仅可使用一次</p>

        <p class="p3" v-if="prize.prizeType === 2">京豆已放到您的账户中 京东-我的-京豆 中查看</p>

        <p class="p3" v-if="prize.prizeType === 1">已发放到您的账户 京东-我的-我的钱包-优惠券 中查看</p>
        <p class="p3" v-if="prize.prizeType === 4">积分已发放到您的账户中 店铺会员页 中查看</p>
        <p class="p3" v-if="prize.prizeType === 6">红包已发放到您的账户中 京东-我的-我的钱包-红包 中查看</p>
        <p class="p3" v-if="prize.prizeType === 7">礼品卡需手动兑换，请根据 兑换指引 前往兑换</p>
        <p class="p3" v-if="prize.prizeType === 8">京东E卡已发放到您的账户中 京东-我的-我的钱包-礼品卡 中查看</p>
        <p class="p3" v-if="prize.prizeType === 3">实物奖品系统不能自动发放，请<span>填写邮寄地址</span></p>

        <p class="p3" v-if="prize.prizeType === 12">
          1:权益非自动发放,需首先填写权益领取信息 <br />
          2:如放弃领取权益,活动结束权益不予补发
        </p>
        <p class="p3" v-if="prize.prizeType === 9 || prize.prizeType === 10"></p>
      </div>
      <div class="btn-list">
        <div class="btn btn-left" @click="shareAct">立即分享</div>
        <div class="btn btn-right" v-if="prize.prizeType === 3" @click="saveAddress">填写地址</div>
        <div class="btn btn-right" v-else-if="prize.prizeType === 5" @click="gotoSkuPage(prize.result)">立即购买</div>
        <div class="btn btn-right" v-else-if="prize.prizeType === 7" @click="showCardNum">如何兑换</div>
        <div class="btn btn-right" v-else-if="prize.prizeType === 9 || prize.prizeType === 10" @click="exchangePlusOrAiqiyi">立即兑换</div>
        <div class="btn btn-right" v-else-if="prize.prizeType === 12" @click="savePhone">立即领取</div>
        <div class="btn btn-right" v-else @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
      </div>
    </div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoShopPage, gotoSkuPage, exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.result.result, props.prize.activityPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, showImg: props.prize.prizeImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  emits('savePhone', props.prize.userPrizeId, props.prize.result.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  height: 6.9rem;
  width: 6rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/102050/20/27655/237782/6268e9b4E40cf6e02/1ed0ccada5088734.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1rem;
  .close {
    height: 0.24rem;
    width: 0.24rem;
    position: absolute;
    right: 0.34rem;
    top: 0.34rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/143521/26/18654/387/5fd9e706E8f1594e3/ae5cc06440559585.png) no-repeat;
    background-size: 100% 100%;
  }
  .prize-img {
    height: 2rem;
    width: 2rem;
    margin: 0 auto;
  }
  .content {
    width: 5.6rem;
    height: 3.5rem;
    background-color: white;
    border-radius: 0.2rem;
    margin: 0.2rem auto 0;
    padding: 0.3rem;
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.24rem;
      font-weight: 500;
    }
    .prize-name {
      font-size: 0.36rem;
      font-weight: bold;
      margin: 0.27rem 0 0;
      text-align: center;
      color: #ff3333;
    }
    .p3 {
      font-size: 0.2rem;
      color: #b8b8b8;
      display: block;
      text-align: center;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        color: #f2270c;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-between;
      .btn {
        width: 2.4rem;
        height: 0.9rem;
        line-height: 0.9rem;
        text-align: center;
        color: white;
        font-size: 0.3rem;
        border-radius: 0.1rem;
      }
      .btn-left {
        background: linear-gradient(to right, #f2270c, #ff6320);
      }
      .btn-right {
        background: #ff9900;
      }
    }
  }
}
.thanks-join {
  width: 6rem;
  height: 6.3rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/192914/3/22012/24033/623985b9E8508c48b/019e54628504b2dc.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 4.5rem;
  .close {
    height: 0.24rem;
    width: 0.24rem;
    position: absolute;
    right: 0.34rem;
    top: 0.34rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/143521/26/18654/387/5fd9e706E8f1594e3/ae5cc06440559585.png) no-repeat;
    background-size: 100% 100%;
  }
  .btn {
    display: block;
    margin: 0 auto;
    width: 4rem;
    height: 0.76rem;
    line-height: 0.76rem;
    color: #fff;
    font-size: 0.3rem;
    border-radius: 0.38rem;
    text-align: center;
    background-color: rgb(201, 0, 26);
  }
}
</style>
