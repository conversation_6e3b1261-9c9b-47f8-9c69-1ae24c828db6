<template>
  <div class="rule-bk">
    <div class="title">
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/223708/13/38610/1228/656d7bcbFbc6cc966/09be6000d6331542.png" class="close" @click="close" />
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
       <div class="topDiv">
         <div class="type">
           <span>{{ prizeType[item.prizeType] }}</span>
           <span>{{ item.userPrizeId ? item.userPrizeId : '' }}</span>
         </div>
         <div class="realNameDiv" v-if="item.prizeType===3 && item.realName">{{ item.realName }}</div>
       </div>
        <div class="info">
          <div class="prizeImgDiv"><img :src="item.prizeImg" alt="" class="show-img" /></div>
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
            <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="status" v-if="item.prizeType === 3">
            <div class="orange" v-if="!item.deliveryStatus">待发货</div>
            <div class="black" v-else>已发货</div>
            <div class="purple" v-if="!item.deliveryStatus && !item.realName" @click="changAddress(item)">填写地址</div>
            <div class="blue" v-else-if="!item.deliveryStatus && item.realName" @click="changAddress(item)">查看地址</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 7">
            <!-- <div class="orange">待发货</div> -->
            <div class="green" @click="showCardNum(item)">如何兑换</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
            <!-- <div class="orange">待发货</div> -->
            <div class="green" @click="exchangePlusOrAiqiyi">立即兑换</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 12">
            <div class="orange" v-if="item.isFuLuWaitingReceive">待领取</div>
            <div class="black" v-else>已发放</div>
            <div class="green" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive">点击领取</div>
          </div>
          <div class="status" v-else>
            <div class="black">已发放</div>
          </div>
        </div>
        <div class="deliver" v-if="item.prizeType === 3 && item.deliveryStatus">
          <div>
            <div class="deliverText">快递公司：{{item.deliverName}}</div>
            <div class="deliverText">快递单号：{{item.deliverNo}}</div>
          </div>
          <div class="copy-btn" :copy-text="item.deliverNo">复制单号</div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import Clipboard from 'clipboard';

import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  realName: string;
  deliverName: string;
  deliverNo: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/30003/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  addressId.value = item.addressId;
  activityPrizeId.value = item.activityPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userPrizeId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 100vw;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/186658/32/42021/124786/656d7d90F77ddf559/27b511946d00c2f4.png");
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 1.16rem 0 0 0;
  min-height: 8rem;
  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.55rem;
  }

  .content {
    max-height: 9.32rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .prize {
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/236636/19/5592/1363/656d7d8dF85d915bc/9277bfa541151312.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 6.90rem;
      margin-bottom: 0.1rem;
      padding-bottom: 0.08rem;
      margin-left: calc(50% - 6.90rem / 2);
      .topDiv{
        display: flex;
        justify-content: space-between;
        padding-top: 0.16rem;
        padding-bottom: 0.08rem;
        border-bottom: 0.02rem dashed #eee;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        .type {
          color: #999999;
          font-size: 0.2rem;
          line-height: 0.2rem;
          text-align: left;
        }
        .realNameDiv{
          color: #999999;
          font-size: 0.2rem;
          line-height: 0.2rem;
          text-align: right;
        }
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.08rem;
        .prizeImgDiv{
          width: 0.74rem;
          height: 0.74rem;
          border-radius: 50%;
          background: #ff723d;
          display: flex;
          align-items: center;
          justify-content: center;
         }
        .show-img {
          height: 0.45rem;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #000000;
          }

          .time {
            color: #999999;
            font-size: 0.2rem;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;

          .black {
            color: #333333;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #3458f5;
          }
          .purple{
            color: #b24ff1;
          }
          .green{
            color: #00bb66;
          }
        }
      }

      .deliver{
        margin-left: 1.24rem;
        margin-right: 0.22rem;
        color: #999999;
        font-size: 0.2rem;
        display: flex;
        justify-content: space-between;
        .copy-btn {
          color: #0083ff;
          margin-top:0.24rem;
          font-size: 0.24rem;
        }
        .deliverText{
          margin-bottom: 0.04rem;
        }
      }
    }

    .no-data {
      background: #fff;
      border-radius: 0.26rem 0.26rem 0 0;
      text-align: center;
      line-height: 35vh;
      font-size: 0.24rem;
      color: #8c8c8c;
      margin: 0 0.22rem;
    }
  }
}
</style>
