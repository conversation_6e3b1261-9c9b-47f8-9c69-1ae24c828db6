<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/222086/5/36311/26797/6569724aFd40968f5/f1e9a266f0078fdd.png'" alt="" class="kv-img" />
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRule = true"><div>规<br>则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showMyPrize = true"><div>奖<br>品</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showDrawRecod = true"><div>抽<br>盲<br>盒<br>记<br>录</div></div>
        </div>
      </div>
    </div>
    <div class="twoDivClass select-hover" :class="{ 'on-select': selectedId === 2 }" :style="furnishStyles.blindBoxBg.value" @click="onSelected(2)">
      <div class="prizeClass">
        <div class="prizeItemClass" v-for="(item, index) in prizeListAll" :key="index">
          <div v-if="!item.isDraw">
            <img v-if="item.isSelect" :src="item.lightLogoImg" alt=""/>
            <img v-else :src="item.logoImg" alt=""/>
            <div v-if="item.isSelect" class="selectClass"></div>
            <div v-else class="noSelectClass" @click.stop="selectMhClick(item, index)"></div>
          </div>
          <div v-else>
            <img :src="item.isDrawLogoImg" alt="" />
            <div class="graySelectClass"></div>
          </div>
        </div>
      </div>
      <div class="pointsClass"><span class="pointsSpan" :style="furnishStyles.drawsNum.value">{{ drawPoints }}</span>积分可拆一次</div>
      <div class ="controlsClass">
        <div class="drawDiv" @click.stop="toast()"></div>
        <div class="changeDiv" @click.stop="toast()"></div>
      </div>
    </div>
    <div class="prizeDivAll select-hover" :class="{ 'on-select': selectedId === 4 }" @click="onSelected(4)" :style="furnishStyles.prizeShowBg.value">
      <div class="prizeDivAll1">
        <div class="prizeItemDiv" v-for="(item, index ) in prizeInfo" :key="index">
          <div class="prizeBlockDiv" v-if="item.prizeType">
            <div class="prizeImageDiv"><img alt="" :src="item.prizeImg"></div>
            <div class="prizeNameDiv">{{item.prizeName}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="shopMessageDiv select-hover" :class="{ 'on-select': selectedId === 5 }" @click="onSelected(5)" :style="furnishStyles.shopMessageBg.value">
       <div class="leftDiv">
         <div class="shopLogoImageDiv">
           <img alt="" :src="shopLogo">
         </div>
         <div class="shopNameDiv" :style="furnishStyles.shopNameColor.value" >{{shopName}}</div>
       </div>
      <div class="rightDiv" :style="furnishStyles.shopMessageGoToShop.value" @click.stop="toast()"></div>
    </div>
    <div class="winners select-hover" :style="furnishStyles.winnersBg.value" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
      <div class="winners-content">
        <div class="winner-list swiper-container" ref="swiperRef">
          <div class="swiper-wrapper" v-if="activityGiftRecords.length != 0">
            <div class="winner swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
              <div>
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" />
                <span>{{ item.nickName }}</span>
              </div>
              <span>抽中了<span class="giftNameSpan">{{ item.giftName }}</span></span>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div v-if="!isCreateImg">
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>

    <!-- 抽盲盒记录-->
    <VanPopup teleport="body" v-model:show="showDrawRecod" position="bottom">
      <DrawRecord v-if="showDrawRecod" @close="showDrawRecod = false"></DrawRecord>
    </VanPopup>

    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import DrawRecord from '../components/drawRecord.vue';
import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);

const shopName = ref('xxx自营旗舰店');
const shopLogo = ref('http://img30.360buyimg.com/popshop/jfs/t12040/167/1277706166/88856/87939c85/5a1e9842N3d659b8f.jpg');

const isLoadingFinish = ref(false);

const showRule = ref(false);
const ruleTest = ref('');

const showMyPrize = ref(false);
const showDrawRecod = ref(false);

const times = ref(0);
const drawPoints = ref(1);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 0,
  prizeName: '',
  showImg: '',
  result: {
    result: {
      planDesc: '',
    },
  },
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
// 是否有选中的盲盒
const isSelectBlindBox = ref(false);
const isSelectBlindBoxIndex = ref(-1);
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

const prizeListAll = reactive([
  {
    isDraw: false,
    isSelect: false,
    position: 1,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/229110/27/5363/14778/65698365Fac440f48/6d1b0ef37f47d253.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/235940/11/5453/17130/65697802F6890f423/5a751fbfe3d51d9e.png',
  },
  {
    isDraw: false,
    isSelect: false,
    position: 2,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/231867/38/5713/15359/65697801Fdf50e09b/a7a995973bd08b20.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/231331/16/5417/17518/656982f6F13628324/7748868813cb4847.png',
  },
  {
    isDraw: false,
    isSelect: false,
    position: 3,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/229528/35/5371/15874/65697802Fc7e74c9e/cc2562271611f4df.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/188459/23/40856/17884/656982acFcbeae41d/5e2278848d5fffc4.png',
  },
  {
    isDraw: false,
    isSelect: false,
    position: 4,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/229528/35/5371/15874/65697802Fc7e74c9e/cc2562271611f4df.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/188459/23/40856/17884/656982acFcbeae41d/5e2278848d5fffc4.png',
  },
  {
    isDraw: false,
    isSelect: false,
    position: 5,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/229110/27/5363/14778/65698365Fac440f48/6d1b0ef37f47d253.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/224763/1/5832/17130/6569802aF1f423cd8/6686a54b3bd45b8b.png',
  },
  {
    isDraw: false,
    isSelect: false,
    position: 6,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/231867/38/5713/15359/65697801Fdf50e09b/a7a995973bd08b20.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/231331/16/5417/17518/656982f6F13628324/7748868813cb4847.png',
  },
  {
    isDraw: false,
    isSelect: false,
    position: 7,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/231867/38/5713/15359/65697801Fdf50e09b/a7a995973bd08b20.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/231331/16/5417/17518/656982f6F13628324/7748868813cb4847.png',
  },
  {
    isDraw: false,
    isSelect: false,
    position: 8,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/229528/35/5371/15874/65697802Fc7e74c9e/cc2562271611f4df.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/188459/23/40856/17884/656982acFcbeae41d/5e2278848d5fffc4.png',
  },
  {
    isDraw: false,
    isSelect: false,
    position: 9,
    prizeName: '谢谢参与',
    isDrawLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/193267/18/41910/9843/65697801Fffa122cd/29edaab05f171f44.png',
    logoImg: '//img10.360buyimg.com/imgzone/jfs/t1/229110/27/5363/14778/65698365Fac440f48/6d1b0ef37f47d253.png',
    lightLogoImg: '//img10.360buyimg.com/imgzone/jfs/t1/224763/1/5832/17130/6569802aF1f423cd8/6686a54b3bd45b8b.png',
  },
]);
const activityGiftRecords = reactive([
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
]);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

// 选择盲盒
const selectMhClick = (item:any, index:number) => {
  for (let i = 0; i < prizeListAll.length; i++) {
    prizeListAll[i].isSelect = false;
  }
  prizeListAll[index].isSelect = true;
  isSelectBlindBox.value = true;
  isSelectBlindBoxIndex.value = index + 1;
};
// 换一批
const changeMhDataClick = () => {
  prizeListAll.sort(() => Math.random() - 0.5);
  for (let i = 0; i < prizeListAll.length; i++) {
    prizeListAll[i].position = i + 1;
    prizeListAll[i].isSelect = false;
    prizeListAll[i].isDraw = false;
  }
  isSelectBlindBoxIndex.value = -1;
  isSelectBlindBox.value = false;
};
// 拆盲盒
const startPlay = () => {
  if (!isSelectBlindBox.value) {
    showToast('请先选中心仪盲盒');
    return;
  }
  for (let i = 0; i < prizeListAll.length; i++) {
    if (prizeListAll[i].position === isSelectBlindBoxIndex.value) {
      if (prizeListAll[i].isDraw) {
        showToast('该盲盒已经开过了哦');
        return;
      }
    }
  }
  // 假设后端返回的中奖索引是0
  award.value = {
    prizeType: 0,
    prizeName: '谢谢参与',
    showImg: '',
    result: {
      result: {
        planDesc: '',
      },
    },
    activityPrizeId: '',
    userPrizeId: '',
  };
  showAward.value = true;

  for (let i = 0; i < prizeListAll.length; i++) {
    if (prizeListAll[i].position === isSelectBlindBoxIndex.value) {
      prizeListAll[i].isDraw = true;
    }
  }

};

const myLucky = ref();
const startCallback = async () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = Math.floor(Math.random() * 8);
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: {
        result: {
          planDesc: '',
        },
      },
      activityPrizeId: '',
      userPrizeId: '',
    };

    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index);
  }, 2000);
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};
const toast = () => {
  showToast('活动预览，仅供查看');
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showAward.value = false;
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  console.log(data, type, 'data=============');
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    drawPoints.value = data.points;
    prizeInfo.splice(0);
    if (data.prizeList.length) {
      for (let a = 0; a < data.prizeList.length; a++) {
        if (data.prizeList[a].prizeImg) {
          prizeInfo.push(data.prizeList[a]);
        }
      }
    }
    ruleTest.value = data.rules;
    shopName.value = data.shopName;
    shopLogo.value = data.logoUrl;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'shop') {
    shopName.value = data;
  } else if (type === 'shopLogo') {
    shopLogo.value = data;
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeInfo.splice(0);
    for (let a = 0; a < activityData.prizeList.length; a++) {
      if (activityData.prizeList[a].prizeImg) {
        prizeInfo.push(activityData.prizeList[a]);
      }
    }
    ruleTest.value = activityData.rules;
    shopName.value = activityData.shopName;
    shopLogo.value = activityData.logoUrl;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  if (activityGiftRecords.length > 4) {
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: {
          delay: 1000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
        direction: 'vertical',
        loop: true,
        slidesPerView: 5,
        loopedSlides: 8,
        centeredSlides: true,
      });
    });
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.37rem 0rem 0.3rem 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
     width: 0.42rem;
    padding: 0.26rem 0;
    margin-bottom: 0.1rem;
    font-size: 0.24rem;
    text-align: center;
    border-radius: 0.22rem 0 0 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.twoDivClass{
  position: relative;
  margin-top: 2.24rem;
  //margin-top: -20.8rem;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/234948/2/5193/54643/65697a81Fb42727f4/bf1b323d81d75ac1.png");
  width: 5.76rem;
  height: 7.93rem;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-left: calc(50% - 5.76rem / 2);
  display: flex;
  align-items: center;
  flex-direction: column;
  .prizeClass{
    width: 3.86rem;
    height: 5.44rem;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    margin-top: 0.9rem;
    .prizeItemClass{
      flex: 1;
      .selectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/237000/27/5276/2892/65697800F1cd69330/3d3d413da692bff5.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 1.11rem;
        height: 0.49rem;
      }
      .noSelectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/224061/3/5097/2766/65697800F1c398617/4b73537eb60825b6.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 1.11rem;
        height: 0.49rem;
      }
      .graySelectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/226172/37/5583/2225/65697800Fadc3d27e/d313cf6108781f1a.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 1.11rem;
        height: 0.49rem;
      }
      img {
        width: 0.95rem;
        height: 1.18rem;
      }
    }
  }
  .pointsClass{
    color:#fff;
    font-size: 0.23rem;
    .pointsSpan{
      color:#fdd746;
    }
  }
  .controlsClass{
    display: flex;
    margin-left: 0.72rem;
    margin-top: 0.24rem;
    .drawDiv{
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/221322/4/34586/4579/65697802Fc116ef36/a1e955834305ab5d.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 1.89rem;
      height: 0.63rem;
    }
    .grayDrawDiv{
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/207978/9/46145/4219/65697800F8ce12f59/bda4216a6fb64294.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 1.89rem;
      height: 0.63rem;
    }
    .changeDiv{
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/208490/15/30606/4732/65697802Fe7407932/941ad710180f0afe.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 1.89rem;
      height: 0.63rem;
      margin-left: 0.07rem;
    }
  }
}
.prizeDivAll{
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/185525/18/42049/9209/65697802F27cd9d2d/5b3aed5e64d99e3f.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 5.86rem;
  height: 2.70rem;
  position: relative;
  margin-left: calc(50% - 6.1rem / 2);
  margin-top: -0.1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  .prizeDivAll1{
    width: 4.74rem;
    overflow-x: scroll;
    display: flex;
  }
  .prizeItemDiv{
    .prizeBlockDiv{
      width: 1.27rem;
      height: 1.59rem;
      border: 2px solid #000000;
      border-radius: 0.08rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 0.07rem;
      justify-content: space-between;
      background: #4cbaf7;
      .prizeImageDiv{
        background: #fff;
        width: 100%;
        display: flex;
        justify-content:center;
        padding: 0.08rem 0;
        border-radius: 0.08rem 0.08rem 0 0;
        img{
          height: 0.7rem;
        }
      }
      .prizeNameDiv{
        text-align: center;
        color: #fff;
        font-size: 0.2rem;
        height: 0.5rem;
        display:flex;
        align-items:center;
        justify-content:center;
        margin-bottom: 0.05rem;
      }
    }
  }
}
.shopMessageDiv{
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/237654/21/5551/2156/656d44b9F44e5361a/86762cd6257da9bb.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 7.50rem;
  height: 1.34rem;
  position: relative;
  margin-top: 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.60rem 0 0.54rem;
  .leftDiv{
    display: flex;
    img{
      height: 0.64rem;
    }
    .shopNameDiv{
      margin-left: 0.18rem;
      color: #333333;
      font-size: 0.23rem;
    }
  }
  .rightDiv{
    background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/224419/1/5665/4422/656d44b9F82753349/5a4077a1bee140d0.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 1.40rem;
    height: 0.60rem;
  }
}
.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.88rem;
  height: 7.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1.1rem;
  position: relative;

  .winners-content {
    width: 5.98rem;
    height: 6.2rem;
    border-radius: 0.1rem;
    margin: 0 auto;
    .giftNameSpan{
      color: #ff3333;
    }
  }
}

.bottom-div {
  padding-top: 0.5rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem;
  background-color: #fff;
  margin-bottom: 0.05rem;
  border-radius: 0.04rem;
  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none !important;
}
</style>
