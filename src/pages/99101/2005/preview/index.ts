import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/291387/33/3400/2811875/681ca258F778a24db/9f343f78812a9376.png',
  actBg: '',
  actBgColor: '#e8e3da',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/272298/38/6555/9191/67dbca97Fbb27ecb0/b96a79af983501d9.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/284591/9/5272/9427/67dbca97Fc11ef66d/355cd996e48ec077.png',
  stepBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/290741/5/3891/5731/681ca254F9d96fd48/1cf30c91180f97f9.png',
  stepBtnSelectBg: '//img10.360buyimg.com/imgzone/jfs/t1/293226/28/3877/8091/681ca255Fe3eb3b3e/6a8dbea1c1ef0df2.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/259850/19/21830/135972/67b6c86bFe24ddd6d/73efdd3021a0e2a8.png',
  exchangeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/300274/35/4157/14658/681ca254F884b07f8/b81dce6d55c879f6.png',
  skuListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/295131/35/3246/84485/681ca254F387aabef/6cd5bdb6210f5df5.png',
  skuTitleColor: '#f8f1e0',
  goSkuBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/302174/20/2964/33321/681ca256Fddfcce69/b549f1f75ad317d2.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/303126/20/3925/38197/681ca506F2c303761/ede0f36ccfdf77b3.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/302370/26/4105/5905/681ca506F31f7441c/c21b30048be4bc5d.jpg',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/295636/15/4629/47178/681ca506F93883747/eb4f6353533b839a.jpg',
  ruleImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/299691/34/4193/274815/681ca255Fbbe57856/4f4ecf3c82f63551.png',
  joinMemberBk: '//img10.360buyimg.com/imgzone/jfs/t1/286280/3/4114/119854/681ca7cbF51afbc76/e559551d1307dead.png',
  ruleBk: '//img10.360buyimg.com/imgzone/jfs/t1/295432/39/3845/57498/681ca7ccFd8d2b454/bea8b254dc788a7f.png',
  ruleTextColor: '#f02816',
  myPrizeTextColor: '#000',
  myPrizeBk: '//img10.360buyimg.com/imgzone/jfs/t1/300413/40/4119/62377/681ca7caF8207b05f/961d01a6d85c5215.png',
  confirmPrizeBk: '//img10.360buyimg.com/imgzone/jfs/t1/297580/27/4709/28647/681ca7caFa7de246c/e57c357cdc951115.png',
  receiveSuccessBk: '//img10.360buyimg.com/imgzone/jfs/t1/298984/2/2156/113283/681ca7ccFf551aefc/67504f04f03b2d53.png',
  saveAddressBk: '//img10.360buyimg.com/imgzone/jfs/t1/293032/1/4162/81931/681ca7cbF4ac45132/7d47ee5e154ff13e.png',
};
const actData2 = {
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/8014/27/27673/8015/66e3f17eFd57d2e61/7aa4b43696214531.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/5404/18/24418/39038/66e3f17fFda46171c/a5d709264c89365f.png',
  rules: '测试',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/166070/15/46885/27256/66e3f17fF139817f3/ed6b59e0774f1a11.png',
  endTime: '2025-03-22 23:59:59',
  crowdBag: '',
  prizeDay: [

  ],
  shopName: '伊利母婴京东自营旗舰店',
  rangeDate: [
    '2025-02-20 00:00:00',
    '2025-03-22 23:59:59',
  ],
  startTime: '2025-02-20 00:00:00',
  threshold: 1,
  activityId: '1892419731947462658',
  gradeLabel: [

  ],
  limitOrder: 1,
  seriesPrizeList: [
    {
      seriesName: '1转2',
      seriesPic: 'https://img10.360buyimg.com/imgzone/jfs/t1/273077/25/6616/94048/67dbca97Ff2025ba0/f45e102c8d6960a9.jpg',
      beforeOptions: [
        1,
      ],
      afterOptions: 2,
      prizeList: [
        {
          prizeKey: 's250320162233349723',
          prizeType: 3,
          dayLimitType: 1,
          dayLimit: 1,
          prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/262756/13/28314/7825/67c80318F618f9044/ae3ef908a85ce0eb.png',
          activityIds: [],
          createTime: 1742458953000,
          quantityAvailable: 3,
          quantityFreeze: 0,
          quantityPreDeliver: 0,
          quantityRemain: 3,
          quantityTotal: null,
          shopId: null,
          skuCode: 's250320162233349723',
          skuDetails: null,
          skuMainPicture: 'https://img10.360buyimg.com/imgzone/jfs/t1/262756/13/28314/7825/67c80318F618f9044/ae3ef908a85ce0eb.png',
          skuName: 'ceshisce',
          version: 1,
          wmsCode: null,
          prizeName: 'ceshisce',
          unitPrice: 1,
          unitCount: 1,
          sendTotalCount: 1,
          step: '1',
        },
      ],
      beforeSkuList: [
        {
          period: '一段',
          periodSort: 1,
          potNum: 1,
          skuId: '1234567',
          skuIdSort: 1,
        },
      ],
      afterSkuList: [
        {
          period: '二段',
          periodSort: 2,
          potNum: 2,
          skuId: '1',
          skuIdSort: 1,
        },
      ],
      stepSetting: [
        {
          step: 1,
          minPotNum: 1,
          maxPotNum: 5,
          stepImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/283460/16/6766/241821/67dd2967Ffa654bbf/cf37b8175d825990.png',
        },
      ],
    },
  ],
  shareTitle: '满额赢好礼，超多惊喜大奖等你来领！',
  joinEndTime: '',
  shareStatus: 1,
  activityName: '满额阶梯礼-2025-02-20',
  orderEndTime: '2025-03-22 23:59:59',
  templateCode: 2001,
  joinStartTime: '',
  joinTimeRange: [

  ],
  supportLevels: '1,2,3,4,5',
  orderStartTime: '2025-02-20 00:00:00',
  limitJoinTimeType: 0,
  actTotalReceiveCount: '',
  actLimitActTotalReceiveCount: 0,
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员转段赠好礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
