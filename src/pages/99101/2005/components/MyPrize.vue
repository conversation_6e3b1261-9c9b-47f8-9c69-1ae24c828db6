<template>
  <div class="rule-bk" :style="furnishStyles.myPrizeBk.value">
    <div class="content" :style="furnishStyles.myPrizeTextColor.value">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="time">{{ item.drawDate }}</div>
        <div class="name">{{ item.prizeName }}</div>
        <div class="status" >
          <div class="orange" v-if="item.prizeType === 3 " @click="toSaveAddress(item.addressId)">更改地址</div>
          <div class="green">{{ item.stateRemark }}</div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>
  <div class="close" @click="close"></div>
  <VanPopup teleport="body" v-model:show="saveAddressPopup" :closeOnClickOverlay="false">
    <SaveAddress v-if="saveAddressPopup" @close="closeSaveAddress" :userPrizeId="userPrizeId"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import furnishStyles from '../ts/furnishStyles';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: string;
  prizeImg: string;
  prizeName: string;
  drawDate: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  winStatus: number;
  realName: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99101/query/prize');
    // res.data.forEach((item: Prize) => {
    //   item.createTime = dayjs(item.createTime).format('YYYY-MM-DD');
    // });
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const saveAddressPopup = ref(false);
const userPrizeId = ref('');

const toSaveAddress = (id: string) => {
  userPrizeId.value = id;
  saveAddressPopup.value = true;
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, prizeImg });
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  saveAddressPopup.value = false;
  emits('close');
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 6.03rem;
  height: 6.9rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.7rem;
  .content {
    height: 4.4rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    font-weight: 500;
    white-space: pre-wrap;
    padding: 0 0.3rem;

    .prize {
      padding: 0.2rem 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .time,
      .name,
      .status {
        width: 30%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .orange {
          width: 1.2rem;
          height: 0.36rem;
          text-align: center;
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/262746/30/22549/1199/67b84101Fef32e01e/f4d27d89ae36b9a5.png);
          background-size: 100% 100%;
          margin: 0 auto;
          margin-left: 0.3rem;
          color: #fff;
          margin-bottom: 0.1rem;
        }
      }
    }

    .no-data {
      text-align: center;
      line-height: 3.95rem;
      font-size: 0.24rem;
      color: #814534;
    }
  }
}
.close {
  width: 0.6rem;
  height: 0.6rem;
  background: url('../assets/closeBtn.png') no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
