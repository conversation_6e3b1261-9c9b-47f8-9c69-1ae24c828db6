<template>
  <div class="rule-bk" :style="furnishStyles.joinMemberBk.value">
    <div class="btn" @click="join"></div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { inject } from 'vue';
import furnishStyles from '../ts/furnishStyles';

const baseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const join = () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 6.03rem;
  height: 6.9rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.1rem;
  padding-left: 0.3rem;
  padding-right: 0.3rem;
  .btn {
    position: absolute;
    top: 5.3rem;
    left: 50%;
    transform: translate(-50%);
    width: 3.6rem;
    height: 0.6rem;
    cursor: pointer;
  }
}
.close {
  width: 0.6rem;
  height: 0.6rem;
  background: url('../assets/closeBtn.png') no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
