<template>
  <div class="rule-bk" :style="furnishStyles.confirmPrizeBk.value">
    <div class="des">奖品限领一个，领取后不可更换</div>
    <div class="content">
      <div v-for="(item, index) in stepPrize" :key="index" class="prize">
       <div class="title">
         <div class="stepGiftName">{{item.stepGiftName}}</div>
       </div>
        <img class="stepImage" :src="item.stepImage" alt="">
        <img @click="exChange(item)" class="exchangeBtn" v-if="item.state === '0'" src="//img10.360buyimg.com/imgzone/jfs/t1/282483/4/8854/11851/67e24321F1f5b3c09/920897062e6cd10e.png" alt="">
        <img class="exchangeBtn" v-else src="//img10.360buyimg.com/imgzone/jfs/t1/275055/5/9676/12706/67e24322F788fcc28/bdd3961b2ade5d4e.png" alt="">
      </div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <VanPopup teleport="body" v-model:show="saveAddressPopup" :closeOnClickOverlay="false">
    <SaveAddress v-if="saveAddressPopup" :stepId=stepId :delay=delay @close="saveAddressPopup=false" @upLoadData="emits('upLoadData')" @success=" emits('success')" ></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, reactive, ref } from 'vue';
import SaveAddress from './SaveAddress.vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import furnishStyles from '../ts/furnishStyles';

const emits = defineEmits(['close', 'success', 'upLoadData']);
const props = defineProps(['stepPrize']);
const saveAddressPopup = ref(false);
const stepId = ref('');
const delay = ref('');

const draw = async (item) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/99101/prize/draw', {
      stepId: item.stepId,
    });
    if (item.delay) {
      emits('success');
    } else {
      showToast('领取成功');
    }
    emits('upLoadData');
    emits('close');
    closeToast();
  } catch (error) {
    showToast(error.message);
    setTimeout(() => {
      emits('upLoadData');
      emits('close');
    }, 1000);
  }
};

const exChange = (item: any) => {
  if (item.hasGet === '1') {
    stepId.value = item.stepId;
    delay.value = item.delay;
    saveAddressPopup.value = true;
    emits('close');
  } else {
    draw(item).then();
  }
};
const close = () => {
  emits('close');
};

const prizes = reactive([]);

</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 6.03rem;
  height: 6.16rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 0.5rem;
  .des {
    text-align: center;
    font-size: 0.16rem;
    color: #f93a15;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
  }

  .content {
    height: 4.7rem;
    font-size: 0.24rem;
    font-weight: 500;
    white-space: pre-wrap;
    display: flex;
    align-items: center;
    padding: 0 0.3rem;
    justify-content: space-around;
    .prize {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .title {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 0.6rem;
        .stepGiftName {
          max-height: 0.6rem;
          line-height: 0.3rem;
          width: 2.5rem;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          text-align: center;
          color: #f93a15;
          font-weight: 600;
          font-size: 0.24rem;
        }
      }

      .stepImage {
        margin:0.2rem 0;
        width: 2.2rem;
        height: 2.2rem;;
      }
      .exchangeBtn {
        width: 2.18rem;
        height: 0.62rem;
      }
    }

    .no-data {
      text-align: center;
      line-height: 4.8rem;
      font-size: 0.24rem;
      color: #814534;
    }
  }
}
</style>
