<template>
  <div class="rule-bk" :style="furnishStyles.ruleBk.value">
    <div class="content" :style="furnishStyles.ruleTextColor.value">{{ rule }}</div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import furnishStyles from '../ts/furnishStyles';

const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 6.06rem;
  height: 8.11rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.7rem;
  padding-left: 0.4rem;
  padding-right: 0.4rem;

  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
    // background:red;
  }

  .content {
    height: 4.6rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #f02816;
    padding: 0 0.15rem;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
