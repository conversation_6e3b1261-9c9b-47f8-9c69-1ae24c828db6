import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/282117/4/5955/195682/67dbca95Fef5fdb70/523bb0fcd6f5ad10.jpg',
  actBg: '', // 主页背景图
  actBgColor: '#e8e3da', // 主页背景色
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/272298/38/6555/9191/67dbca97Fbb27ecb0/b96a79af983501d9.png', // 活动规则按钮
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/284591/9/5272/9427/67dbca97Fc11ef66d/355cd996e48ec077.png', // 我的奖品按钮
  stepBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/279329/35/8244/9492/67e106e0Fba8a4ae5/5448de550cb8e245.png', // 门槛按钮
  stepBtnSelectBg: '//img10.360buyimg.com/imgzone/jfs/t1/281358/29/8517/11485/67e106e0Fa82fd4c7/a08c36c277f76846.png', // 门槛按钮选中
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/259850/19/21830/135972/67b6c86bFe24ddd6d/73efdd3021a0e2a8.png', // 奖品背景图
  prizeBorder: '//img10.360buyimg.com/imgzone/jfs/t1/260947/26/22002/3870/67b6c869F9d2e191b/5bd1dd8e75f0a2e7.png',
  exchangeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/277910/38/10109/19165/67e35787F6134ea21/96ad0e0d576adbaf.png', // 兑换按钮
  skuListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/80196/10/27770/128910/66dff8c1Fd8dd7d8f/666f84f37a8a83cf.png', // 商品列表背景图
  skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/97264/18/48609/13072/66e12e18Fd2700ee4/5cc9c135ed83bb7d.png', // 商品背景图
  skuTitleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/243580/22/16339/2665/66dff8c2Fe83e05c4/8f5a41ccc7212189.png', // 商品标题背景图
  skuTitleColor: '#f8f1e0', // 商品标题颜色
  goSkuBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/156269/19/45386/5947/66dff8c3F6a3f18a8/dc58e86af778aa79.png', // 去商品按钮
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  ruleImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/280435/17/8434/466804/67e0f3e3Fa32c7b7c/3fe83fae60bb9eab.png',
};
const actData2 = {
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/8014/27/27673/8015/66e3f17eFd57d2e61/7aa4b43696214531.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/5404/18/24418/39038/66e3f17fFda46171c/a5d709264c89365f.png',
  rules: '测试',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/166070/15/46885/27256/66e3f17fF139817f3/ed6b59e0774f1a11.png',
  endTime: '2025-03-22 23:59:59',
  crowdBag: '',
  prizeDay: [

  ],
  shopName: '伊利母婴京东自营旗舰店',
  rangeDate: [
    '2025-02-20 00:00:00',
    '2025-03-22 23:59:59',
  ],
  startTime: '2025-02-20 00:00:00',
  threshold: 1,
  activityId: '1892419731947462658',
  gradeLabel: [

  ],
  limitOrder: 1,
  seriesPrizeList: [
    {
      seriesName: '1转2',
      seriesPic: 'https://img10.360buyimg.com/imgzone/jfs/t1/273077/25/6616/94048/67dbca97Ff2025ba0/f45e102c8d6960a9.jpg',
      beforeOptions: [
        1,
      ],
      afterOptions: 2,
      prizeList: [
        {
          prizeKey: 's250320162233349723',
          prizeType: 3,
          dayLimitType: 1,
          dayLimit: 1,
          prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/262756/13/28314/7825/67c80318F618f9044/ae3ef908a85ce0eb.png',
          activityIds: [],
          createTime: 1742458953000,
          quantityAvailable: 3,
          quantityFreeze: 0,
          quantityPreDeliver: 0,
          quantityRemain: 3,
          quantityTotal: null,
          shopId: null,
          skuCode: 's250320162233349723',
          skuDetails: null,
          skuMainPicture: 'https://img10.360buyimg.com/imgzone/jfs/t1/262756/13/28314/7825/67c80318F618f9044/ae3ef908a85ce0eb.png',
          skuName: 'ceshisce',
          version: 1,
          wmsCode: null,
          prizeName: 'ceshisce',
          unitPrice: 1,
          unitCount: 1,
          sendTotalCount: 1,
          step: '1',
        },
      ],
      beforeSkuList: [
        {
          period: '一段',
          periodSort: 1,
          potNum: 1,
          skuId: '1234567',
          skuIdSort: 1,
        },
      ],
      afterSkuList: [
        {
          period: '二段',
          periodSort: 2,
          potNum: 2,
          skuId: '1',
          skuIdSort: 1,
        },
      ],
      stepSetting: [
        {
          step: 1,
          minPotNum: 1,
          maxPotNum: 5,
          stepImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/283460/16/6766/241821/67dd2967Ffa654bbf/cf37b8175d825990.png',
        },
      ],
    },
  ],
  shareTitle: '满额赢好礼，超多惊喜大奖等你来领！',
  joinEndTime: '',
  shareStatus: 1,
  activityName: '满额阶梯礼-2025-02-20',
  orderEndTime: '2025-03-22 23:59:59',
  templateCode: 2001,
  joinStartTime: '',
  joinTimeRange: [

  ],
  supportLevels: '1,2,3,4,5',
  orderStartTime: '2025-02-20 00:00:00',
  limitJoinTimeType: 0,
  actTotalReceiveCount: '',
  actLimitActTotalReceiveCount: 0,
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员转段赠好礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
