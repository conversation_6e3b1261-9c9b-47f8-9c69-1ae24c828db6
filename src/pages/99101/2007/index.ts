import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // thresholdPopup: JudgmentConditions,
  // backActRefresh: true,
};

const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/275084/6/5467/225938/67d9482fF846c055f/a0e2d7639a540437.png',
  actBg: '',
  actBgColor: '#e8e3da',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/228105/27/33270/2446/67b6c86aFfd65e0a7/3cb2aed142c88a15.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/259477/23/22154/2504/67b6c86bFe2fb5b0e/e0dd5dd9ed8969ad.png',
  orderBtn: '//img10.360buyimg.com/imgzone/jfs/t1/258068/14/22310/2339/67b6c867F71ff5bb6/8f9f40158a7be60f.png',
  userInfoBg: '//img10.360buyimg.com/imgzone/jfs/t1/252918/37/22664/42952/67b6c869F855546ad/54e323eb5bf6c8d2.png',
  userInfoColor: '#97653c',
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/271852/11/5473/45669/67d94818F89985d13/7945070671e2045c.png',
  stepBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/279233/11/5462/2171/67d94818F86c6f7ba/04bab3c9f0698b51.png',
  stepBtnSelectBg: '//img10.360buyimg.com/imgzone/jfs/t1/282655/2/4871/2174/67d94818Fe9be6c1c/aae3083330883627.png',
  stepTextColor: '#e0362a',
  stepTextColorShadow: '#fff',
  prizeListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/271740/20/5442/56239/67d94818F087c570e/2a6eeb4f997dce30.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/231457/4/37577/7511/67b6fe56F667a4414/1cbdbb15d8655b25.png',
  exchangeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/264505/37/22187/4140/67b6c869F1a12ff97/997d4d0411856a73.png',
  skuListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/80196/10/27770/128910/66dff8c1Fd8dd7d8f/666f84f37a8a83cf.png',
  skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/97264/18/48609/13072/66e12e18Fd2700ee4/5cc9c135ed83bb7d.png',
  skuTitleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/243580/22/16339/2665/66dff8c2Fe83e05c4/8f5a41ccc7212189.png',
  skuTitleColor: '#f8f1e0',
  goSkuBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/156269/19/45386/5947/66dff8c3F6a3f18a8/dc58e86af778aa79.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
};

init(config).then(({ baseInfo, pathParams, decoData, userInfo }) => {
  // 设置页面title
  console.log(baseInfo, 'baseInfo基础信息');
  document.title = baseInfo?.activityName || '满额阶梯礼';
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.provide('userInfo', userInfo);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
