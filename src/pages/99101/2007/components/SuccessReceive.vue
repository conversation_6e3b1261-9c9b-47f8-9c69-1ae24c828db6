<template>
  <div class="rule-bk" :style="furnishStyles.receiveSuccessBk.value">
    <div class="btn" @click="join"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { inject } from 'vue';
import furnishStyles from '../ts/furnishStyles';

const baseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const join = () => {
  emits('close');
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 6.00rem;
  height: 7.4rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.1rem;
  padding-left: 0.3rem;
  padding-right: 0.3rem;

  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
    // background-color: red;
  }

  .btn {
    position: absolute;
    top: 4.4rem;
    left: 50%;
    transform: translate(-50%);
    width: 3.0rem;
    height: 0.6rem;
    cursor: pointer;
    // background-color: red;
  }
}
</style>
