<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="kv" >
      <img :src="furnish.actBg" alt="" />
      <div class="btn-list">
        <img :src="furnish.ruleBtn" alt="" @click="showRulePopup" />
        <img :src="furnish.myPrizeBtn" alt="" @click="myPrizePopup = true" />
      </div>
    </div>
    <div class="series-list-all" v-if="seriesPrizeList.find(option => option.seriesPic)">
      <div class="series-list" v-for="(item, index) in seriesPrizeList" :key="index">
        <div class="prize-item" >
          <img :src="item.seriesPic" alt="" class="prize-img" />
          <img v-if="item.state === '0' " :src="furnish.exchangeBtn" alt="" class="exchange-btn" @click="exchangeClick(item.steps)" />
          <!-- @click="toast(item.state)" -->
          <img v-else :src="furnish.exchangeBtn" alt="" class="exchange-btn gray" @click="toast(item.state)"  />
        </div>
      </div>
    </div>

    <div class="sku-content" :style="furnishStyles.skuListBg.value" v-if="showSkuList.length">
      <div class="title">
        <div class="tabBox">
          <div  v-for="(item, index) in showSkuList"
                :key="index"
                class="tabItem"
                :style="skuActStep==index? {...furnishStyles.stepBtnSelectBg.value, color: '#fff'} :{...furnishStyles.stepBtnBg.value,color: '#897faa'}"
                @click="changeSkuActStep(index)"
          >
            {{item.period}}
          </div>
        </div>
      </div>
      <div class="sku-sc">
        <div class="sku-list">
          <div class="sku-item" v-for="(item, index) in showSkuList[skuActStep].skuList" :key="index" :style="furnishStyles.skuBg.value">
            <div class="sku-text" :style="furnishStyles.skuTitleBg.value">{{ item.skuName }}</div>
            <img :src="item.skuImg" alt="" class="sku-img" />
            <img :src="furnish.goSkuBtn" alt="" class="btn" @click="gotoSkuPage(item.skuId)" />
          </div>
        </div>
      </div>
    </div>
<!--    <img style=" margin: 0 auto;width: 7.0rem"  :src="furnish.ruleImg" alt="" />-->
  </div>

  <VanPopup teleport="body" v-model:show="rulePopup">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup" :closeOnClickOverlay="false">
    <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false" @showCardNum="showCardNum"></MyPrize>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="saveAddressPopup" :closeOnClickOverlay="false">
    <SaveAddress v-if="saveAddressPopup" @upLoadData="getMain" @close="saveAddressPopup = false" :userPrizeId="awardPrize.userPrizeId" @success="successReceivePopup = true"></SaveAddress>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="copyCardPopup" :closeOnClickOverlay="false">
    <CopyCard @close="copyCardPopup = false" :detail="cardDetail"></CopyCard>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="joinPopup" :closeOnClickOverlay="false">
    <OpenCard @close="joinPopup = false"></OpenCard>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="successReceivePopup" :closeOnClickOverlay="false">
    <SuccessReceive @close="successReceivePopup = false;"></SuccessReceive>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="confirmPopupShow" :closeOnClickOverlay="false">
    <ConfirmPopup @upLoadData="getMain" @close="confirmPopupShow = false" :stepPrize="stepPrize" @success="successReceivePopup = true" ></ConfirmPopup>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import furnishStyles, { furnish, taskRequestInfo } from './ts/furnishStyles';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage } from '@/utils/platforms/jump';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import SaveAddress from './components/SaveAddress.vue';
import OpenCard from './components/OpenCard.vue';
import CopyCard from './components/CopyCard.vue';
import ConfirmPopup from './components/ConfirmPopup.vue';
import SuccessReceive from './components/SuccessReceive.vue';
import { UserInfo } from '@/utils/products/types/UserInfo';
import useThreshold from '@/hooks/useThreshold';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const userInfo = inject('userInfo') as UserInfo;

const isLoadingFinish = ref(false);
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const saveAddressPopup = ref(false);
const successReceivePopup = ref(false);
const joinPopup = ref(false);
const confirmPopupShow = ref(false);
const confirmPopup = ref(false);
const series = ref<any[]>({});
const stepPrize = ref<any[]>([]);
// 展示活动规则，首次获取规则
const ruleText = ref('');
const skuActStep = ref(0);
// 展示卡号卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  prizeImg: '',
});

const seriesPrizeList = ref<any[]>([]);
const showSkuList = ref<any[]>([]);
const pageInfo = reactive({
  pageNum: 1,
  pageSize: 10,
  pagesAll: 0,
});

const changeSkuActStep = (idx: number) => {
  skuActStep.value = idx;
};
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error) {
    console.error();
  }
};
const checkMember = () => {
  if (baseInfo.thresholdResponseList.length) {
    joinPopup.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return false;
  }
  return true;
};
const toast = (state:any) => {
  if (checkMember()) {
    if (state === '1') {
      showToast('您已领取过该奖品!');
    } else if (state === '2') {
      showToast('您来晚了,奖品已发完!');
    } else if (state === '3') {
      showToast('您暂无资格兑换该奖品!');
    }
  }
};

// 获取曝光商品
const getExposureSku = async (seriesId: string) => {
  try {
    const res = await httpRequest.post('/99101/query/goods', {
      seriesId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    });
    closeToast();
    showSkuList.value.push(...res.data);
    pageInfo.pagesAll = res.data.total;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const getMain = async () => {
  try {
    const res = await httpRequest.post('/99101/query/series');
    closeToast();
    seriesPrizeList.value = res.data || [];
  } catch (error) {
    closeToast();
  }
};
const exchangeClick = (item) => {
  if (checkMember()) {
    stepPrize.value = item;
    confirmPopupShow.value = true;
  }
};
const loadMore = async () => {
  pageInfo.pageNum++;
  await getExposureSku(skuActStep.value);
};

const showCardNum = (result: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  copyCardPopup.value = true;
};

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getMain();
    await getExposureSku(1);
    isLoadingFinish.value = true;
    checkMember();
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  min-height: 100vh;
  position: relative;
  padding-bottom: 0.35rem;
  .kv {
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .btn-list{
      position: absolute;
      top: 3.2rem;
      right: 0rem;
      img {
        display: flex;
        justify-content: flex-end;
        width: 0.54rem;
      }
    }
  }
  .series-list-all{
    margin-top: -0.7rem;
  }
  .series-list {
    width: 7.1rem;
    margin: 0.2rem auto 0.5rem;
    .prize-item {
      position: relative;
      .prize-img {
        width: 7.1rem;
        height: auto;
      }
      .exchange-btn {
        position: absolute;
        bottom: -0.3rem;
        left: 50%;
        width: 3.56rem;
        height: 1.19rem;
        transform: translateX(-50%);
      }
    }
  }
  .sku-content {
    position: relative;
    width: 7.00rem;
    height: 11.50rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
    padding-top: 1.4rem;
    .title {
      width: 6.4rem;
      // height: 1.2rem;
      margin: auto auto 0.4rem auto;
      overflow-x: scroll;
      .tabBox {
        display: flex;
        align-items: center;
        width: max-content;
        .tabItem {
          white-space: nowrap;
          flex: 1;
          margin-right: 0.1rem;
          width: 2.08rem;
          height: 0.54rem;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #de2929;
          font-weight: bold;
          font-size: 0.24rem;
          // text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
        }
      }
    }
    .step-sc {
      width: 6.2rem;
      margin: 0 auto;
      overflow-x: auto;
      overflow-y: hidden;
      .step-list {
        width: fit-content;
        min-width: 6.2rem;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        height: 1.15rem;
      }
    }
    .sku-sc {
      overflow-y: scroll;
      height: 9.6rem;
    }
    .sku-list {
      width: 6.2rem;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .sku-item {
        position: relative;
        width: 3.04rem;
        height: 2.80rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-bottom: 0.3rem;
        display: flex;
        align-items: center;
        // justify-content: center;
        flex-direction: column;
      }
      .sku-img {
        width: 1.6rem;
        height: 1.6rem;
        border-radius: 0.2rem;
      }
      .sku-text {
        // position: absolute;
        // left: 0.1rem;
        // top: 0;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 3.0rem;
        // height: 0.48rem;
        white-space: nowrap;
        font-size: 0.18rem;
        line-height: 0.48rem;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        padding-left: 0.1rem;
        padding-right: 0.1rem;
        font-weight: bold;
      }
      .btn {
        position: absolute;
        bottom: 0.15rem;
        left: 50%;
        transform: translateX(-50%);
        width: 1.52rem;
      }
      .more-btn {
        text-align: center;
        font-size: 0.2rem;
        color: #a86117;
        width: 100%;
      }
    }
  }
}
.step {
  width: 2.1rem;
  height: 0.6rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  text-align: center;
  padding-top: 0.05rem;
  line-height: 0.6rem;
  font-size: 0.22rem;
  .step-svg {
    width: 100%;
    height: 100%;
    stroke-linejoin: round;
    font-weight: bold;
    vertical-align: middle;
  }
}
.gray {
  filter: grayscale(1);
}
</style>
