<template>
  <div>
    <CommonDrawer title="填写信息 领取权益" @close="emits('close')">
      <div class="content">
        <div class="form">
          <VanField v-model="phone" required label="电话：" maxlength="11" type="number"></VanField>
        </div>
        <p class="tip-title">重要提醒:</p>
        <div class="tip">{{ planDesc }}</div>
        <div class="submit" @click="checkForm">提交</div>
      </div>
    </CommonDrawer>
    <VanPopup teleport="body" v-model:show="confirmPopup">
      <div class="affiem-inner" @click.stop>
        <div class="affiem-text">{{ phone }}</div>
        <div class="affiem-hint">请再次确定号码填写无误</div>
        <div class="affiem-word">
          <div class="affiem-balck">重要提示：</div>
          <p>① 充值成功后 无法退换；</p>
          <p>② 切勿写错手机号，如充错，责任自行承担；</p>
          <p>③ 点击【确认提交】后，权益领取手机号会无法修改，请 确认无误后再点击确认。</p>
        </div>
        <div class="flex">
          <div class="affirm-cancel-btn m-r-37" @click="confirmPopup = false">返回修改</div>
          <div class="affiem-btn" @click="submit">确认提交</div>
        </div>
      </div>
    </VanPopup>
  </div>

</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { showToast, showLoadingToast, closeToast } from 'vant';
import { ref } from 'vue';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  userReceiveRecordId: {
    type: String,
    required: true,
  },
  planDesc: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const phone = ref('');

// 二次确认弹窗
const confirmPopup = ref(false);

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10030/fuLuReceive', {
      userReceiveRecordId: props.userReceiveRecordId,
      phone: phone.value,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
      confirmPopup.value = false;
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!phone.value) {
    showToast('请输入电话');
  } else if (!checkPhone.test(phone.value)) {
    showToast('请输入正确的电话');
  } else {
    // submit();
    confirmPopup.value = true;
  }
};
</script>

<style scoped lang="scss">
.content {
  border: 0.3rem solid transparent;
  font-size: 0.24rem;
  color: #333333;
  white-space: pre-wrap;

  .form {
    .van-cell {
      border-radius: 0.08rem;
      margin-bottom: 0.1rem;

      &::after {
        display: none;
      }
    }
  }
  .tip-title {
    margin-top: 0.25rem;
    font-size: 0.25rem;
    color: #262626;
  }
  .tip {
    max-height: 5rem;
    overflow-y: scroll;
    font-size: 0.2rem;
    color: #b3b3b3;
  }

  .submit {
    margin-top: 0.25rem;
    text-align: center;
    font-size: 0.24rem;
    line-height: 0.7rem;
    color: #fff;
    background-color: #e2231a;
    height: 0.7rem;
    border-radius: .1rem;
  }
}

// 二次确认弹窗
.affirm-box {
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiem-inner {
  background-color: #fff;
  border-radius: 0.2rem;
  width: 6rem;
  min-height: 2rem;
  padding: 0.4rem 0.2rem;
}
.affiem-text {
  margin-top: 0.09rem;
  font-size: 0.3rem;
  text-align: center;
  line-height: 0.54rem;
  color: #262626;
}
.affiem-hint {
  color: #f2270c;
  font-weight: 400;
  font-size: 0.26rem;
  text-align: center;
  line-height: 0.54rem;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.22rem;
}
.affirm-cancel-btn {
  width: 2rem;
  border-radius: 0.3rem;
  height: 0.6rem;
  line-height: 0.6rem;
  background-color: #fff;
  border: 1px solid #f2270c;
  color: #f2270c;
  text-align: center;
  font-size: 0.24rem;
  box-sizing: border-box;
}
.affiem-btn {
  width: 2rem;
  border-radius: 0.3rem;
  height: 0.6rem;
  line-height: 0.6rem;
  color: #fff;
  text-align: center;
  font-size: 0.24rem;
  box-sizing: border-box;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/125769/5/21394/20372/6268eedeEa00c993d/dfd61890bb7e7fa9.png) no-repeat 100%;
  background-size: cover;
}
.m-r-37 {
  margin-right: 0.37rem;
}
.affiem-word {
  color: #8c8c8c;
  font-size: 0.2rem;
  margin-top: 0.4rem;
  padding: 0 0.2rem;
  line-height: 0.4rem;
}
.affiem-balck {
  color: #262626;
  line-height: 0.43rem;
}
</style>
