<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/200103/2/17947/114377/619dd4c4Ebee607b5/2402df23dbbd8d6f.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event" v-click-track="btn.clickCode">
            {{ btn.name }}
          </div>
        </div>
      </div>
    </div>
    <div>
      <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart" />
    </div>
<!--    <div class="useLimitDiv">-->
<!--      <img src="//img10.360buyimg.com/imgzone/jfs/t1/213434/32/26845/1397/641d6971F89215d5b/f2ae6d9cb1079103.png" alt="" />-->
<!--      <div>活动期间同一账号，每单每个商品最少购买1件，最多可购买1件，才可享受专享价。</div>-->
<!--    </div>-->
    <div class="prizeDivAll" :style="{ 'background-image': 'url(' + furnish.prizeBg ?? `https://img10.360buyimg.com/imgzone/jfs/t1/200141/28/18104/6512/619de8eeE608831e9/2e28e1c10fc6f3e9.png` + `)` }">
      <div class="prizeGoodsDiv">
        <div class="goodsImgDiv">
          <img :src="prizeImg" alt="" />
        </div>
        <div class="leftMessageDiv">
          <div class="prizeNameDiv">{{prizeName}}</div>
          <div class="priceDivAll">
            <div class="currentPriceDiv">
              <div class="currenDivLogo">当前价</div>
              <div>￥{{unitPromotional}}</div>
            </div>
            <div class="oldPriceDiv">京东价：￥{{unitPrice}}</div>
          </div>
          <div class="btnDiv" v-click-track="'ljgm'" :style="{ 'background-image': 'url(' + furnish.goBuyBg ?? `https://img10.360buyimg.com/imgzone/jfs/t1/200085/8/18209/7737/619de904Edf600151/d5017b79d06468fb.png` + `)` }" @click="gotoSkuPage(skuId)"></div>
        </div>
      </div>
      <div class="shareDivAll">
       <div class="shareDiv1All" v-if="!pathParams.shareId">
         <div class="firstRowDiv">
           <div class="shareNumDiv">已邀请<span>{{peopleNum}}</span>人，</div>
           <div class="priceDiv">当前价：<span>￥{{unitPromotional}}</span></div>
         </div>
         <div class="secondRowDiv">邀请更多好友砍出超低价</div>
         <div class="secondRowDiv">优惠名额有限，先到先得</div>
         <div class="thirdRowDiv">
           <div class="processTitleDiv">
             <div class="stepDiv">
               <div class="startProcessDiv">活动开始</div>
               <div class="startDiv">Go</div>
             </div>
             <div class="stepDiv" v-for="(item,index) in prizeList" :key="index">
               <div class="processDiv" v-if="furnish.discountType === 1">￥{{item.unitPromotional}}元</div>
               <div class="processDiv" v-else-if="furnish.discountType === 2">{{item.unitDiscount}}折</div>
               <div class="processDiv" v-else-if="furnish.discountType === 3">砍掉{{item.balance}}元</div>
               <div class="hasShareNumDiv">库存 {{item.remainNum || 0}}</div>
               <div class="hasShareNumDiv">邀请{{item.peopleNum}}人</div>
             </div>
           </div>
           <div class="processDetailDiv">
             <div class="processDetailDiv1">
               <div class="stepOneDivAll">
                 <div class="goDiv"></div>
               </div>
               <div v-for="(items,indexs) in prizeList" :key="indexs" class="stepTwoDivAll1">
                 <div :class="[items.peopleNum!=0 && items.peopleNum <= peopleNum ? 'stepTwoDivAllSucc' : 'stepTwoDivAllFail']">
                   <div class="stepTwoSuccDiv" v-if="items.peopleNum!=0 && items.peopleNum <= peopleNum"></div>
                   <div class="stepTwoFailDiv" v-else></div>
                 </div>
               </div>
             </div>
           </div>
         </div>
       </div>
        <div class="helpDivAll" v-else>
          <div class="sharePeopleDiv">
            <img :src="shareAvatar" alt="" />
            <div class="sharePeopleName">{{shareNickName}}:</div>
          </div>
          <div class="promptTextDiv">
            <span v-if="isHelp===3" class="canHelpDiv">
              <p>小伙伴们，快来帮我砍价吧~</p>
              <p class="canHelpPDiv">【朋友一生一起走，帮砍一刀有没有】</p>
            </span>
            <span v-else-if="isHelp===1" class="canNotHelpDiv">
              <p>奥里给，已经帮我砍过价了~</p>
              <p class="canHelpPDiv">【一声朋友你会懂，发现好货就出手】</p>
            </span>
            <span v-else-if="isHelp === 2" class="canNotHelpDiv">
              <p>奥里给，已经帮其他好友砍过价了~</p>
              <p class="canHelpPDiv">【一声朋友你会懂，发现好货就出手】</p>
            </span>
          </div>
        </div>
        <div class="fourRowDiv" :style="furnishStyles.prizeBtnStyle.value" v-if="!pathParams.shareId" v-threshold-click="shareHelp">立即召唤小伙伴帮砍价</div>
        <div class="fourRowDiv" :style="furnishStyles.prizeBtnStyle.value" v-else>
          <span v-if="isHelp === 3" v-threshold-click="helpTaClick">立即帮TA砍一刀</span>
          <span v-else-if="isHelp === 1 || isHelp === 2" v-threshold-click="joinAcClick">我也要参加</span>
        </div>
      </div>
    </div>
    <div class="sku" v-if="skuList.length">
      <img class="title-img" :src="furnish.winnersBg" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns">
            <div class="price">￥{{ item.jdPrice }}</div>
            <div class="to-bug" @click="gotoSkuPage(item.skuId)" v-click-track="'qg'">抢购</div>
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @savePhone="showSavePhone"></AwardPopup>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import RulePopup from '../components/RulePopup.vue';
import AwardPopup from '../components/AwardPopup.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import CountDown from '../components/CountDown.vue';
import MyPrize from '../components/MyPrize.vue';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import { defaultStateList } from '../ts/default';

const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
// 0-全部商品 1-指定商品  2-排除
const orderSkuisExposure = ref(0);
const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// 店铺名称
const shopName = ref(baseInfo.shopName);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 我的奖品弹窗
const showMyPrize = ref(false);
// 中奖弹窗
const showAward = ref(false);
// 奖品列表
type Prize = {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  sendTotalCount: number;
  skuInfos:any[];
};
const prizeList = ref<Prize[]>(defaultStateList);
const prizeImg = ref('');
const prizeName = ref('');
const unitPrice = ref(0);
const unitPromotional = ref(0);
const peopleNum = ref(0);
const skuId = ref(0);
const isHelp = ref(0); // 1帮当前人助力 2帮其他人助力 3未助力
const shareAvatar = ref('');
const shareNickName = ref('');
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const f = ref<Sku[]>([]);
// 奖品信息
const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  prizeId: '',
  userReceiveRecordId: '',
});
// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
// 按钮列表
const btnList: {
  name: string;
  clickCode: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    clickCode: 'hdgz',
    event: () => {
      showRulePopup();
    },
  },
  {
    name: '进店逛逛',
    clickCode: 'jdgg',
    event: () => {
      gotoShopPage(baseInfo.shopId);
    },
  },
];
// 我也要参加活动
const joinAcClick = () => {
  isHelp.value = 0;
  const urlObj = new URL(window.location.href);
  const searchParams = new URLSearchParams(urlObj.search);
  // 移除shareId参数
  searchParams.delete('shareId');
  // 构造新的URL，不包含shareId参数
  const newUrl = `${urlObj.origin + urlObj.pathname}?${searchParams.toString()}`;
  console.log(newUrl, '没有shareId的活动链接');
  window.location.href = newUrl;
};
// 立即帮ta砍一刀
const helpTaClick = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10038/doTask/doTask', {
      shareId: pathParams.shareId,
    });
    closeToast();
    isHelp.value = 1;
  } catch (e) {
    showToast(e.message);
  }
};
// 邀请好友帮忙助力
const shareHelp = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
// 获取阶梯信息(
const activityData = async () => {
  try {
    const { data } = await httpRequest.post('/10038/activity', {
      shareId: pathParams.shareId,
    });
    console.log(data, 'datadatadata');
    prizeImg.value = data.img;
    prizeName.value = data.prizeName;
    peopleNum.value = data.peopleNum;
    skuId.value = data.skuId;
    isHelp.value = data.status ? data.status : 0;
    unitPrice.value = parseFloat(data.unitPrice / 100).toFixed(2);
    unitPromotional.value = parseFloat(data.unitPromotional / 100).toFixed(2);
    data.prizeList.forEach((item, index) => {
      item.unitPrice = item.unitPrice ? parseFloat(item.unitPrice / 100).toFixed(2) : 0;
      item.unitPromotional = item.unitPromotional ? parseFloat(item.unitPromotional / 100).toFixed(2) : 0;
      item.balance = item.balance ? parseFloat(item.balance / 100).toFixed(2) : 0;
    });
    prizeList.value = data.prizeList;
    if (data.activity10038AssInfoResponse) {
      shareAvatar.value = data.activity10038AssInfoResponse.avatar;
      shareNickName.value = data.activity10038AssInfoResponse.nickName;
    }

  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};

const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
// 获取曝光商品
const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10038/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 18,
    });
    closeToast();
    skuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([activityData(), getSkuList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style lang="scss" scoped>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.useLimitDiv{
  font-size: 0.24rem;
  line-height: 0.44rem;
  width: 6.9rem;
  margin: 0.3rem auto 0px;
  background: #FFFFFF;
  border-radius: 0.2rem;
  text-align: left;
  padding: 0.1rem 0.3rem;
  vertical-align: bottom;
  display: flex;
  justify-content: center;
  img{
    width: 0.3rem;
    height:0.35rem;
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.1rem;
    margin-top: 0.1rem;
  }
}
.prizeDivAll{
  margin: 0.3rem auto 0.45rem;
  width: 6.9rem;
  height: 7.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/200141/28/18104/6512/619de8eeE608831e9/2e28e1c10fc6f3e9.png);
  background-repeat: no-repeat;
  background-size: 100%;
  .prizeGoodsDiv{
    width: 6.9rem;
    height: 2.6rem;
    border-radius: 0.2rem;
    position: relative;
    display: flex;
    .goodsImgDiv{
      width: 2rem;
      height: 2rem;
      margin: 0.3rem;
      img{
        width:100%;
      }
    }
    .leftMessageDiv{
      width: 4rem;
      text-align: left;
      .prizeNameDiv{
        font-size: 0.3rem;
        overflow: hidden;
        color: rgb(38, 38, 38);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        margin-top: 0.35rem;
      }
      .priceDivAll{
        margin-top: 0.2rem;
        .currentPriceDiv{
          display: flex;
          align-items: center;
          font-size: 0.36rem;
          color: rgb(242, 39, 12);
          .currenDivLogo{
            width: 0.74rem;
            height: 0.32rem;
            margin-right: 0.14rem;
            background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/176715/34/21342/1217/619df150Edcb27ff1/a0eb3bc0204868dc.png);
            background-repeat: no-repeat;
            background-size: 100%;
            font-size:0;
          }
        }
        .oldPriceDiv{
          margin-top: 0.15rem;
          text-decoration: line-through;
          color: rgb(140, 140, 140);
          font-size: 0.2rem;
        }
      }
      .btnDiv{
        width: 1.5rem;
        height: 0.5rem;
        position: absolute;
        top: 1.8rem;
        right: 0.3rem;
        background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/200085/8/18209/7737/619de904Edf600151/d5017b79d06468fb.png);
        background-repeat: no-repeat;
        background-size: 100%;
      }
    }
  }
  .shareDivAll{
    text-align: center;
    font-size: 0.2rem;
    margin-top: 0.05rem;
    height: 4.6rem;
    padding-top: 0.25rem;
    width: 100%;
    .shareDiv1All{
      display: flex;
      flex-direction: column;
      align-items: center;
      .firstRowDiv{
        font-size: 0.3rem;
        color: rgb(38, 38, 38);
        display: flex;
        //width: 100%;
        //justify-content: center;
        .shareNumDiv{
          span{
            color: rgb(255, 153, 0);
          }
        }
        .priceDiv{
          span{
            color: rgb(242, 39, 12)
          }
        }
      }
      .secondRowDiv{
        font-size: 0.24rem;
        color: rgb(140, 140, 140);
        font-weight: 500;
        margin-bottom: 0.1rem;
      }
      .thirdRowDiv{
        .processTitleDiv{
          display: flex;
          -webkit-box-pack: justify;
          -ms-flex-pack: justify;
          justify-content: space-between;
          align-items: center;
          margin: 0.2rem 0;
          width: 6.2rem;
          .stepDiv{
            font-size: 0.2rem;
            color: #8c8c8c;
            .startProcessDiv{
              font-size: 0.2rem;
              color: rgb(242, 39, 12);
            }
            .startDiv{
              font-size: 0.2rem;
              color: rgb(255, 153, 0);
            }
          }
        }
        .processDetailDiv{
          width: 5.7rem;
          height: 0.2rem;
          background: linear-gradient(90deg, #f90 0%, #f2270c 100%);
          //background:gray;
          border-radius: 10px;
          margin: 0.37rem auto 0;
          position: relative;
          display: flex;
          .processDetailDiv1{
            position: absolute;
            top: -0.2rem;
            display: flex;
            //width: 100%;

            width: 5.7rem;
            height: 0.2rem;
            border-radius: 10px;
            margin: 0.20rem auto 0;
            .stepOneDivAll{
              //flex:1;
              position: absolute;
              top: -0.1rem;
              left: -0.1rem;
              z-index: 10;
              .goDiv{
                width: 0.36rem;
                height: 0.36rem;
                background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/202544/15/16332/1484/619dfb59Ebb8fea11/890b9279c5478a38.png);
                background-repeat: no-repeat;
                background-size: 100%;
              }
            }
            .stepTwoDivAll1{
              flex:1;
              display: flex;
              justify-content: right;
            }
            .stepTwoDivAllSucc{
              flex:1;
              display: flex;
              justify-content: right;
              position: relative;
              .stepTwoSuccDiv{
                width: 0.74rem;
                height: 0.74rem;
                background-image:url(https://img10.360buyimg.com/imgzone/jfs/t1/209502/4/10442/5281/619dfb52E28f76c72/fd57e72afaa2e961.png);
                background-repeat: no-repeat;
                background-size: 100%;
                position: absolute;
                top: -0.24rem;
                right: -0.1rem;
                z-index: 10;
              }
            }
            .stepTwoDivAllFail{
              flex:1;
              display: flex;
              justify-content: flex-end;
              background: #f2f2f2;
              position: relative;
              .stepTwoFailDiv{
                width: 0.36rem;
                height: 0.36rem;
                background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/201373/8/16826/1147/619dfb63Ed9db6173/7a44e21f1e20f4ae.png);
                background-repeat: no-repeat;
                background-size: 100%;
                position: absolute;
                top: -0.08rem;
                z-index: 10;
                right:-0.1rem;
              }
            }
          }
        }
      }
    }
    .fourRowDiv{
      width: 4rem;
      height: 0.9rem;
      display: inline-block;
      border-radius: 0.5rem;
      line-height: 0.9rem;
      font-size: 0.3rem;
      text-align: center;
      margin-top: 0.3rem;
    }
    .helpDivAll{
      .sharePeopleDiv{
        display: flex;
        align-items: center;
        margin: 0 0.32rem;
        img{
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }
        .sharePeopleName{
          font-size:0.24rem;
          color:gray;
          margin-left:0.1rem;
        }
      }
      .promptTextDiv{
        margin-top:0.48rem;
        margin-bottom:0.2rem;
        .canHelpDiv{
          font-size: 0.3rem;
          color: #ff9900;
          font-weight: bold;
        }
        .canNotHelpDiv{
          font-size: 0.3rem;
          color: #262626;
          font-weight: bold;
        }
        .canHelpPDiv{
          font-size: 0.24rem;
          color: #8c8c8c;
          font-weight: 500;
        }
      }
    }
  }
}

.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .more-btn-all {
    width:6.9rem;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        font-size: 0.27rem;
        color: #262626;
        height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.2rem 0;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug {
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
