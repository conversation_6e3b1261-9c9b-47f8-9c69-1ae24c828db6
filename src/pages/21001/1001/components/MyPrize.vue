<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv">-</div>
      <div>我的奖品</div>
      <div class="rightLineDiv">-</div>
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="type">{{ prizeType[+item.prizeType] }}</div>
        <div class="info">
          <img :src="item.prizeImg" alt="" class="show-img" />
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
            <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="status">
            <div class="green">已发放</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
}
const prizes = reactive([] as Prize[]);
const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/21001/myPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}
</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/230764/27/10369/3999/658d748cF7768ebea/9c4dda764bed7080.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;
    .leftLineDiv {
      width: 0.3rem;
      height: 0.08rem;
      line-height: 0.08rem;
    }
    .rightLineDiv {
      width: 0.3rem;
      height: 0.08rem;
      line-height: 0.08rem;
      margin-left: 0.1rem;
    }
  }
  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .content {
    height: 40vh;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .prize {
      background: #ffffff;
      margin-bottom: 0.1rem;
      padding-bottom: 0.24rem;
      border-radius: 0.16rem;

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.16rem;
        padding-bottom: 0.16rem;
        border-bottom: 0.02rem dashed #eee;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.24rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;

        .show-img {
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #ff3333;
          }

          .time {
            color: #999999;
            font-size: 0.2rem;
            margin-top: 0.2rem;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;

          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }

          .blue {
            color: #0083ff;
          }
        }
      }
    }

    .no-data {
      text-align: center;
      line-height: 35vh;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}
.deliver-dialog {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0.2rem 0.8rem;
  width: 5.88rem;
  height: 2.76rem;
  background-color: #fff;
  border-radius: 0.2rem;
  align-items: center;
  .deliver-btn {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/249881/20/551/18047/65894308F93be030b/8a3bbbb31e4294cc.png') no-repeat;
    background-size: 100% 100%;
    width: 4.5rem;
    height: 0.76rem;
  }
}
.right1btn {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/236543/30/8550/5618/6585328aF1f8cc84b/dd057198e10bd3ef.png) no-repeat;
  background-size: 100% 100%;
  width: 1.2rem;
  height: 0.44rem;
  margin-left: 0.3rem;
  text-align: center;
  line-height: 0.44rem;
  color: #ffffff;
  font-size: 0.24rem;
}
.right2 {
  width: 1.6rem;
  text-align: center;
  font-size: 0.24rem;
  color: #ffa161;
}
</style>
