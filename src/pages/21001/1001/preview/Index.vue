<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <img alt="" :src="furnishStyles.kvImg.value.backgroundImage"  class="kv-img"/>
    <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
      <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
    </div>
    <div class="header-btn-box">
      <div class="header-btn" :style="furnishStyles.operateBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
        {{ btn.name }}
      </div>
    </div>
    <div class="activity-info-box">
      <div class="goods-list-box">
        <div class="line-box"/>
        <div class="time-icon-box">
          <div v-if="activityStatus==='1'" class="time-box-tips1">距离活动开始时间:</div>
          <div v-else-if="activityStatus==='0'" class="time-box-tips2">活动剩余时间:</div>
          <div v-else-if="activityStatus==='2'" class="time-box-tips3">活动已结束</div>
          <van-count-down :time="countDownTime" format="DD:HH:mm" class="time-box">
            <template #default="timeData">
              <div class="time-info-box">{{ timeData.days }}天</div>
              <div class="time-info-box">{{ timeData.hours }}时</div>
              <div class="time-info-box">{{ timeData.minutes }}分</div>
              <div class="time-info-box">{{ timeData.seconds }}秒</div>
            </template>
          </van-count-down>
        </div>
        <div class="goods-box" v-if="giftInfo">
          <div class="goods-info-box">
            <div><span class="goods-price-unit">￥</span><span class="goods-price">{{ giftInfo.couponDiscount }}</span></div>
            <div class="goods-name-box">
              <div class="goods-name">{{ giftInfo.rangeType === 1 ? '店铺券' : '商品券' }}</div>
              <div class="limit-text">满{{ giftInfo.couponQuota }}元使用</div>
            </div>
          </div>
          <div  class="goods-tips">有效期：{{ dayjs(giftInfo.startTime).format('YYYY-MM-DD HH:mm:ss') }}至{{ dayjs(giftInfo.endTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
        </div>
      </div>
    </div>
    <div class="receive-btn-box"  :style="furnishStyles.btnBgColor.value">
      <div class="get-btn">立即领取</div>
    </div>
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import { timeUtils } from '@/utils/timeUtils';
import dayjs from 'dayjs';

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const showMyPrize = ref(false);
const giftInfo = ref({
  couponDiscount: 50,
  prizeName: 'XXX',
  couponQuota: 5999,
  startTime: new Date('2023-12-12 00:00:00').getTime(),
  endTime: new Date('2023-12-12 00:00:00').getTime(),
  rangeType: 1,
});
const couponValidateDayTips = ref('');
const ruleTest = ref('');
// 装修时选择框
const showSelect = ref(false);
const countDownTime = ref(0);
const activityStatus = ref('1'); // 活动状态 '0' 进行中 '1' 未开始 '2' 已结束

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
const setDataInfo = (data:any) => { // 数据赋值
  giftInfo.value = data.prizeInfo;
  if (data.prizeInfo) {
    if (data.prizeInfo.expireType === 1) {
      couponValidateDayTips.value = `领取后${data.prizeInfo.couponValidateDay}天内有效`;
    } else if (data.prizeInfo.expireType === 5) {
      couponValidateDayTips.value = `有效期：${timeUtils.formatDate(data.prizeInfo.couponBeginTime)}至${timeUtils.formatDate(data.prizeInfo.couponEndTime)}`;
    }
  }
  if (data.rules) {
    ruleTest.value = data.rules;
  }
  if (+new Date().getTime() < +new Date(data.startTime)) {
    activityStatus.value = '1';
    countDownTime.value = +new Date(data.startTime) - new Date().getTime();
  }
  if ((+new Date(data.startTime) < new Date().getTime() || +new Date(data.startTime) === new Date().getTime()) && new Date().getTime() < +new Date(data.endTime)) {
    activityStatus.value = '0';
    countDownTime.value = +new Date(data.endTime) - new Date().getTime();
  }
  if (new Date().getTime() > +new Date(data.endTime)) {
    activityStatus.value = '2';
    countDownTime.value = +new Date(data.endTime) - new Date().getTime();
  }
};
const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '我的奖品',
    event: () => {
      showMyPrize.value = true;
    },
  },
];
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    setDataInfo(data);
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  if (activityData) {
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
img[src=""]
,img:not([src])
{
  opacity:0;
}
.bg {
  position: relative;
  padding-bottom: 1rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  .kv-img {
    position: relative;
    width: 7.5rem;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .header-btn-box {
    position: absolute;
    top: 0.3rem;
    right: -0.02rem;
    .header-btn {
      width: 1.35rem;
      height: 0.5rem;
      margin-bottom: 0.1rem;
      font-size: 0.25rem;
      text-align: center;
      border-radius: 0.22rem 0 0 0.22rem;
      border: 0.01rem;
      border-style: solid;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
  .activity-info-box {
    position: absolute;
    top: 38%;
    left: 50%;
    transform: translateX(-50%);
    .goods-list-box {
      position: relative;
      padding: 1.3rem 0.6rem 0.38rem;
      width: 7.21rem;
      height: 8.26rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/231476/6/11631/32081/6597757bF1693ea27/f0beda2d2f66d506.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .line-box {
        margin: 0 0 0.18rem 2.5rem;
        width: 1.21rem;
        height: 0.1rem;
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/232016/9/10667/319/658d6439F1d0c06ee/97bc955ccd7c8c48.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .time-icon-box {
        position: absolute;
        top: 0.28rem;
        height: 0.31rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        white-space: nowrap;
        letter-spacing: 0.031rem;
        color: #ffffff;
        .time-box-tips1 {
          margin-right: 6%;
          height: 0.31rem;
          line-height: 0.31rem;
          font-size: 0.31rem;
          letter-spacing: 0.031rem;
          color: #0b93da;
        }
        .time-box-tips2 {
          margin-right: 16%;
          height: 0.31rem;
          line-height: 0.31rem;
          font-size: 0.31rem;
          letter-spacing: 0.031rem;
          color: #0b93da;
        }
        .time-box-tips3 {
          margin-right: 1.6rem;
          height: 0.31rem;
          line-height: 0.31rem;
          font-size: 0.31rem;
          letter-spacing: 0.031rem;
          color: #0b93da;
        }
        .time-box {
          display: flex;
          height: 0.35rem;
          line-height: 0.35rem;
          font-size: 0.31rem;
          letter-spacing: 0.031rem;
          color: #ffffff;
          .time-info-box {
            margin: 0 0.03rem;
          }
        }
      }
      .goods-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        width: 5.99rem;
        height: 2.61rem;
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/241822/6/1393/5649/658d36aaFc7983cd1/b6571c315349e7b4.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        .goods-info-box {
          display: flex;
          align-items: center;
          .goods-price-unit {
            width: 0.35rem;
            height: 0.48rem;
            font-size: 0.622rem;
            color: #4c6edc;
          }
          .goods-price {
            margin-right: .38rem;
            height: 1.04rem;
            font-size: 1rem;
            color: #4c6edc;
          }
          .goods-name-box {
            margin-bottom: 0.2rem;
          }
          .goods-name {
            margin-bottom: 0.1rem;
            width: 2.53rem;
            font-family: MiSans-Regular;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            white-space: nowrap;
            font-size: 0.373rem;
            letter-spacing: -0.03rem;
            color: #6e66ff;
          }
          .limit-text {
            height: 0.29rem;
            font-family: MiSans-Regular;
            font-size: 0.293rem;
            color: #7e83ff;
          }
        }
        .goods-tips {
          height: 0.23rem;
          width: 5.6rem;
          text-align: center;
          font-family: MiSans-Regular;
          font-size: 0.22rem;
          transform: scale(0.9);
          letter-spacing: -0.005rem;
          color: #568cff;
        }
      }
    }
  }
  .receive-btn-box {
    position: fixed;
    bottom: 0;
    padding: 0.2rem 0;
    display: flex;
    justify-content: center;
    .get-btn {
      width: 4.76rem;
      height: 1.1rem;
      line-height: 1.1rem;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/237432/37/10244/4201/658d379cF81371ed2/f76dd83b6f171446.png");
      background-size: 100%;
      background-repeat: no-repeat;
      font-family: MiSans-Regular;
      text-align: center;
      font-size: 0.53rem;
      letter-spacing: 0.011rem;
      color: #2d53f3;
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
