<template>
  <div class="question_bg">
    <div class="backDiv" @click="close()">&lt;返回</div>
    <div class="questionDivAll">
      <div class="questionDiv">
        <div class="titleDiv">
          <div style="font-size:0.36rem">{{questionIndex+1}}/{{questionData.length}}</div>
          <div>你们的每一个回答对我们都有很大的帮助</div>
        </div>
        <div class="answerDivAll">
          <div class="indexDiv">
            {{questionIndex + 1}}、
            <span class="questionTypeDiv">【{{questionData[questionIndex].questionType === 1 ? '单选题' : questionData[questionIndex].questionType === 2 ? '多选题' : '填空题' }}】</span>
            <span>{{questionData[questionIndex].questionName}}</span>
          </div>
          <div class="answerListDiv">
            <div v-if="questionData[questionIndex].questionType === 1">
              <van-radio-group v-model="questionShowRequestData[questionIndex].checkedAnswerId" @change="radioChange">
                <van-radio class="answerItemDiv" v-for="(item,index) in questionData[questionIndex].answerDetailResponses" :key="index" :name="item.answerId">
                  <div class="answerNameDiv">{{item.answerName}}</div>
                </van-radio>
              </van-radio-group>
            </div>
            <div v-else-if="questionData[questionIndex].questionType === 2">
              <van-checkbox-group v-model="questionShowRequestData[questionIndex].checkBoxAnswerIdArr" @change="checkedResultChange">
                <van-checkbox class="answerItemDiv" v-for="(item,index) in questionData[questionIndex].answerDetailResponses" :key="index" :name="item.answerId">
                  <div class="answerNameDiv">{{item.answerName}}</div>
                </van-checkbox>
              </van-checkbox-group>
            </div>
            <div class="textareaDivAll"  v-else-if="questionData[questionIndex].questionType === 3">
              <textarea v-model="questionShowRequestData[questionIndex].answerFill" class="textareaDiv" maxlength="100" placeholder="至少请输入1个字,最多可输入100个字" />
            </div>
          </div>
          <div class="bottomDivAll">
            <div class="topDiv" v-if="questionIndex > 0" @click="topClick()">上一题</div>
            <div class="nextDiv" v-if="questionIndex+1 < questionData.length" @click="nextClick()">下一题&gt;</div>
            <div class="submitDiv" v-else @click="submitClick()">提交</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const emits = defineEmits(['close', 'answerResult']);
const questionRequestData = ref([]); // 最终提交的数据
const questionShowRequestData = ref([]); // 用于展示的数据结构，里面有单选多选的数据
const questionIndex = ref(0); // 当前展示的问题下标
const checkedAnswerId = ref(); // 单选题选中的那道题的answerId
const checkBoxAnswerIdArr = ref(); // 多选题选中的answerId
const isSubmit = ref(true); // 是否允许点击提交
const props = defineProps({
  questionData: {
    type: Object,
    default: null,
  },
});
props.questionData.forEach((item, index) => {
  const aa = {
    questionType: item.questionType,
    questionId: item.questionId,
    answerFill: '',
    answerRequestList: [],
    checkedAnswerId: null, // 单选的选择数据
    checkBoxAnswerIdArr: [], // 多选的选择数据
  };
  questionShowRequestData.value[index] = aa;
});
// 单选的选择
const radioChange = (radioData) => {
  questionShowRequestData.value[questionIndex.value].checkedAnswerId = radioData;
  console.log(radioData, questionShowRequestData.value[questionIndex.value], '单选题');
};
// 多选题的选择
const checkedResultChange = (checkBoxData) => {
  questionShowRequestData.value[questionIndex.value].checkBoxAnswerIdArr = checkBoxData;
  console.log(checkBoxData, questionShowRequestData.value[questionIndex.value], '多选题id');
};
// 下一题
const nextClick = () => {
  // console.log(questionIndex.value, props.questionData, 'radioSelectData.value');
  if (props.questionData[questionIndex.value].questionType === 1) {
    // 单选
    if (!questionShowRequestData.value[questionIndex.value].checkedAnswerId || questionShowRequestData.value[questionIndex.value].checkedAnswerId.length < 1) {
      showToast('请选择答案');
      return;
    }
  } else if (props.questionData[questionIndex.value].questionType === 2) {
    // 多选题
    if (!questionShowRequestData.value[questionIndex.value].checkBoxAnswerIdArr || questionShowRequestData.value[questionIndex.value].checkBoxAnswerIdArr.length < 1) {
      showToast('请选择答案');
      return;
    }
    if (questionShowRequestData.value[questionIndex.value].checkBoxAnswerIdArr.length < 2) {
      showToast('多选题必须选择2个及2个以上的选项');
      return;
    }
  } else if (props.questionData[questionIndex.value].questionType === 3) {
    if (!questionShowRequestData.value[questionIndex.value].answerFill) {
      showToast('请填写答案');
      return;
    }
  }
  // console.log(questionRequestData.value, '问卷结果');
  questionIndex.value += 1;
  // console.log(questionShowRequestData.value[questionIndex.value], 'questionRequestData[questionIndex].answerFill');
  // 最后一题如果选择过了，需要回显数
  // console.log(questionRequestData.value, '下一题');
};
// 上一题
const topClick = () => {
  // console.log(questionRequestData.value[questionIndex.value], '上一题');
  questionIndex.value -= 1;

};
// 提交问卷
const submitClick = async () => {
  // console.log(questionRequestData.value, '提交问卷结果');
  // 最后一题走提交，不走下一题，所以最后一题再提交的时候校验
  if (props.questionData[questionIndex.value].questionType === 1) {
    // 单选
    if (!questionShowRequestData.value[questionIndex.value].checkedAnswerId || questionShowRequestData.value[questionIndex.value].checkedAnswerId.length < 1) {
      showToast('请选择答案');
      return;
    }
  } else if (props.questionData[questionIndex.value].questionType === 2) {
    // 多选题
    if (!questionShowRequestData.value[questionIndex.value].checkBoxAnswerIdArr || questionShowRequestData.value[questionIndex.value].checkBoxAnswerIdArr.length < 1) {
      showToast('请选择答案');
      return;
    }
    if (questionShowRequestData.value[questionIndex.value].checkBoxAnswerIdArr.length < 2) {
      showToast('多选题必须选择2个及2个以上的选项');
      return;
    }
  } else if (props.questionData[questionIndex.value].questionType === 3) {
    if (!questionShowRequestData.value[questionIndex.value].answerFill) {
      showToast('请填写答案');
      return;
    }
  }
  questionShowRequestData.value.forEach((item, index) => {
    if (item.questionType === 1) {
      const aa = {
        questionId: item.questionId,
        answerFill: '',
        answerRequestList: [{
          answerId: item.checkedAnswerId,
        }],
      };
      questionRequestData.value[index] = aa;
    }
    if (item.questionType === 2) {
      const bb = {
        questionId: item.questionId,
        answerFill: '',
        answerRequestList: [],
      };
      item.checkBoxAnswerIdArr.forEach((answerIdS) => {
        const aa = {
          answerId: answerIdS,
        };
        bb.answerRequestList.push(aa);
      });

      questionRequestData.value[index] = bb;
    } else if (item.questionType === 3) {
      const cc = {
        questionId: item.questionId,
        answerFill: item.answerFill,
        answerRequestList: [],
      };
      questionRequestData.value[index] = cc;
    }
  });
  console.log(questionRequestData.value, '提交数据');
  if (!isSubmit.value) {
    return;
  }
  isSubmit.value = false; // 不允许点击提交
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10081/answer', {
      request: questionRequestData.value,
    });
    closeToast();
    console.log('提交问卷返回', data);
    emits('answerResult', data.activity10081ReceivePrizeResponse);
    isSubmit.value = true;
  } catch (e) {
    showToast({
      message: e.message,
      duration: 2000,
      onClose: (() => {
        emits('answerResult', false);
        isSubmit.value = true;
      }),
    });
  }
};
const close = () => {
  emits('close');
};
</script>
<style lang="scss" scoped>
.question_bg{
  width:100vw;
  height:100vh;
  background:#b8d9ed;
  padding-top:1rem;
  .backDiv{
    position: absolute;
    right: 0.24rem;
    top: 0.2rem;
  }
  .questionDivAll{
    .questionDiv{
      width: 6.44rem;
      background: linear-gradient(91deg, #0350fc 0%, #1a91ff 100%);
      border-radius: 0.24rem;
      margin-left: calc(50% - 6.44rem / 2);
      .titleDiv{
        display: flex;
        height: 1rem;
        align-items: center;
        padding: 0 0.31rem;
        justify-content: space-between;
        color:#fff;
        font-size:0.24rem;
      }
      .answerDivAll{
        width: 6.44rem;
        background: #f2f8fd;
        border-radius: 0.24rem;
        padding: 0.4rem 0.25rem 0.59rem 0.31rem;
        .indexDiv{
          color: #0452fc;
          font-size: 0.24rem;
          padding-bottom: 0.42rem;
          border-bottom: 0.02rem solid #0452fc;
          text-align: left;
          .questionTypeDiv{
            color: #ff0101;
          }
        }
        .answerListDiv{
          .answerItemDiv{
            margin: 0.05rem 0 0.2rem;
            min-height: 0.78rem;
            position: relative;
            font-size: 0.22rem;
            color: #333333;
            display: flex;
            align-items: center;
            border-bottom: 0.01rem solid #999999;
            .answerNameDiv{
              -webkit-box-flex: 1;
              -ms-flex: 1;
              flex: 1;
              text-align: left;
              margin-left: 0.1rem;
              padding: 0.26rem 0;
            }
          }
          .textareaDivAll{
            text-align: center;
            .textareaDiv{
              width: 5.2rem;
              height: 2.95rem;
              border: 0.02rem solid #999999;
              border-radius: 0.05rem;
              margin-top: 0.38rem;
              color: #676767;
              font-size: 0.24rem;
              padding: 0.3rem 0.3rem;
            }
          }
        }
        .bottomDivAll{
          margin-top: 0.58rem !important;
          padding-bottom: 0.3rem !important;
          .topDiv{
            font-size: 0.3rem;
            font-weight: 400;
            color: #1a44ed;
            text-align: end;
            float: left;
          }
          .nextDiv{
            font-size: 0.3rem;
            font-weight: 400;
            color: #1a44ed;
            text-align: end;
            float: right;
          }
          .submitDiv{
            text-align: end;
            float: right;
            border-radius: 0.5rem;
            background: #1a44ed;
            color: #fff;
            padding: 0.1rem 0.5rem;
          }
        }
      }
    }
  }
}
</style>
