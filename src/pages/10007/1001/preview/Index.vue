<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/210588/23/11807/566916/61af40ecE6db54279/b011dcfd1c173156.png'" alt="" class="kv-img" />
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
            {{ btn.name }}
          </div>
        </div>
      </div>
    </div>

    <div class="prizeDivAll" :style="{ 'background-image': 'url(' + furnish.prizeBg ?? `https://img10.360buyimg.com/imgzone/jfs/t1/200141/28/18104/6512/619de8eeE608831e9/2e28e1c10fc6f3e9.png` + `)` }">
      <div class="prizeGoodsDiv">
        <div class="goodsImgDiv">
          <img :src="skuList.length > 0 &&  skuList[0].skuMainPicture ? skuList[0].skuMainPicture : '//img10.360buyimg.com/imgzone/jfs/t1/35593/13/23547/4370/66d015beF9f9a0630/54752ec44513f3ca.png'" alt="" />
        </div>
        <div class="leftMessageDiv">
          <div class="prizeNameDiv">{{skuList.length >0 ? skuList[0].skuName : '--'}}</div>
          <div class="priceDivAll">
<!--            <div class="currentPriceDiv">-->
<!--              <div class="currenDivLogo">券后价：</div>-->
<!--              <div>￥?</div>-->
<!--            </div>-->
            <div class="oldPriceDiv">京东价：<span>￥{{skuList.length >0 ? skuList[0].jdPrice : '--'}}</span></div>
<!--            <div class="prizeDetailDiv">-->
<!--              <div class="prizeInfoDiv">?</div>-->
<!--            </div>-->
          </div>
          <div class="btnDiv" :style="{ 'background-image': 'url(' + furnish.goBuyBg ?? `https://img10.360buyimg.com/imgzone/jfs/t1/200085/8/18209/7737/619de904Edf600151/d5017b79d06468fb.png` + `)` }" @click="ShowToast()"></div>
        </div>
      </div>
      <div class="shareDivAll">
        <div class="firstRowDiv">
          <div class="shareNumDiv">已成功邀请<span>0</span>人，</div>
        </div>
        <div class="secondRowDiv">赶紧邀请好友帮忙砍价，领取优惠券吧~</div>
        <div class="thirdRowDiv">
          <div class="processTitleDiv">
            <div class="stepDiv">
              <div class="startProcessDiv">活动开始</div>
              <div class="startDiv">Go</div>
            </div>
            <div class="stepDiv" v-for="(item,index) in prizeList" :key="index">
              <div class="processDivAll">
                <div class="logoDiv1"></div>
                <div class="processDiv">
                  ￥{{item.couponDiscount}}元
                </div>
              </div>
              <div class="hasShareNumDiv">邀请{{item.peopleNum}}人</div>
            </div>
          </div>
          <div class="processDetailDiv">
            <div class="processDetailDiv1">
              <div class="stepOneDivAll">
                <div class="goDiv"></div>
              </div>
              <div class="stepTwoDivAll" v-for="(items,indexs) in prizeList" :key="indexs">
                <div class="stepTwoFailDiv"></div>
              </div>
            </div>
          </div>
          <div class="processRestNumDiv">
            <div class="stepDiv">
              <div class="startRestProcessDiv"> </div>
            </div>
            <div class="stepDiv" v-for="(item,index) in prizeList" :key="index">
              <div class="hasShareNumDiv">剩余<span>{{item.sendTotalCount}}</span>份</div>
            </div>
          </div>
        </div>
        <div class="fourRowDiv" :style="furnishStyles.prizeBtnStyle.value" @click="ShowToast()">立即召唤小伙伴帮砍价</div>
      </div>
    </div>

    <div class="bottom-div">我也是有底线的哦~</div>
    <div class="goToShopDiv" :style="furnishStyles.goToShopBg.value" @click="toast()">进店逛逛</div>
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);

const isLoadingFinish = ref(false);
type Prize = {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  sendTotalCount: number;
  skuInfos:any[];
};
const prizeList = ref<Prize[]>(defaultStateList);

const total = ref(0);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuListPreview = ref<Sku[]>([]);
const skuList = ref<Sku[]>([]);
const shopName = ref('XX旗舰店');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);
const showAward = ref(false);

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '我的奖品',
    clickCode: 'wdjp',
    event: () => {
      showMyPrize.value = true;
    },
  },
];

const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);

const createImg = async () => {
  showRule.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  console.log(data, 'data=====');
  endTime.value = dayjs(data.endTime).valueOf();
  if (data.prizeList.length && data.prizeList[0].prizeType > 0) {
    data.prizeList.forEach((item, index) => {
      // 优惠价格
      item.couponDiscount = item.couponDiscount ? parseFloat(item.couponDiscount).toFixed(2) : 0;
    });
    prizeList.value = data.prizeList;
  }
  skuListPreview.value = data.skuListPreview ? data.skuListPreview : [];
  skuList.value = data.skuList ? data.skuList : [];
  ruleTest.value = data.rules;
  isExposure.value = data.isExposure;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
  // showSelect.value = false;
});
// 点击边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  if (activityData) {
    if (activityData.prizeList && activityData.prizeList.length && activityData.prizeList[0].prizeType > 0) {
      activityData.prizeList.forEach((item, index) => {
        item.couponDiscount = item.couponDiscount ? parseFloat(item.couponDiscount).toFixed(2) : 0;
      });
      prizeList.value = activityData.prizeList;
    }
    ruleTest.value = activityData.rules;
    shopName.value = activityData.shopName;
    isExposure.value = activityData.isExposure;
    skuListPreview.value = activityData.skuListPreview ? activityData.skuListPreview : [];
    skuList.value = activityData.skuList ? activityData.skuList : [];
  } else {
    prizeList.value = defaultStateList;

  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style lang="scss" scoped>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.useLimitDiv{
  font-size: 0.24rem;
  line-height: 0.44rem;
  width: 6.9rem;
  margin: 0.3rem auto 0px;
  background: #FFFFFF;
  border-radius: 0.2rem;
  text-align: left;
  padding: 0.1rem 0.3rem;
  vertical-align: bottom;
  display: flex;
  justify-content: center;
  img{
    width: 0.3rem;
    height:0.35rem;
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.1rem;
    margin-top: 0.1rem;
  }
}
.prizeDivAll{
  margin: 0.3rem auto 0.45rem;
  width: 6.9rem;
  height: 7.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/200141/28/18104/6512/619de8eeE608831e9/2e28e1c10fc6f3e9.png);
  background-repeat: no-repeat;
  background-size: 100%;
  .prizeGoodsDiv{
    width: 6.9rem;
    height: 2.6rem;
    border-radius: 0.2rem;
    position: relative;
    display: flex;
    .goodsImgDiv{
      width: 2rem;
      height: 2rem;
      margin: 0.3rem;
      img{
        width:100%;
      }
    }
    .leftMessageDiv{
      width: 4rem;
      text-align: left;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .prizeNameDiv{
        font-size: 0.26rem;
        overflow: hidden;
        color: rgb(38, 38, 38);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        margin-top: 0.15rem;
      }
      .priceDivAll{
        //margin-top: 0.2rem;
        .currentPriceDiv{
          display: flex;
          align-items: center;
          font-size: 0.36rem;
          color: rgb(242, 39, 12);
          .currenDivLogo{
            //width: 0.74rem;
            //height: 0.32rem;
            //margin-right: 0.14rem;
            //background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/176715/34/21342/1217/619df150Edcb27ff1/a0eb3bc0204868dc.png);
            //background-repeat: no-repeat;
            //background-size: 100%;
            //font-size:0;
            font-size: 0.24rem;
            font-weight: bold;
          }
        }
        .oldPriceDiv{
          //margin-top: 0.15rem;
          color: rgb(140, 140, 140);
          font-size: 0.2rem;
          span{
            color: rgb(242, 39, 12);
          }
        }
        .prizeDetailDiv{
          width: 1.63rem;
          height: 0.48rem;
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/220172/37/6933/4395/61af48c8Ece392134/69a7cbd38e0df31b.png);
          background-repeat: no-repeat;
          background-size: 100%;
          position: relative;
          margin-top:0.2rem;
          .prizeInfoDiv{
            font-size: 0.3rem;
            color: rgb(255, 255, 255);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 0.9rem;
            height: 100%;
          }
        }
      }
      .btnDiv{
        width: 1.5rem;
        height: 0.5rem;
        position: absolute;
        top: 1.8rem;
        right: 0.3rem;
        background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/200085/8/18209/7737/619de904Edf600151/d5017b79d06468fb.png);
        background-repeat: no-repeat;
        background-size: 100%;
      }
    }
  }
  .shareDivAll{
    text-align: center;
    font-size: 0.2rem;
    margin-top: 0.1rem;
    height: 4.6rem;
    padding-top: 0.41rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .firstRowDiv{
      font-size: 0.3rem;
      color: rgb(38, 38, 38);
      display: flex;
      //width: 100%;
      //justify-content: center;
      .shareNumDiv{
        span{
          color: rgb(255, 153, 0);
        }
      }
      .priceDiv{
        span{
          color: rgb(242, 39, 12)
        }
      }
    }
    .secondRowDiv{
      font-size: 0.24rem;
      color: rgb(140, 140, 140);
      font-weight: 500;
      margin-bottom: 0.15rem;
    }
    .thirdRowDiv{
      .processTitleDiv{
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        margin: 0.2rem 0;
        width: 6.2rem;
        .stepDiv{
          font-size: 0.2rem;
          color: #8c8c8c;
          .processDivAll{
            display:flex;
            align-items: center;
            .logoDiv1{
              background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/219789/9/7054/464/61b01423Eff63833e/dd43ceed25bf6066.png);
              background-repeat: no-repeat;
              background-size: 100%;
              height:0.22rem;
              width:0.28rem;
            }
            .processDiv{
              color:#f2270c;
              margin-left:0.04rem;
            }
          }
          .startProcessDiv{
            font-size: 0.2rem;
            color: rgb(242, 39, 12);
          }
          .startDiv{
            font-size: 0.2rem;
            //color: rgb(255, 153, 0);
          }
        }
      }
      .processDetailDiv{
        width: 5.7rem;
        height: 0.2rem;
        //background: linear-gradient(90deg, #f90 0%, #f2270c 100%);
        background: #f2f2f2;
        border-radius: 10px;
        margin: 0.37rem auto 0;
        position: relative;
        display: flex;
        .processDetailDiv1{
          position: absolute;
          top: -0.1rem;
          display: flex;
          width: 100%;
          .stepOneDivAll{
            //flex:1;
            .goDiv{
              width: 0.36rem;
              height: 0.36rem;
              background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/202544/15/16332/1484/619dfb59Ebb8fea11/890b9279c5478a38.png);
              background-repeat: no-repeat;
              background-size: 100%;
            }
          }
          .stepTwoDivAll{
            flex:1;
            display: flex;
            justify-content: flex-end;
            .stepTwoSuccDiv{
              width: 0.36rem;
              height: 0.36rem;
              background-image:url(https://img10.360buyimg.com/imgzone/jfs/t1/209502/4/10442/5281/619dfb52E28f76c72/fd57e72afaa2e961.png);
              background-repeat: no-repeat;
              background-size: 100%;
            }
            .stepTwoFailDiv{
              width: 0.36rem;
              height: 0.36rem;
              background-image:url(//img10.360buyimg.com/imgzone/jfs/t1/201373/8/16826/1147/619dfb63Ed9db6173/7a44e21f1e20f4ae.png);
              background-repeat: no-repeat;
              background-size: 100%;
            }
          }
        }
      }
      .processRestNumDiv{
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        margin: 0.3rem 0 0.1rem;
        width: 6.2rem;
        .stepDiv{
          font-size: 0.2rem;
          color: #8c8c8c;
          .processDiv{
            color:#f2270c;
          }
          .hasShareNumDiv{
            span{
              color:#f2270c;
            }
          }
          .startRestProcessDiv{
            font-size: 0.2rem;
            color: rgb(242, 39, 12);
            width:0.8rem;
            height:0.3rem;
          }
          .startDiv{
            font-size: 0.2rem;
            //color: rgb(255, 153, 0);
          }
        }
      }
    }
    .fourRowDiv{
      width: 4rem;
      height: 0.9rem;
      display: inline-block;
      border-radius: 0.5rem;
      line-height: 0.9rem;
      font-size: 0.3rem;
      text-align: center;
    }
  }
}

.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .more-btn-all {
    width:6.9rem;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text {
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        font-size: 0.27rem;
        color: #262626;
        height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.2rem 0;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price {
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug {
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #ffffff;
  text-align: center;
}
.goToShopDiv{
  background-color: #F74014;
  position: fixed;
  bottom: 0px;
  width: 100%;
  height: 0.88rem;
  font-size: 0.3rem;
  color: rgb(255, 255, 255);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
