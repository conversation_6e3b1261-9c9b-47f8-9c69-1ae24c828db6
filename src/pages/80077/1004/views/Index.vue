<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img"/>
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
           <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div
            class="header-btn"
            :style="furnishStyles.headerBtn.value"
            v-for="(btn, index) in btnList"
            :key="index"
            @click="btn.event"
            v-click-track="btn.clickCode"
          >
            {{btn.name}}
          </div>
        </div>
      </div>
    </div>
    <div class="draw-btn">
      <div class="count-down-box">
        <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart"/>
      </div>
      <div class="prizes"
           :style="{'background-image':'url('+furnish.prizeBg ?? `https://img10.360buyimg.com/imgzone/jfs/t1/225772/13/1562/67020/6541b15aF50956eed/af8fcb05fc4e6e5a.png`+`)`}"
      >
        <div class="gift-show">
          <div class="gift-img">
            <img class="imgs" :src="prizeList.prizeImg ? prizeList.prizeImg : 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png'" alt="">
            <div class="back">{{prizeList.prizeName}}</div>
          </div>
          <div class="gift-info">
            <div :style="furnishStyles.prizeNameColor.value">{{prizeList.prizeName}}</div>
            <div class="remain-prize" :style="furnishStyles.prizeNameColor.value">奖品剩余：{{prizeList.remainCount}}份</div>
          </div>
          <div class="get-prize-btn1" v-if="prizeList.status === 0 || prizeList.status === -1 || prizeList.status === 2 || prizeList.status === 3" v-threshold-click="receivePrize">
            <img :src="furnish.getPrizeBtn" alt="">
          </div>
          <div class="get-prize-btn2" v-if="prizeList.status === 1" v-threshold-click="receivePrize">已领取</div>
          <div class="get-prize-btn2" v-if="prizeList.status === 4" v-threshold-click="receivePrize">奖品已领光</div>
        </div>
      </div>
    </div>
    <div class="sku" v-if="skuList.length">
      <img class="title-img" :src="furnish.showSkuBg" alt="">
      <div class="sku-list" >
        <div class="sku-list-child">
          <div class="sku-item" v-for="(item,index) in skuList" :key="index">
            <img :src="item.skuMainPicture" alt="">
            <div class="sku-text">{{item.skuName}}</div>
            <div class="sku-price"><span>活动价</span>￥{{item.jdPrice}}</div>
            <div class="sku-btns" @click="gotoSkuPage(item.skuId)">
            </div>
          </div>
        </div>
        <div class="load-more" @click="handleLoadMore">加载更多</div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
    <div class="bottom-shop-share">
      <div class="to-shop" :style="furnishStyles.btnToShop.value" @click="showGoShop = true"/>
    </div>
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup @openShowGoShop="showGoShop = true" :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @savePhone="showSavePhone"></AwardPopup>
    </VanPopup>
    <!--我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
    </VanPopup>
    <!-- 活动商品弹窗-->
    <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
      <GoodsPopup @openShowGoShop="showGoShop = true" :data="orderSkuList" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup
      teleport="body"
      v-model:show="showSaveAddress"
      position="bottom">
      <SaveAddress
        :userReceiveRecordId="userReceiveRecordId"
        :activityPrizeId="activityPrizeId"
        @close="showSaveAddress = false" />
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup" position="bottom">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
    </VanPopup>
    <!-- 领取京元宝权益 -->
    <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
      <SavePhone v-if="savePhonePopup" :userReceiveRecordId="userReceiveRecordId" :planDesc="planDesc" @close="handleClose" />
    </VanPopup>
    <!-- 进店逛逛 -->
    <VanPopup teleport="body" v-model:show="showGoShop" position="bottom" z-index="10000">
      <GoShopPop v-if="showGoShop" @close="showGoShop = false"></GoShopPop>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { inject, nextTick, onMounted, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import RulePopup from '../components/RulePopup.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import CopyCard from '../components/CopyCard.vue';
import SavePhone from '../components/SavePhone.vue';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import CountDown from '../components/CountDown.vue';
import MyPrize from '../components/MyPrize.vue';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import Swiper from 'swiper';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import GoShopPop from '../components/GoShopPop.vue';

import { Handler } from '@/utils/handle';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// 店铺名称
const shopName = ref(baseInfo.shopName);
// 门槛弹窗
// const showLimit = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 我的奖品弹窗
const showMyPrize = ref(false);
// 领取记录弹窗
const showReceiveRecord = ref(false);
// 中奖弹窗
const showAward = ref(false);
// 单次领取弹窗
const showTip = ref(false);
// 我的订单
const showOrderRecord = ref(false);
// 曝光商品
const showGoods = ref(false);

// 奖品列表
const prizeList = ref({
  prizeId: 0,
  prizeImg: '',
  prizeName: '',
  prizeType: 0,
  remainCount: 0,
  sendTotalCount: 0,
  // 奖品状态 -1 不满足条件 0 未领取 1 领取成功 2 取消报名 3 发放奖奖品 4剩余份数不足
  status: 0,
  receivePrizeId: 0,
});
// 活动商品列表
type Sku = {
  skuId: string,
  skuName: string,
  skuMainPicture: string,
  jdPrice: string,
}
const pageNum = ref(1);
const showGoShop = ref(false);
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);
const handler = Handler.getInstance();
onMounted(() => {
  handler.on('onGoShopOpen', () => {
    showGoShop.value = true;
  });
});
// 奖品信息
const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  prizeId: '',
  userReceiveRecordId: '',
});
// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userReceiveRecordId = ref('');
// 填写地址弹窗
const toSaveAddress = (id: string) => {
  userReceiveRecordId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};
// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
// 按钮列表
const btnList: {
  name: string;
  clickCode: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    clickCode: 'hdgz',
    event: () => {
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    clickCode: 'wdjp',
    event: () => {
      showMyPrize.value = true;
    },
  },
  {
    name: '活动商品',
    clickCode: 'hdsp',
    event: () => {
      showGoods.value = true;
    },
  },
  { name: '我的订单',
    clickCode: 'wddd',
    event: () => {
      showOrderRecord.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  prizeImg: '',
});
// 展示卡密
const showCardNum = (result: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');

const showSavePhone = (id: string, desc: string) => {
  userReceiveRecordId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

const handleClose = () => {
  showReceiveRecord.value = false;
  savePhonePopup.value = false;
};

// 获取阶梯信息(
const getStatePrizeList = async () => {
  try {
    const { data } = await httpRequest.post('/80077/activity');
    prizeList.value = data.prize;
    prizeList.value.receivePrizeId = data.receivePrizeId;
  } catch (error) {
    console.error(error);
  }
};
// 获取曝光商品
const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 5000,
      overlay: true,
    });
    const { data } = await httpRequest.post('/80077/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 20,
    });
    closeToast();
    if (data.records.length === 0 && pageNum.value > 1) {
      showToast({
        message: '没有更多数据了',
        duration: 2000,
      });
      return;
    }
    skuList.value.push(...data.records);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const handleLoadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/80077/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    if (activityGiftRecords.length > 4) {
      nextTick(() => {
        const mySwiper = new Swiper('.swiper-container', {
          autoplay: {
            delay: 1000,
            stopOnLastSlide: false,
            disableOnInteraction: false,
          },
          direction: 'vertical',
          loop: true,
          slidesPerView: 5,
          loopedSlides: 8,
          centeredSlides: true,
        });
      });
    }
  } catch (error) {
    console.error(error);
  }
};

// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getStatePrizeList(), getSkuList(), getWinners()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
// 领取奖品
const saveReceivePrize = async (prizeId: number, receivePrizeId:number) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/80077/receivePrize', { prizeId, receivePrizeId });
    closeToast();
    award.value = data;
    showAward.value = true;
    showTip.value = false;
    await init();
  } catch (error) {
    closeToast();
    showToast(error.message);
    console.log(error);
  }
};
// 报名领取
const receivePrize = async () => {
  if (prizeList.value.status === 1) {
    showToast('您已领取过该奖品~');
    return;
  }
  if (prizeList.value.status === 4) {
    showToast('手慢了，奖品已领光~');
    return;
  }
  await saveReceivePrize(prizeList.value.prizeId, prizeList.value.receivePrizeId);
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    line-height: 0.52rem;
    position: relative;
    right: -0.3rem;
    top: 0.6rem;
    width: 1.25rem;
    height: 0.58rem;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.draw-btn {
  width: 6.9rem;
  margin: 0.4rem auto 0 auto;
  img {
    width: 100%;
  }
  .count-down-box{
    position: relative;
    bottom: 0;
    right: 0.37rem;
  }
}

.prizes {
  margin: 0.4rem auto 0 auto;
  border-radius: 0.3rem;
  background-repeat: no-repeat;
  background-size: 100%;
  height: 3.71rem;
  padding-top: 0.72rem;

  .gift-show {
    margin-left: 0.6rem;
    margin-top: 0.32rem;
    margin-right: 0.26rem;
    padding-bottom: 0.22rem;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    font-size: 0.25rem;
    align-items: center;

    .gift-img {
      width: 2rem;
      height: 2rem;
      flex:1;
      border: 0.02rem solid #ff3633;
      margin: 0 0.5rem 0 0;
      border-radius: 0.16rem;
      overflow: hidden;
      position: relative;
      .imgs{
        width: 2rem;
        height: auto;
        border-radius: 0.16rem;
        margin: 0 auto;
      }
      .back{
        width: 1.92rem;
        height: 0.7rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
        background-size: 100%;
        background-repeat: no-repeat;
        position: absolute;
        top: 1.3rem;
        //position: relative;
        //top:-0.6rem;
        text-align: center;
        padding-top: 0.32rem;
        font-size: 0.24rem;
        color: #fff;
      }
    }

    .gift-info {
      -webkit-box-align: end;
      align-items: flex-end;
      flex:1.1;
      text-align: left;
      font-weight: 700;
      .remain-prize{
        //color: #999999;
        font-weight: 500;
        margin: 0.2rem 0 0 0;
      }
    }

    .get-prize-btn1 {
      width: 1.5rem;
      height: 0.5rem;
      //background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
      border-radius: 0.25rem;
      font-size: 0.24rem;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0;
      color: rgb(255, 255, 255);
      text-align: center;
      line-height: 0.5rem;
      position: relative;
      top: 0.9rem;
      right: 2.2rem;
    }
    .get-prize-btn2 {
      width: 1.5rem;
      height: 0.5rem;
      background: linear-gradient(90deg, rgb(80, 80, 80) 0%, rgb(131, 131, 131) 100%);
      border-radius: 0.25rem;
      font-size: 0.24rem;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0;
      color: rgb(255, 255, 255);
      text-align: center;
      line-height: 0.5rem;
      position: relative;
      top: 0.9rem;
      right: 2.2rem;
    }
  }
}

.sku{
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  position:relative;
  .title-img{
    //width: 2.82rem;
    //height: 0.4rem;
    width: 6.9rem;
    margin: 0 auto;
    background-repeat:no-repeat;
    background-size: 100%;
  }
  .sku-list{
    width: 6.9rem;
    margin: 0 auto;
    place-content: flex-start space-between;
    padding: 0.2rem;
    background: rgb(252, 229, 200);
    height: 9.85rem;
    //position: relative;
    border-radius:0 0 0.3rem 0.3rem;
    .sku-list-child{
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      overflow-y: scroll;
      height: 8.85rem;
    }
    .load-more {
      width: 3rem;
      height: 0.6rem;
      line-height: 0.6rem;
      font-size: .24rem;
      text-align: center;
      background: #fa5375;
      border-radius: 0.2rem;
      color: white;
      position: absolute;
      bottom: 0.25rem;
      left: 50%;
      transform: translateX(-50%);
      font-weight: 600;
    }
    .sku-item{
      width: 3.15rem;
      margin-bottom: 0.1rem;
      background: #ffffff;
      //background: url("https://img10.360buyimg.com/imgzone/jfs/t1/189318/33/41501/4729/65360aa7F0f91d0a7/54800024163f3443.png") no-repeat;
      //background-size: 100%;
      border-radius: 0.2rem;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      //width: 3.15rem;
      //margin-bottom: 0.1rem;
      //background: rgb(255, 186, 100);
      //border-radius: 0.2rem;
      //overflow: hidden;
      img{
        margin-top: 0.1rem;
        display: block;
        width: 2.7rem;
        height: 2.7rem;
        border-radius: 0.1rem;
      }
      .sku-text{
        width: 3.1rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 0.3rem;
        color: #000;
        height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0 0 0.1rem;
        box-sizing: border-box;
        line-height: .4rem;
      }
      .sku-price{
        font-size: 0.18rem;
        font-weight: 600;
        color: rgb(255, 67, 47);
        line-height: 0.18rem;
        position: relative;
        left:-0.5rem;
      }
      .sku-btns{
        width: 2.56rem;
        height: 0.6rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin: 0.1rem auto;
        background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/103244/21/43088/2387/64c86e6cFc1a5b032/26519b42d90cb7b9.png);
      }
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1.2rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share{
  //display: flex;
  position: fixed;
  bottom: 0;
  width: 7.5rem;
  //left:0.3rem;
  margin: 0 auto;
  .to-shop{
    height: 1rem;
    width: 7.5rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .share-friends{
    flex: 1;
    height: 0.88rem;
    width: 3.75rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1.1rem;

  .winners-content {
    width: 6.6rem;
    height: 4.2rem;
    //background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}
.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem 0;
  border-bottom: 1px dashed rgb(230, 230, 230);

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
