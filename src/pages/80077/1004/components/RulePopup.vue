<template>
  <div class="rule-bk">
    <div class="close" @click="close"></div>
    <div class="content">
      <div v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/107887/37/34331/205202/64c8b004F4863247d/c8e84b6e3c477bc2.png) no-repeat;
  background-size: 100%;
  width: 5.1rem;
  height: 6.9rem;

  .close {
    width: 0.25rem;
    height: 0.25rem;
    position: relative;
    left: 4.7rem;
    top: 0.15rem;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/163849/34/37594/298/64c87c0cF3e6aab57/00fa23dd4bece777.png");
    background-repeat: no-repeat;
    background-size: 100%;
  }

  .content {
    height: 6rem;
    width: 5.1rem;
    margin: 0.5rem auto 0  auto;
    //background: url(//img10.360buyimg.com/imgzone/jfs/t1/232560/34/9110/4825/65854f01Fdebcf9a2/a37203c75a9d6936.png) no-repeat;
    //background-size: 100%;
    padding: 0.3rem 0.35rem 0.3rem 0.4rem;
    font-size: 0.24rem;
    color: #262626;
    white-space: pre-wrap;
    word-break: break-all;
    div {
      height: 100%;
      overflow-y: scroll;
    }
  }
}
</style>
