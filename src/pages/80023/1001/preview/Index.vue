<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="rulePopup = true"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true"><div>我的奖品</div></div>
        </div>
      </div>
    </div>
    <div class="calendar-bg" :style="furnishStyles.calendarBg.value" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)">
      <div class="sign-in-area">
        <img :src="furnish.signInBeforeIcon" alt="" class="icon" />
        <div class="text">
          <p class="title title-before">今日未签到</p>
          <p class="tip">赶紧点击按钮签到哦~</p>
        </div>
        <img :src="furnish.signInBeforeBt" alt="" class="btn" @click="toSign" />
      </div>
      <div class="info">
        <div>
          连续签<span>{{ continuousSignDays }}</span
          >天
        </div>
        <div class="now">{{ dayjs().format('YYYY年MM月DD日') }}</div>
        <div>
          累计签<span>{{ signDays }}</span
          >天
        </div>
      </div>
      <Calendar></Calendar>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="prizeTipPopup" position="bottom" :closeOnClickOverlay="false"><PrizeTip @close="prizeTipPopup = false"></PrizeTip></VanPopup>
  <VanPopup teleport="body" v-model:show="rulePopup" position="bottom" :closeOnClickOverlay="false">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup" position="bottom" :closeOnClickOverlay="false">
    <MyPrize @close="myPrizePopup = false"></MyPrize>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import { showToast } from 'vant';
import Calendar from '../components/Calendar.vue';
import PrizeTip from '../components/PrizeTip.vue';
import MyPrize from '../components/MyPrize.vue';
import Rule from '../components/Rule.vue';
import dayjs from 'dayjs';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);

const continuousSignDays = ref(0); // 连续签到天数
const signDays = ref(0); // 签到总天数
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const prizeTipPopup = ref(false);

const toSign = () => {
  showToast('活动预览，仅供查看');
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  // showGoods.value = false;
  // showOrderRecord.value = false;
  // showOrderRecord.value = false;
  // showLimit.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.calendar-bg {
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.9rem;
  height: 9.2rem;
  margin: 0.3rem auto 0;
  padding: 0.15rem;
  .sign-in-area {
    height: 2.6rem;
    display: flex;
    align-items: center;
    padding: 0 0.3rem;
    .icon {
      width: 1.4rem;
    }
    .text {
      flex: 1;
      padding-left: 0.26rem;
      .title {
        font-size: 0.36rem;
        color: rgb(139, 133, 255);
        font-weight: bold;
      }
      .title-before {
        color: rgb(242, 39, 12);
      }
      .tip {
        font-size: 0.24rem;
        color: rgb(140, 140, 140);
      }
    }
    .btn {
      width: 1.5rem;
    }
  }
  .info {
    margin-top: 0.05rem;
    height: 1.1rem;
    padding: 0.1rem 0.2rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgb(140, 140, 140);
    font-size: 0.24rem;
    .now {
      font-size: 0.34rem;
      color: rgb(38, 38, 38);
      font-weight: bold;
    }
    span {
      color: rgb(242, 39, 12);
    }
  }
}
</style>
