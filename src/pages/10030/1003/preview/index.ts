import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
// const a = {
//   actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/154557/38/33916/220653/653f0f35Fe6277273/53475e6902451d03.png',
//   pageBg: '',
//   actBgColor: '#a579dc',
//   shopNameColor: '#000000',
//   btnColor: '#ffffff',
//   btnBg: '#a579dc',
//   btnBorderColor: '#ffffff',
//   cutDownBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/204202/32/36601/7261/653f6bcaFe909fc96/83ea15496141340f.png',
//   cutDownColor: '#d51649',
//   prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/225824/18/1243/33834/653f6bcaF55a4c12b/c77b29737a8adcc4.png',
//   ruleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/231554/17/1107/9704/653f0e7dFd3f3a857/a60d76decc7c4396.png',
//   btnPrizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/94921/11/31035/9780/653f0e79Ffb2f5b29/cdc3b73a6eba8d9a.png',
//   btnToShop: 'https://img10.360buyimg.com/imgzone/jfs/t1/96917/39/37981/4647/65041567F5acb147d/2c1340503e3e4f39.png',
//   btnShare: 'https://img10.360buyimg.com/imgzone/jfs/t1/97801/9/45221/4681/65041567F7e06c15a/682c967082403bb3.png',
//   winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229811/7/1181/73391/653f6c7bF59353c7a/0a02a3e62522ad7d.png',
//   mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/101154/30/31805/66938/653f0e79F9562077b/b2ff590496ae7dea.png',
//   cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/101154/30/31805/66938/653f0e79F9562077b/b2ff590496ae7dea.png',
//   h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/101154/30/31805/66938/653f0e79F9562077b/b2ff590496ae7dea.png',
// };
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
