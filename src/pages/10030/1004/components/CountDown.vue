<template>
  <img style="width: 6.9rem" :src="furnish.cutDownBg" alt=""/>
  <div class="count-down-time" :style="furnishStyles.cutDownColor.value">
    <span v-if="props.isStart">距离活动结束剩余：</span>
    <span v-else>距离活动开始还有：</span>
    <van-count-down :time="props.isStart ? (props.endTime - new Date().getTime()) : (props.startTime - new Date().getTime())" format="DD:HH:mm:ss">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownColor.value">{{ timeData.days }}</div><span>天</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownColor.value">{{ timeData.hours }}</div><span>时</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownColor.value">{{ timeData.minutes }}</div><span>分</span>
          <div class="acblockStyleStyle" :style="furnishStyles.cutDownColor.value">{{ timeData.seconds }}</div><span>秒</span>
        </div>
      </template>
    </van-count-down>
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';

const props = defineProps({
  isStart: {
    type: Boolean,
    default: false,
    required: true,
  },
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
});
</script>

<style scoped lang="scss">
.count-down-time {
  position: relative;
  top: -0.58rem;
  left: 1rem;
  width: 6rem;
  font-size: 0.25rem;
  //color: #f2270c;
  .contentSpan {
    margin-left: 0.39rem;
    display: flex;
    position: absolute;
    top: -0.06rem;
    left: 2rem;

    .acblockStyleStyle {
      width: 0.4rem;
      height: 0.44rem;
      //color: #f2270c;
      border-radius: 0.05rem;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
      background-color: #fff;
    }
    span {
      width: 0.4rem;
      height: 0.44rem;
      color: #fe2183;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
