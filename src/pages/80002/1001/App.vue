<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRulePopup" v-click-track="'hdgz'"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true" v-click-track="'wdjp'"><div>我的奖品</div></div>
        </div>
      </div>
      <div class="winener-list" v-if="winnerList.length">
        <van-swipe :loop="true" :autoplay="2000" style="height: 0.6rem" vertical :show-indicators="false">
          <div v-for="(item, index) in winnerList" :key="index">
            <van-swipe-item>
              <div class="winner-item">恭喜{{ item.userName }}获得{{ item.prizeName }}</div>
            </van-swipe-item>
          </div>
        </van-swipe>
      </div>
    </div>
    <div class="count-down-box" v-if="!isEnd">
      <CountDown :endTime="endTime" :startTime="startTime" :hasEligible="hasEligible" :isStart="isStart" :receiveStartTime="receiveStartTime" />
    </div>
    <div class="calendar-bg" :style="furnishStyles.calendarBg.value">
      <div class="sign-in-area" v-if="isSign">
        <img :src="furnish.signInAfterIcon" alt="" class="icon" />
        <div class="text">
          <p class="title">今日已签到</p>
          <p class="tip">真棒！再接再厉哦~</p>
        </div>
        <img :src="furnish.signInAfterBt" alt="" class="btn" />
      </div>
      <div class="sign-in-area" v-else>
        <img :src="furnish.signInBeforeIcon" alt="" class="icon" />
        <div class="text">
          <p class="title title-before">今日未签到</p>
          <p class="tip">赶紧点击按钮签到哦~</p>
        </div>
        <img :src="furnish.signInBeforeBt" alt="" class="btn" v-threshold-click="toSign" v-click-track="'dwqd'" />
      </div>
      <div class="info">
        <div>
          连续签<span>{{ continuousSignDays }}</span
          >天
        </div>
        <div class="now">{{ dayjs().format('YYYY年MM月DD日') }}</div>
        <div class="right">
          <!--          再签<span>{{nextSignDays}}</span>天可领奖-->
        </div>
      </div>
      <Calendar :signList="signRecordList"></Calendar>
    </div>
    <div class="draw-btn">
      <div
        class="prizes"
        :style="{
          'background-image': 'url(' + furnish.prizeBg ?? `https://img10.360buyimg.com/imgzone/jfs/t1/225772/13/1562/67020/6541b15aF50956eed/af8fcb05fc4e6e5a.png` + `)`,
        }">
        <div class="gift-show" v-for="(item, index) in prizeList" :key="index">
          <div class="gift-img">
            <div class="img-box">
              <img class="imgs" :src="item.prizeImg ? item.prizeImg : 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png'" alt="" />
            </div>
            <div class="back">{{ item.prizeName }}</div>
          </div>
          <div class="gift-info">
            <div :style="furnishStyles.prizeNameColor.value">{{ item.prizeName }}</div>
            <div class="remain-prize" :style="furnishStyles.prizeNameColor.value">奖品剩余：{{ item.remainNum }}份</div>
            <div class="get-prize-btn1" v-if="item.status === 1" v-threshold-click="() => receivePrize(item.prizeId)" v-click-track="'ljlq'">立即领取</div>
            <div class="get-prize-btn3" v-if="item.status === 2"></div>
            <div class="get-prize-btn2" v-if="item.status === 3" v-threshold-click="() => receivePrize(item.prizeId)">奖品已领光</div>
          </div>
        </div>
      </div>

      <!-- 奖品提示弹窗 -->
      <VanPopup teleport="body" v-model:show="prizeTipPopup" position="bottom" :closeOnClickOverlay="false">
        <PrizeTip @close="prizeTipPopup = false" :prizeList="prizeList" @openShowGoShop="showGoShop = true" />
      </VanPopup>
      <VanPopup teleport="body" v-model:show="rulePopup" position="bottom" :closeOnClickOverlay="false">
        <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
      </VanPopup>
      <VanPopup teleport="body" v-model:show="myPrizePopup" position="bottom" :closeOnClickOverlay="false">
        <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
      </VanPopup>
      <!-- 中奖弹窗 -->
      <VanPopup teleport="body" v-model:show="awardPopup">
        <Award @openShowGoShop="showGoShop = true" :prize="award" @close="awardPopup = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></Award>
      </VanPopup>
      <!-- 保存地址弹窗 -->
      <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom" :closeOnClickOverlay="false">
        <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
      </VanPopup>
      <!-- 展示卡密 -->
      <VanPopup teleport="body" v-model:show="copyCardPopup" :closeOnClickOverlay="false">
        <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
      </VanPopup>
      <!-- 领取京元宝权益 -->
      <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom" :closeOnClickOverlay="false">
        <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
      </VanPopup>
      <!-- 进店逛逛 -->
      <VanPopup teleport="body" v-model:show="showGoShop" position="bottom" z-index="10000">
        <GoShopPop v-if="showGoShop" @close="showGoShop = false"></GoShopPop>
      </VanPopup>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref, onMounted } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import Calendar from './components/Calendar.vue';
import PrizeTip from './components/PrizeTip.vue';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import Award from './components/AwardPopup.vue';
import SaveAddress from './components/SaveAddress.vue';
import GoShopPop from './components/GoShopPop.vue';
import CopyCard from './components/CopyCard.vue';
import SavePhone from './components/SavePhone.vue';
import dayjs from 'dayjs';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { CardType } from './ts/type';
import CountDown from './components/CountDown.vue';

import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const shopName = ref(baseInfo.shopName);
const showGoShop = ref(false);
const isLoadingFinish = ref(false);
const handler = Handler.getInstance();
onMounted(() => {
  handler.on('onGoShopOpen', () => {
    showGoShop.value = true;
  });
});

interface Prize {
  prizeId: string;
  prizeName: string;
  prizeImg: string;
  remainNum: number;
  status: number;
}

const isSign = ref(false); // 今日是否签到
const continuousSignDays = ref(0); // 连续签到天数
// const nextSignDays = ref(0); // 再签几天可领奖
const signRecordList = ref([] as number[]); // 历史签到天
const prizeList = ref([] as Prize[]); // 奖品列表
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const prizeTipPopup = ref(true);

const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const hasEligible = ref(false);
const startTime = ref(0);
const receiveStartTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
};

// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error) {
    console.error();
  }
};

const awardPopup = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  awardPopup.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  awardPopup.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  awardPopup.value = false;
  myPrizePopup.value = false;
  savePhonePopup.value = true;
};

// 获取签到信息
const getCalendar = async () => {
  try {
    const { data } = await httpRequest.post('/80002/activity');
    data.signRecordList.forEach((item: number, index: number) => {
      const time = dayjs(item).format('YYYYMMDD');
      data.signRecordList[index] = Number(time);
    });
    isSign.value = data.sign;
    continuousSignDays.value = data.continuousSignDays;
    // nextSignDays.value = data.nextSignDays;
    signRecordList.value = data.signRecordList;
    receiveStartTime.value = new Date(data.receiveStartTime).getTime();
    hasEligible.value = receiveStartTime.value < new Date().getTime();
  } catch (error) {
    console.error(error);
  }
};

// 获取礼品信息
const getPrizeList = async () => {
  try {
    const { data } = await httpRequest.post('/80002/getPrizeList');
    prizeList.value = data;
  } catch (error) {
    console.error(error);
  }
};

const receivePrize = async (prizeId: any) => {
  try {
    showLoadingToast({
      message: '',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/80002/receivePrize', { prizeId });
    closeToast();
    award.value = {
      prizeType: data.prizeType,
      prizeName: data.prizeName,
      showImg: data.prizeImg,
      result: data.result ?? '',
      activityPrizeId: data.activityPrizeId ?? '',
      userPrizeId: data.userPrizeId,
    };
    awardPopup.value = true;
    await getPrizeList();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

// 签到
const toSign = async () => {
  try {
    showLoadingToast({
      message: '',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/80002/sign');
    if (data === true) {
      setTimeout(() => {
        showToast('签到成功');
      }, 1000);
      isSign.value = true;
    }
    closeToast();
    await getCalendar();
    await getPrizeList();
    // showToast('签到成功');
  } catch (error: any) {
    await getCalendar();
    closeToast();
    showToast(error.message);
  }
};

interface winner {
  userName: string;
  prizeName: string;
}

const winnerList = ref([] as winner[]);
// 获取获奖名单
const getWinnerList = async () => {
  try {
    const { data } = await httpRequest.post('/80002/prizeReceiveList');
    winnerList.value = data;
  } catch (error) {
    console.error(error);
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    if (isEnd.value) {
      prizeTipPopup.value = false;
    }
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getCalendar(), getPrizeList(), getWinnerList()]);
    isLoadingFinish.value = true;

    closeToast();
  } catch (error) {
    console.error(error);
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .winener-list {
    height: 0.6rem;
    color: #ffffff;
    z-index: 20;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 0.5rem;
    padding: 0.12rem 0.33rem;
    width: 3.2rem;
    font-size: 0.24rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    position: absolute;
    top: 0.7rem;
    left: 0.18rem;
    .winner-item {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.count-down-box {
  margin: 0 auto 0 0.1rem;
}
.calendar-bg {
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.9rem;
  //height: 9.2rem;
  margin: 0.6rem auto 0;
  padding: 0.15rem;
  .sign-in-area {
    height: 2.6rem;
    display: flex;
    align-items: center;
    padding: 0 0.3rem;
    .icon {
      width: 1.4rem;
    }
    .text {
      flex: 1;
      padding-left: 0.26rem;
      .title {
        font-size: 0.36rem;
        color: rgb(139, 133, 255);
        font-weight: bold;
      }
      .title-before {
        color: rgb(242, 39, 12);
      }
      .tip {
        font-size: 0.24rem;
        color: rgb(140, 140, 140);
      }
    }
    .btn {
      width: 1.5rem;
    }
  }
  .info {
    margin-top: 0.05rem;
    height: 1.1rem;
    padding: 0.1rem 0.2rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgb(140, 140, 140);
    font-size: 0.24rem;
    .now {
      font-size: 0.34rem;
      color: rgb(38, 38, 38);
      font-weight: bold;
    }
    .right {
      width: 1rem;
    }
    span {
      color: rgb(242, 39, 12);
    }
  }
}
.draw-btn {
  width: 6.9rem;
  margin: 0.4rem auto 0 auto;
  padding: 0;
  img {
    width: 100%;
  }
}
.prizes {
  margin: 0.4rem auto 0 auto;
  border-radius: 0.3rem;
  background-repeat: no-repeat;
  background-size: 100%;
  //height: 3.71rem;
  padding-top: 0.72rem;
  background-color: #ffffff;

  .gift-show {
    margin-left: 0.6rem;
    margin-top: 0.54rem;
    margin-right: 0.26rem;
    padding-bottom: 0.22rem;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    font-size: 0.25rem;
    align-items: center;

    .gift-img {
      width: 2rem;
      height: 2rem;
      //flex:1;
      border: 0.02rem solid #ff3633;
      margin: 0 0.3rem 0 0;
      border-radius: 0.16rem;
      overflow: hidden;
      .img-box {
        width: 2rem;
        height: 2rem;
        margin: 0 auto;
        .imgs {
          width: 2rem;
          height: auto;
          border-radius: 0.16rem;
          margin: 0 auto;
        }
      }
      .back {
        width: 2.1rem;
        height: 0.7rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
        background-size: 100%;
        background-repeat: no-repeat;
        position: relative;
        top: -0.7rem;
        left: 0;
        text-align: center;
        padding: 0.32rem 0.1rem 0 0.1rem;
        font-size: 0.24rem;
        color: #fff;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .gift-info {
      -webkit-box-align: end;
      align-items: flex-end;
      flex: 1.2;
      text-align: left;
      .remain-prize {
        color: #999999;
        margin: 0.6rem 0 0 0;
      }
    }

    .get-prize-btn {
      width: 1.5rem;
      height: 0.5rem;
      background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
      border-radius: 0.25rem;
      font-size: 0.24rem;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      color: rgb(255, 255, 255);
      text-align: center;
      line-height: 0.5rem;
      position: relative;
      top: 0.5rem;
      right: 0.4rem;
    }

    .get-prize-btn1 {
      width: 1.5rem;
      height: 0.5rem;
      background: linear-gradient(90deg, rgb(242, 39, 12) 0%, rgb(255, 100, 32) 100%);
      border-radius: 0.25rem;
      font-size: 0.24rem;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0;
      color: rgb(255, 255, 255);
      text-align: center;
      line-height: 0.5rem;
      position: relative;
      bottom: 0.4rem;
      left: 2.2rem;
    }
    .get-prize-btn2 {
      width: 1.5rem;
      height: 0.5rem;
      background: linear-gradient(90deg, rgb(80, 80, 80) 0%, rgb(131, 131, 131) 100%);
      border-radius: 0.25rem;
      font-size: 0.24rem;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0;
      color: rgb(255, 255, 255);
      text-align: center;
      line-height: 0.5rem;
      position: relative;
      bottom: 0.4rem;
      left: 2.2rem;
    }
    .get-prize-btn3 {
      position: relative;
      bottom: 0.7rem;
      left: 2.4rem;
      width: 1rem;
      height: 1rem;
      background: url('https://img10.360buyimg.com/imgzone/jfs/t1/211491/30/10303/7300/619cec62E8749b805/1582925af439f63d.png') no-repeat;
      background-size: 100%;
    }
  }
}
::-webkit-scrollbar {
  width: 0;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
