<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRulePopup" v-click-track="'hdgz'"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="myPrizePopup = true" v-click-track="'wdjp'"><div>我的奖品</div></div>
        </div>
      </div>
    </div>
    <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart" />
    <div class="prizeClassAll select-hover" :style="furnishStyles.drawPrizeBg.value">
      <div class="prizeDetailAll">
        <div class="joinNumDiv">已有<span class="numDiv">{{prizeInfo[0].num}}</span>名用户抢到奖品</div>
        <div class="prizeDetail">
          <div class="leftDiv">
            <div class="imgDiv">
              <img class="prizeImgDiv" :src="prizeInfo[0].prizeImg" alt="" />
              <div class="backDiv">{{prizeInfo[0].prizeType}}</div>
            </div>
<!--                        奖品状态2种 已抢完 已领取-->
            <div v-if="prizeInfo[0].status === 2" class="prizeStatusImgDiv" :style="furnishStyles.prizeImgStatus2.value"></div>
            <div v-else-if="prizeInfo[0].status === 3" class="prizeStatusImgDiv" :style="furnishStyles.prizeImgStatus1.value"></div>

          </div>
          <div class="rightDiv">
            <div class="prizeNameDiv">{{prizeInfo[0].prizeName}}</div>
            <div class="dateDiv">领取时间:{{prizeInfo[0].hourMin}}~23:59</div>
            <div class="restDiv">今日剩余:{{prizeInfo[0].todayNum}}份（先到先得）</div>
          </div>
        </div>
      </div>
      <!--      按钮 领取置灰 领取高亮 已领取 已抢完-->
      <div class="prizeBtnDiv" v-if="prizeInfo[0].status === 1 " :style="furnishStyles.getPrizeBtn.value" v-threshold-click="() => drawPrize(prizeInfo[0])">高亮未领取</div>
      <div class="prizeBtnDiv" v-else-if="prizeInfo[0].status === 2 " :style="furnishStyles.getPrizeBtn3.value">已领取</div>
      <div class="prizeBtnDiv" v-else-if="prizeInfo[0].status === 3 " :style="furnishStyles.getPrizeBtn4.value">已抢完</div>
      <div class="prizeBtnDiv" v-else-if="prizeInfo[0].status === 4 " :style="furnishStyles.getPrizeBtn2.value">置灰未领取</div>

    </div>
    <div class="sku" v-if="skuList && skuList.length > 0">
      <img class="title-img" :src="furnish.winnerTitle" alt="">
      <div class="sku-list">
        <div class="sku-item" v-for="(item,index) in skuList" :key="index" @click="gotoSkuPage(item.skuId)">
          <img :src="item.skuMainPicture" alt="">
          <div class="sku-text">{{item.skuName}}</div>
          <div class="sku-btns">
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>

    <VanPopup teleport="body" v-model:show="rulePopup" position="bottom" :closeOnClickOverlay="false">
      <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="myPrizePopup" position="bottom" :closeOnClickOverlay="false">
      <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="awardPopup">
      <Award :prize="award" @close="awardPopup = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></Award>
    </VanPopup>
<!--     保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom" :closeOnClickOverlay="false">
      <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :userPrizeId="userPrizeId" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup" :closeOnClickOverlay="false">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
    </VanPopup>
    <!-- 领取京元宝权益 -->
    <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom" :closeOnClickOverlay="false">
      <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
    </VanPopup>

  </div>
</template>

<script lang="ts" setup>
import { inject, nextTick, reactive, ref } from 'vue';
import furnishStyles, { furnish, prizeInfo } from './ts/furnishStyles';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import Award from './components/AwardPopup.vue';
import SaveAddress from './components/SaveAddress.vue';
import CopyCard from './components/CopyCard.vue';
import SavePhone from './components/SavePhone.vue';
import dayjs from 'dayjs';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { CardType } from './ts/type';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { prizeType } from './ts/default';
import CountDown from './components/CountDown.vue';
import { gotoSkuPage } from '@/utils/platforms/jump';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const shopName = ref(baseInfo.shopName);
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

const chanceNum = ref(0); // 抽奖次数
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const endTime = ref(0);
const isEnd = ref(false);
const startTime = ref(0);
const isStart = ref(false);

const getTime = () => {
  startTime.value = dayjs(baseInfo.startTime).valueOf();
  endTime.value = dayjs(baseInfo.endTime).valueOf();
  const now = dayjs().valueOf();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error) {
    console.error();
  }
};

const awardPopup = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string, userPrizeId1: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  userPrizeId.value = userPrizeId1;
  awardPopup.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  awardPopup.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  awardPopup.value = false;
  myPrizePopup.value = false;
  savePhonePopup.value = true;
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/10022/getPrize');
    prizeInfo[0] = data;

    if (data.status !== 2 && data.todayNum < 1) {
      // 1 未领取 2 已领取 3 已发光 4 未到时间
      data.status = 3;
    } else {
      // 获取当前日期的开始（午夜）
      const todayStart = dayjs().startOf('day');
      // 假设我们有两个时间字符串
      const timeStr1 = data.hourMin;
      const timeStr2 = dayjs().format('HH:mm');
      const timeStr1Hour = timeStr1.split(':')[0];
      const timeStr1Minute = timeStr1.split(':')[1];
      const timeStr1Time = todayStart.hour(timeStr1Hour).minute(timeStr1Minute).format();

      const timeStr2Hour = timeStr2.split(':')[0];
      const timeStr2Minute = timeStr2.split(':')[1];
      const timeStr2Time = todayStart.hour(Number(timeStr2Hour)).minute(Number(timeStr2Minute)).format();
      console.log(timeStr1Time, timeStr2Time, '假设我们有两个时间字符串');

      // 现在我们可以比较这两个时间的总分钟数来确定哪个时间更早或更晚
      if (dayjs(timeStr1Time).isBefore(dayjs(timeStr2Time))) {
        console.log(`${timeStr1} 在 ${timeStr2} 之前`);
      } else {
        console.log(`${timeStr1} 在 ${timeStr2} 之后`);
        data.status = 4;
      }
    }
    console.log(prizeInfo, 'prizeInfoprizeInfo');
  } catch (error) {
    console.error(error);
  }
};
// 点击立即抽奖按钮
const drawPrize = async (prizeData:any) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10022/sendPrize');
    // const data = {
    //   activityPrizeId: '1820334376899821570',
    //   result: {
    //     activityPrizeId: '1820334376899821570',
    //     prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229812/4/22258/2537/66a9fdffF93ca3a0a/9a1665c2cf72b465.png',
    //     prizeName: 'Crxtest001',
    //     prizeType: 3,
    //     result: {
    //       result: 'o240805134520910000',
    //     },
    //     sortId: 0,
    //     status: 1,
    //     userPrizeId: '1820335256896724993',
    //   },
    //   userPrizeId: '1820335256896724993',
    // };
    closeToast();
    await getPrizes();
    data.prizeType = prizeData.prizeType;
    data.prizeName = prizeData.prizeName;
    data.showImg = prizeData.prizeImg;
    data.prizeType = data.result.prizeType;
    award.value = data;
    awardPopup.value = true;
  } catch (error) {
    closeToast();
    showToast(error.message);
  }
};

const getExposureSkuData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10022/getExposureSku', {
      pageNum: pageNum.value,
      pageSize: 10,
      type: 0,
    });
    closeToast();
    if (res.data.records && res.data.records.length > 0) {
      skuList.value.push(...res.data.records);
    }
    pagesAll.value = res.data.pages;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getExposureSkuData();
};
// 初始化
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getPrizes(), getExposureSkuData()]);
    closeToast();
  } catch (error) {
    console.error(error);
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .winener-list{
    height: 0.6rem;
    color: #ffffff;
    z-index: 20;
    background: rgba(0,0,0,0.5);
    border-radius: 0.5rem;
    padding: 0.12rem 0.33rem;
    width: 2.9rem;
    font-size: 0.24rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    position: absolute;
    top: 0.7rem;
    left: 0.18rem;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    padding: 0 0.2rem;
    height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.prizeClassAll{
  width: 6.9rem;
  height: 5.63rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/15152/19/17080/126146/62aa8a71E60d7f958/cc3690c730999236.png);
  background-size: 100%;
  margin-left: calc(50% - 6.9rem / 2);
  margin-top: 0.4rem;
  position: relative;
  .prizeDetailAll{
    position: relative;
    .joinNumDiv{
      font-size: 0.28rem;
      position: absolute;
      width: 100%;
      height: 0.68rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      .numDiv{
        color: rgb(242, 39, 12);
        margin-left: 0.1rem;
        margin-right: 0.1rem;
      }
    }
    .prizeDetail{
      .leftDiv{
        width: 2rem;
        height: 2rem;
        border: 2px solid #ff3633;
        border-radius: 0.1rem;
        left: 0.5rem;
        top: 1.25rem;
        position: relative;
        overflow: hidden;
        .imgDiv{
          .prizeImgDiv{
            width:100%;
            max-width: 100%;
            height:auto;
          }
          .backDiv{
            width: 2rem;
            height: 0.7rem;
            background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
            background-size: 100%;
            background-repeat: no-repeat;
            position: absolute;
            bottom: -2px;
            left: -2px;
            text-align: center;
            padding-top: 0.32rem;
            font-size: 0.24rem;
            color: #fff;
          }
        }
        .prizeStatusImgDiv{
          width: 1.22rem;
          height: 1.22rem;
          position: absolute;
          left: calc(50% - 0.61rem);
          top: 0.25rem;
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/57392/19/19451/7344/62b15019Ef4d1060b/7538990654a464a7.png);
          background-size: 100%;
        }
      }
      .rightDiv{
        width: 3.9rem;
        height: 2rem;
        position: absolute;
        top: 1.25rem;
        left: 2.7rem;
        .prizeNameDiv{
          font-size: .4rem;
          color: #f2270c;
          text-align: left;
          width: 100%;
          font-weight: bold;
          margin-top: .2rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .dateDiv{
          width: 3.6rem;
          height: .4rem;
          background: #ffdcdd;
          color: #f2270c;
          margin-top: .2rem;
          border-radius: .2rem;
          font-size: .22rem;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-align: center;
          -ms-flex-align: center;
          align-items: center;
          -webkit-box-pack: center;
          -ms-flex-pack: center;
          justify-content: center;
        }
        .restDiv{
          font-size: .18rem;
          color: #8c8c8c;
          margin-top: .2rem;
          width: 3.6rem;
        }
      }
    }
  }
  .prizeBtnDiv{
    font-size:0;
    width: 4.84rem;
    height: 1.42rem;
    background-size: 100%;
    position: absolute;
    left: calc(50% - 2.42rem);
    bottom: .25rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/196638/23/26083/39751/62b136c1E4d059f8f/6684609e6fd84f77.png);
  }
}
.sku{
  width: 7.5rem;
  margin: 0.3rem auto 0 auto;
  padding: 0.2rem;
  position: relative;
  .title-img{
    width: 2.82rem;
    margin: 0 auto 0.24rem auto;
    background-repeat:no-repeat;
    background-size: 100%;
    position: relative;
    z-index: 10;
  }
  .more-btn-all{
    width:100%;
    display:flex;
    justify-content:center;
    margin-top:0.24rem;
    padding-bottom:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    width: 6.9rem;
    place-content: flex-start space-between;
    border-radius: 0.4rem;
    max-height: 11rem;
    min-height:4rem;
    position: relative;
    overflow-y: scroll;
    padding: 0.24rem 0.12rem 0px;
  }
  .sku-item{
    width: 3.25rem;
    margin-bottom: 0.1rem;
    background: rgb(255, 255, 255);
    border-radius: 0.2rem;
    overflow: hidden;
    img{
      display: block;
      width: 3.4rem;
      height: 3.4rem;
    }
    .sku-text{
      width: 3.4rem;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;

      overflow: hidden;
      font-size: 0.27rem;
      color: #262626;
      padding: 0 0.2rem;
      margin: 0.2rem 0 0.2rem 0;
      box-sizing: border-box;
    }
    .sku-btns{
      width: 2.56rem;
      height: 0.6rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.14rem auto;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/32236/20/17598/3026/6307406bE3b87a669/355ec23696288825.png);
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  //padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem;
  //margin-top: 0.1rem;
  background: #ffffff;
  border-radius: 0.1rem;

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
.wheel {
  width: 100%;
  margin: 0 auto;
  //background-color: #ffffff;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
