<template>
  <div class="rule-bk">
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div>{{ item.createTime }}</div>
        <div>{{ item.prizeName }}</div>
        <div>
          <div class="status" v-if="item.prizeType === 3">
            <div class="btn" v-if="!item.realName" @click="changAddress(item)">填写地址</div>
            <div v-else>已领取</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 7">
            <div class="btn" @click="showCardNum(item)">如何兑换</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
            <div class="btn" @click="exchangePlusOrAiqiyi" v-if="item.status === 2">立即兑换</div>
            <div v-else>已领取</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 12">
            <div class="btn" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive">填写信息</div>
            <div v-else>已领取</div>
          </div>
          <div class="status" v-else>
            <div>已发放</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  status: number;
  showOrder: boolean;
  orderInfo: {
    orderId: string;
    orderStatus: string;
    orderPrice: string;
  };
  deliverName: string;
  deliverNo: string;
  realName: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99008/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
    prizes.forEach((item) => {
      item.createTime = dayjs(item.createTime).format('YYYY/MM/DD');
      item.showOrder = false;
    });
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  addressCode: '',
});

// 修改地址
const changAddress = (item: any) => {
  addressId.value = item.addressId;
  activityPrizeId.value = item.activityPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userPrizeId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.rule-bk {
  width: 6.5rem;
  height: 6.9rem;
  background: url('../assets/myPrize.png') no-repeat;
  background-size: 100%;
  padding: 1.3rem 0.2rem 0.2rem;

  .close {
    position: absolute;
    bottom: 0;
    left: 2.95rem;
    right: 0.26rem;
    width: 0.77rem;
    height: 0.77rem;
  }

  .content {
    height: 3.5rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #72421f;
    white-space: pre-wrap;

    .prize {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.15rem;
      div {
        text-align: center;
        flex: 0.3333;
      }
      .btn {
        width: 1.2rem;
        height: 0.42rem;
        border-radius: 0.6rem;
        border: 0.02rem solid #fff;
        color: #fff;
        background-color: #72421f;
        margin: 0 auto;
        line-height: 0.38rem;
        font-size: 0.18rem;
      }
    }

    .no-data {
      text-align: center;
      line-height: 5.4rem;
      font-size: 0.24rem;
      color: #72421f;
    }
  }
}
</style>
