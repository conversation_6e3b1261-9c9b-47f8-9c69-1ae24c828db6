<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img
        :src="furnish.actBg"
        alt=""
        class="kv-img" />
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
      <div class="rule-btn" @click="showRulePopup()">活动规则 ></div>
    </div>

    <div>
      <div class="prizeBox">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/248549/8/14065/9188/668660c6Fbad0333f/fd53a7fb5202bda1.png" alt=""/>
        <div class="box">
          <div class="swiper-container-prize" ref="swiperRef" >
            <div class="swiper-wrapper " >
              <div class="swiper-slide prizeItem" v-for="item in prizeList" :key="item">
                <div class="prizeItem" >
                  <img :src="item.prizeImg  " alt=""/>
                  <div class="prizeName">{{item.prizeName }}</div>
                </div>
              </div>
            </div>
            <div v-if="prizeList.length>2" class="swiper-button-prev" @click="prevSwiper"></div>
            <div v-if="prizeList.length>2" class="swiper-button-next" @click="nextSwiper"></div>
          </div>
        </div>
      </div>
      <div class="draw-btn" v-if="baseInfo.status ==2 && !isCanRecive">
        <CountDown
          :applicationEndTime="applicationEndTime"
          :applicationStartTime="applicationStartTime"
          :grantStartTime="grantStartTime"
        />
      </div>
      <div v-if="!showWinners && isInApplication">
        <div v-if="applicationStatus==1" class="application-button-over">已报名</div>
        <div v-else class="application-button-over" :style="furnishStyles.singnStyle.value" @click="handleSignUp" v-click-track="'ljbm'">开始报名</div>
      </div>

      <div class="winners" v-if="[2,3].includes(baseInfo.status) && showWinners">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/200328/34/39751/9887/668660c5Fe47e1f37/c2a649f1a7b5056c.png" alt=""/>
        <div class="header-box">
          <div class="header-btn" :style="furnishStyles.headerBtn.value"  v-for="(btn, index) in btnList" :key="index" @click="btn.event">
            {{ btn.name }}
          </div>
        </div>
        <div class="winners-content">
          <div class="winner-header">
            <div class="rank">排名</div>
            <div class="nickName">昵称</div>
            <div class="prizeName">奖品</div>
          </div>
          <div class="winner-list swiper-container-winner" ref="swiperRef" v-if="activityGiftRecords.length >0">
            <div class="swiper-wrapper" >
              <div class="winner swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
                  <span >NO.{{ index+1 }}</span>
                  <span >{{ item.nickName }}</span>
                  <span >{{ item.prizeName }}</span>
              </div>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无记录</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup v-if="showRule" :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
     <MyPrize v-if="showMyPrize"  @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress v-if="showSaveAddress" @close="saveAddressClose" ></SaveAddress>
    </VanPopup>
    <!--我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup v-if="showOrderRecord" @close="showOrderRecord = false" ></OrderRecordPopup>
    </VanPopup>
    <!-- 填写手机号 -->
    <VanPopup teleport="body" v-model:show="showSavePhone" position="center">
      <SavePhone v-if="showSavePhone" @close="showSavePhone = false;init();" ></SavePhone>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { inject, ref, reactive, nextTick, onMounted } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';

import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import useThreshold from '@/hooks/useThreshold';
import furnishStyles, { furnish } from '../ts/furnishStyles';

import RulePopup from '../components/RulePopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import Threshold2 from '@/components/Threshold2/index.vue';
import CountDown from '../components/CountDown.vue';
import MyPrize from '../components/MyPrize.vue';

import 'swiper/swiper.min.css';
import dayjs from 'dayjs';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import SavePhone from '../components/SavePhone.vue';

Swiper.use([Autoplay]);
let mySwiper: Swiper;

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

// 状态定义
const isCanRecive = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const showLimit = ref(false);
const showOrderRecord = ref(false);
const showSaveAddress = ref(false);
const applicationStatus = ref(0);// 报名状态
const isInApplication = ref(false);// 是否在报名时间内
const showSavePhone = ref(false); // 报名
const showWinners = ref(false); // 是否显示中奖名单
// 数据定义
const prizeList = ref<any>([]);
const prizeObj = ref([] as any);
const activityGiftRecords = reactive([] as ActivityGiftRecord[]);
const ruleTest = ref('');

// 时间定义
const grantStartTime = ref(0);
const grantEndTime = ref(0);

const applicationStartTime = ref(0);
const applicationEndTime = ref(0);
// 函数定义
const prevSwiper = () => {
  mySwiper.slidePrev();
};
const nextSwiper = () => {
  mySwiper.slideNext();
};

// 初始化
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});
// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
// 按钮列表
const btnList = [{
  name: '您的订单 >',
  clickCode: 'wddd',
  event: () => {
    showOrderRecord.value = true;
  },
},
{
  name: '中奖记录 >',
  clickCode: 'zjjl',
  event: () => {
    showMyPrize.value = true;
  },
},
];
const getWinnerList = async () => {
  try {
    const res = await httpRequest.post('/95001/award');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    await nextTick(() => {
      const mySwiper = new Swiper('.swiper-container-winner', {
        autoplay: activityGiftRecords.length > 5 ? { delay: 1000, stopOnLastSlide: false, disableOnInteraction: false } : false,
        direction: 'vertical',
        loop: activityGiftRecords.length > 5,
        slidesPerView: 5,
        loopedSlides: 7,
        spaceBetween: 10,
      });
    });
  } catch (error) {
    console.error(error);
  }
};
// 获取活动信息(
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/95001/activity');
    const nowTime = dayjs().valueOf();
    if (nowTime >= data.grantStartTime) {
      showWinners.value = true;
      await getWinnerList();
    }
    if (nowTime >= data.grantStartTime && nowTime <= data.grantEndTime) {
      isCanRecive.value = true;
    } else {
      isCanRecive.value = false;
    }
    if (nowTime >= data.applicationStartTime && nowTime <= data.applicationEndTime) {
      isInApplication.value = true;
    } else {
      isInApplication.value = false;
    }
    grantStartTime.value = data.grantStartTime; // 领奖开始时间
    grantEndTime.value = data.grantEndTime; // 领奖结束时间

    applicationStartTime.value = data.applicationStartTime; // 报名开始时间
    applicationEndTime.value = data.applicationEndTime; // 报名结束时间
    applicationStatus.value = data.applicationStatus;

  } catch (error) {
    console.error(error);
  }
};
// 获取奖品信息(
const getPrizeList = async () => {
  try {
    const { data } = await httpRequest.post('/95001/prize');
    prizeList.value = data;
    await nextTick(() => {
      mySwiper = new Swiper('.swiper-container-prize', {
        allowTouchMove: prizeList.value.length > 2,
        loop: prizeList.value.length > 2,
        slidesPerView: 2 || 'auto',
        spaceBetween: prizeList.value.length > 2 ? 30 : 0,
        centeredSlides: [1, 3].includes(prizeList.value.length),
        navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
      });
    });
  } catch (error) {
    console.error(error);
  }
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityInfo(), getPrizeList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
// 报名参加活动
const handleSignUp = () => {
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  lzReportClick('bmhd');
  showSaveAddress.value = true;
};
// 保存地址弹窗关闭
const saveAddressClose = (type: any) => {
  if (type) {
    showSaveAddress.value = false;
    init();
  } else {
    showSaveAddress.value = false;
  }
};

init();
</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }
}
.sign-up-button {
  margin-top: 0.3rem;
  width: 2.8rem;
  height:0.68rem;
  font-size: 0.26rem;
  background-size: 100%;
  display: flex;
  color: #6a4828;
  background: #e5c986;
  border-radius: .1rem;
  justify-content: center;
  align-items: center;
}
.gray {
  color: #6f6f6f !important;
  background: #ccc !important;
}
.sign-up-button-over {
  width: 7.08rem;
  height: 1.29rem;
  font-size: 0.36rem;
  line-height: 1rem;
  margin: 0 auto;
  text-align: center;
  background-repeat: no-repeat;
  background-size: 100%;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/238948/17/5293/13052/65e80561F9d3971d8/522a53e7b56b1ac0.png');
  color: #999999;
}

.bottom-div {
  margin: 0.8rem auto 0;
  padding-bottom: .5rem;
  font-size: 0.24rem;
  color: #fff;
  text-align: center;
}
.header-box {
  height: 1rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .header-btn {
    width: 3.19rem;
    height: 0.68rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-size: 100%;
  }
}
.rule-btn {
  font-size: 0.24rem;
  color: #fff;
  text-align: right;
  padding-right: 0.2rem;
  text-decoration: underline;
  text-underline-offset: .1rem;//下划线和文字间距
}
.prizeBox {
  font-size: 0.3rem;
  color: #fff;
  overflow: hidden;
  position: relative;
  img {
    width: 4rem;
    margin: 0.2rem auto;
  }
  .box {
    width: 80%;
    margin:0 auto;
    overflow: hidden;
  }
  .prizeItem {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 2.5rem;

    img {
      width: 1.5rem;
      background: #fff;
    }
    .prizeName {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: center;
    }
  }
}
.application-button-over {
  width: 4.5rem;
  height: 1.5rem;
  font-size: 0.36rem;
  line-height: 1rem;
  margin: 0.5rem auto;
  text-align: center;
  background-repeat: no-repeat;
  background-size: 100%;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/224813/20/21511/2661/669df3e1F0be10966/db1fab96f210a38f.png');
  color: #999999;
}
.draw-btn {
  width: 5rem;
  height: 1rem;
  line-height: 1rem;
  margin: .5rem auto 0 ;
}
.swiper-button-prev {
  position: absolute;
  left: 0;
  top: 70%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/180230/35/37580/745/66755088F83e4bb06/fe2cefbfd6ece6c5.png') no-repeat;
  background-size: 100%;
  z-index: 1;
}
.swiper-button-next {
  z-index: 1;
  position: absolute;
  right: 0;
  top: 70%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/233542/28/20707/755/66755089Fd2d6dab8/628521ff436b49fa.png') no-repeat;
  background-size: 100%;
}
.winners {
  width: 6.96rem;
  height: 9.36rem;
  margin: 0.49rem auto 0;
  padding-top: 0.8rem;
  img {
    width: 4rem;
    margin: 0.2rem auto;
  }
  .winners-content {
    width: 6.6rem;
    height: 5.34rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
    .winner-header {
      display: flex;
      justify-content: space-between;
      padding: 0.2rem 0.3rem;
      font-size: 0.24rem;
      color: #0f822f;
      font-weight: bold;
      div {
        width: 2.2rem;
        text-align: left;
      }
     .prizeName {
        width: 2.2rem;
        text-align: right;
      }
      .nickName {
        width: 2.2rem;
        text-align: center;
      }
    }
    .winner-list {
      width: 100%;
      height: 85%;
      overflow: hidden;
    }
  }
}
.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem;
  //margin-top: 0.1rem;
  // background: #ffffff;
  border-radius: 0.1rem;

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.winner-null {
  text-align: center;
  line-height: 4.9rem;
  font-size: 0.24rem;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: flex;
  justify-content: space-around;
}
</style>
