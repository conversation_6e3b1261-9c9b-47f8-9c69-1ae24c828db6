import { computed, reactive } from 'vue';
import { prizePlateArray } from '@/utils/prizePlateArray';

export const prizeInfo = reactive([
  {
    // id: '',
    // index: 1,
    // prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    // prizeName: 'XX积分',
    // shareNum: 0,
    // sendTotalCount: 0,
    // prizeType: 4,
    // img: '',
    // num: 0,
    // peopleNum: 0,
    // status: 0,
    prizeName: 'XX积分',
    prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    prizeType: 4,
    sendTotalCount: 0,
    type: 0,
    peopleNum: 0,
    rank: 0,
    img: '',
    num: 0,
    status: 1,
    userImg: '',
  },
]);

export const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};
export const furnish = reactive({
  pageBg: '',
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/131542/2/31821/60132/6360cbc6Ea2daddd4/e605cec8be5b01e9.png', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  tipsBg: '',
  tipsTextColor: '',
  tipsNumColor: '',
  shareBtn: '',
  goShopBtn: '',
  activityGuideBg: '',
  closeActivityGuideBg: '',
  sharePrizeBg: '',
  sharePrizeGrayGetBtn: '', // 领取置灰
  sharePrizeReceiveBnt: '', // 已发光
  sharePrizeAlreadyReceived: '', // 已领取
  sharePrizeCanGetBtn: '', // 可以领取
  rankPrizeBg: '',
  rankPrizeCanGetBtnBg: '', // 排行榜可以领取奖品
  rankPrizeBtnBg: '', // 排名中
  rankPrizeHasGetBtnBg: '', // 已经领取
  rankPrizeNoRankBtnBg: '', // 未入榜
  sharePrizeTitleBg: '',
  rankPrizeTitleBg: '',
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));
const sharePrizeTitleBg = computed(() => ({
  backgroundImage: furnish.sharePrizeTitleBg ? `url("${furnish.sharePrizeTitleBg}")` : '',
}));

const rankPrizeTitleBg = computed(() => ({
  backgroundImage: furnish.rankPrizeTitleBg ? `url("${furnish.rankPrizeTitleBg}")` : '',
}));
const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));
const shareBtn = computed(() => ({
  backgroundImage: furnish.shareBtn ? `url("${furnish.shareBtn}")` : '',
}));
const goShopBtn = computed(() => ({
  backgroundImage: furnish.goShopBtn ? `url("${furnish.goShopBtn}")` : '',
}));
const activityGuideBg = computed(() => ({
  backgroundImage: furnish.activityGuideBg ? `url("${furnish.activityGuideBg}")` : '',
}));
const closeActivityGuideBg = computed(() => ({
  backgroundImage: furnish.closeActivityGuideBg ? `url("${furnish.closeActivityGuideBg}")` : '',
}));
const rankPrizeCanGetBtnBg = computed(() => ({
  backgroundImage: furnish.rankPrizeCanGetBtnBg ? `url("${furnish.rankPrizeCanGetBtnBg}")` : '',
}));
const rankPrizeBtnBg = computed(() => ({
  backgroundImage: furnish.rankPrizeBtnBg ? `url("${furnish.rankPrizeBtnBg}")` : '',
}));
const rankPrizeHasGetBtnBg = computed(() => ({
  backgroundImage: furnish.rankPrizeHasGetBtnBg ? `url("${furnish.rankPrizeHasGetBtnBg}")` : '',
}));
const rankPrizeNoRankBtnBg = computed(() => ({
  backgroundImage: furnish.rankPrizeNoRankBtnBg ? `url("${furnish.rankPrizeNoRankBtnBg}")` : '',
}));
const sharePrizeGrayGetBtn = computed(() => ({
  backgroundImage: furnish.sharePrizeGrayGetBtn ? `url("${furnish.sharePrizeGrayGetBtn}")` : '',
}));
const sharePrizeReceiveBnt = computed(() => ({
  backgroundImage: furnish.sharePrizeReceiveBnt ? `url("${furnish.sharePrizeReceiveBnt}")` : '',
}));
const sharePrizeAlreadyReceived = computed(() => ({
  backgroundImage: furnish.sharePrizeAlreadyReceived ? `url("${furnish.sharePrizeAlreadyReceived}")` : '',
}));
const sharePrizeCanGetBtn = computed(() => ({
  backgroundImage: furnish.sharePrizeCanGetBtn ? `url("${furnish.sharePrizeCanGetBtn}")` : '',
}));
export default {
  pageBg,
  shopNameColor,
  headerBtn,
  shareBtn,
  goShopBtn,
  activityGuideBg,
  closeActivityGuideBg,
  rankPrizeCanGetBtnBg,
  rankPrizeBtnBg,
  rankPrizeHasGetBtnBg,
  rankPrizeNoRankBtnBg,
  sharePrizeGrayGetBtn,
  sharePrizeReceiveBnt,
  sharePrizeAlreadyReceived,
  sharePrizeCanGetBtn,
  rankPrizeTitleBg,
  sharePrizeTitleBg,
};
