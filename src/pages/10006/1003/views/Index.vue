<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ? furnish.actBg : 'https://img10.360buyimg.com/imgzone/jfs/t1/234575/39/10213/195021/658a9753F9ed1ddb1/1c792d1a305be428.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div class="header-btn-all">
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRulePopup" v-click-track="'hdgz'">活动规则</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showMyPrize = true" v-click-track="'wdjp'">我的奖品</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showShareFriend = true" v-click-track="'yqhy'">邀请好友</div>

        </div>
      </div>
    </div>
    <div class="shareDivAll">
      <div class="shareNumDiv" :style="furnishStyles.tipsBgStyle.value">
        <div class="shareNum">
          已成功邀请<span>{{assNum}}</span>位好友
        </div>
      </div>
      <div class="shareBtnDiv" v-threshold-click="shareAct" :style="furnishStyles.shareBtn.value"></div>
    </div>
    <div class="sharePrizeDivAll" v-if="isInvite == 2 ">
      <div class="share-prize-top" :style="{'backgroundImage':'url('+furnish.sharePrizeTopBg+')'}"></div>
      <div class="show-prizes swiper-share-prize" ref="swiperRef" :style="{'backgroundImage':'url('+furnish.sharePrizeCenterBg+')'}">
        <div class="swiper-wrapper-share">
          <div class="show-prize-box swiper-slide-share"  v-for="(item,index) in sharePrizeList" :key="index">
            <div class="show-prize-box-fle">
              <div class="prize-box">
                <img class="prizeImgDiV" :src="item.img || 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png'" alt="">
                <div class="backDiv">{{prizeType[item.prizeType]}}</div>
              </div>
              <div class="prize-info">
                <div class="prizeNameDiv">{{item.prizeName}}</div>
                <div class="shareNumDiv"><span>成功邀请{{item.peopleNum}}</span>位好友可领取</div>
                <div class="restDiv">奖品剩余：{{item.num}}份</div>
              </div>
            </div>
            <div class="prizeBtn" v-if="item.status === 1" :style="furnishStyles.sharePrizeCanGetBtn.value" v-click-track="'ljlq'" v-threshold-click="() => drawPrize(item)">可以领取</div>
            <div class="prizeBtn" v-else-if="item.status === 2" :style="furnishStyles.sharePrizeGrayGetBtn.value">不能领取</div>
            <div class="prizeBtn" v-else-if="item.status === 3" :style="furnishStyles.sharePrizeAlreadyReceived.value">已领取</div>
            <div class="prizeBtn" v-else-if="item.status === 4" :style="furnishStyles.sharePrizeReceiveBnt.value">已发光</div>

          </div>
        </div>
      </div>
      <div class="share-prize-bottom" :style="{'backgroundImage':'url('+furnish.sharePrizeBottomBg+')'}"></div>
    </div>
    <div class="activity-guide" :style="furnishStyles.closeActivityGuideBg.value" v-if="rank === 0"></div>
    <div class="activity-guide" :style="furnishStyles.activityGuideBg.value" v-else></div>
    <div class="rankPrizeDivAll" v-if="rank === 1">
<!--      swiper-rank-prize-->
      <div class="swiper-container-rank" :style="{'backgroundImage':'url('+furnish.rankPrizeBg+')'}">
        <div class="show-rank-prizes swiper-rank-prize" ref="swiperRef">
          <div class="swiper-wrapper">
            <div class="show-prize-box swiper-slide"  v-for="(item,index) in rankPrizeList" :key="index">
              <div class="show-prize-box-fle">
                <div class="prize-box">
                  <img class="prizeImgDiV" :src="item.img || 'https://img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png'" alt="">
                  <div class="backDiv">{{prizeType[item.prizeType]}}</div>
                </div>
                <div class="prize-info">
                  <div class="prizeNameDiv">{{item.prizeName}}</div>
                  <div class="shareNumDiv"><span>排名为第{{item.rank}}</span>名可领取</div>
                  <div class="restDiv">奖品剩余：{{item.num}}份</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="ranking-box" :style="[rankingBoxStyle]" v-if="rank === 1">
      <div class="ranking-box-tips1" v-if="dayjs().valueOf() < acStartTime && dayjs().valueOf() < acEndTime">
        <!--        活动尚未开始-->
        <div>活动尚未开始...</div>
      </div>
      <div class="ranking-box-tips1" v-else-if="dayjs().valueOf() > acEndTime && rankStatus === 0">
        <!--        活动已结束-->
        <div>活动已结束...</div>
      </div>
      <div class="ranking-box-tips" v-else-if="rankType === 2 && rankStatus === 1 && dayjs().valueOf() >= dayjs(rankStartTime).valueOf()">
        <!--        已排榜的不能领取（未入榜）-->
      </div>
      <div class="ranking-box-tips" v-else-if="showLimit1">
        <div>不满足参与条件...</div>
      </div>
      <div class="ranking-box-tips" v-else-if="dayjs().valueOf() < dayjs(statisticsEndTime).valueOf()">
        <!--        未到开奖时间-->
        <div>排名正在进行中</div>
        <span>排名每两小时更新一次哦~</span>
      </div>
      <div class="ranking-box-tips" v-else-if="dayjs().valueOf() <= dayjs(rankStartTime).valueOf() && dayjs().valueOf() > dayjs(statisticsEndTime).valueOf()">
        <!--        未到开奖时间-->
        <div>排名正在更新中,请稍后...</div>
      </div>

      <div class="ranking-box-tips" v-else-if="momentType > 0 ">
        <!--         0未入榜 1-n 榜单排名 -->
        <div>您获得: 第{{ momentType }}名</div>
      </div>
      <div class="ranking-box-tips" v-else>
        <div>未排榜</div>
      </div>
      <div class="drawBtnDiv" v-if="rankType === 1" v-click-track="'ljlqphb'" v-threshold-click="drawRankPrize">按钮</div>
      <div class="ranking-box-list" v-if="rankListInfo.length > 0">
        <div class="ranking-box-list-item" v-for="(item,index) in rankListInfo" :key="index">
          <div class="items">
            <div class="items-num" v-if="index<3" :style="{ 'background-image': `url('${numImg[item.rank - 1]}')` }"></div>
            <div v-else class="items-num2">{{item.rank}}</div>
            <div class="items-name">{{item.nickName}}</div>
          </div>
          <div class="ranking-box-list-item-amount">
            邀请人数：<span>{{item.num}}</span>人
          </div>
        </div>
      </div>
      <div class="rank-no-data" v-else>
        <div v-if="dayjs().valueOf() < acStartTime && dayjs().valueOf() < acEndTime" style="marginBottom:1.5rem">活动尚未开始...</div>
        <div v-else-if="dayjs().valueOf() > acEndTime && rankStatus === 0" style="marginBottom:1.5rem">活动已结束...</div>
        <div v-else-if="showLimit1" style="marginBottom:1.5rem">不满足参与条件...</div>
        <img v-else-if="dayjs().valueOf() < dayjs(rankStartTime).valueOf()" src="https://img10.360buyimg.com/imgzone/jfs/t1/61015/14/22123/14554/6371f665E41fd0fb2/544d9f4db3c708aa.png" alt="">
        <div style="marginBottom:1.5rem" v-else>暂无数据</div>
      </div>
    </div>
    <div class="goToshopDiv" :style="furnishStyles.goShopBtn.value" @click="gotoShopPage(baseInfo.shopId)"></div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 邀请好友弹窗 -->
  <VanPopup teleport="body" v-model:show="showShareFriend" position="bottom">
    <ShareFriends v-if="showShareFriend" @close="showShareFriend = false"></ShareFriends>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :userPrizeId="userPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, inject, nextTick } from 'vue';
import furnishStyles, { furnish, prizeType } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { gotoShopPage } from '@/utils/platforms/jump';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import ShareFriends from '../components/ShareFriends.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType, PrizeInfo, Rank } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';

Swiper.use([Autoplay]);
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const shopName = ref(baseInfo.shopName);
const pathParams = inject('pathParams') as any;
const acStartTime = baseInfo.startTime;
const acEndTime = baseInfo.endTime;

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const showMyPrize = ref(false);
const showShareFriend = ref(false);
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
  prizeImg: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string, userPrizeId1: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  userPrizeId.value = userPrizeId1;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};
const isShowShareBtn = ref(true); // 是否显示邀请按钮
const rank = ref(0); // 是否开启排行榜 0 未开启 1开启
const assNum = ref(0); // 已邀请人数
const sharePrizeList = ref<PrizeInfo[]>([]);
const rankPrizeList = ref<PrizeInfo[]>([]);
const rankListInfo = ref<Rank[]>([]);
const rankType = ref(0); // 排行榜奖品  1可以领取 2不能领取(未入榜) 3已领取
const rankStatus = ref(0); // 0未排榜1已排榜
const rankingBoxStyle = ref(furnishStyles.rankPrizeBtnBg.value);
const momentType = ref(-1); // 排名顺序 0未入榜 1-n 榜单排名
const rankStartTime = ref(''); // 开奖开始时间
const statisticsEndTime = ref('');
const numImg = [
  '//img10.360buyimg.com/imgzone/jfs/t1/214317/31/16906/3536/624f9d31E2e45d8cf/8363020f60f115ff.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/219322/7/17032/3636/624f9d31E79f6ea41/442e2f2451d5e1f3.png',
  'https://img10.360buyimg.com/imgzone/jfs/t1/145169/18/26369/3813/624f9d31E2fd9a567/021e24c4f043c941.png',
];
const isInvite = ref(2); // 邀请有礼奖品设置 1 不配置 2配置
const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
const getRankList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10006/rankList');
    rankListInfo.value = data as any[];
    closeToast();
  } catch (e) {
    showToast(e.message);
  }
};
const doTask = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10006/openCard/doTask', {
      shareId: pathParams.shareId,
    });
    showToast('助力成功');
  } catch (e) {
    showToast(e.message);
    console.log(e);
  }
};
const swiperRankPrize = ref<any>(null); // 或者其他初始值 ;
const getPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10006/getPrizes');
    closeToast();
    // console.log(res, '奖品接口========');
    assNum.value = res.data.assNum;
    rankType.value = res.data.rankType; // 1可以领取2不能领取3已领取
    rankStatus.value = res.data.rankStatus; // 0未排榜1已排榜
    momentType.value = res.data.momentType; // 排名顺序 0未入榜 1-n 榜单排名
    isInvite.value = res.data.isInvite;
    if (res.data.prizeResponses && res.data.prizeResponses.length > 0) {
      // 邀请奖品
      sharePrizeList.value = res.data.prizeResponses.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 0);
      sharePrizeList.value.forEach((item, index) => {
        if (item.num === 0 && item.status !== 3) {
          item.status = 4;
        }
      });
    }
    rank.value = res.data.rank;
    if (res.data.rank === 1) {
      // 判断当前时间是否到排行榜统计时间，到统计结束时间就不展示邀请按钮
      // if (dayjs().valueOf() > dayjs(res.data.statisticsEndTime).valueOf()) {
      //   isShowShareBtn.value = false;
      // } else {
      //   isShowShareBtn.value = true;
      // }
      rankStartTime.value = res.data.rankStartTime;
      statisticsEndTime.value = res.data.statisticsEndTime;
      rankPrizeList.value = res.data.prizeResponses.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 1);
      // if (dayjs().valueOf() >= dayjs(res.data.statisticsStartTime).valueOf()) {
      //   // 到了排行榜统计时间
      //   await getRankList();
      // }
      if (rankStatus.value === 1 && dayjs().valueOf() >= dayjs(rankStartTime.value).valueOf()) {
        if (rankType.value === 1) {
          rankingBoxStyle.value = furnishStyles.rankPrizeCanGetBtnBg.value;
        } else if (rankType.value === 2) {
          rankingBoxStyle.value = furnishStyles.rankPrizeNoRankBtnBg.value;
        } else if (rankType.value === 3) {
          rankingBoxStyle.value = furnishStyles.rankPrizeHasGetBtnBg.value;
        } else {
          rankingBoxStyle.value = furnishStyles.rankPrizeBtnBg.value;
        }
      }
      await getRankList();
    }
    if (rankPrizeList.value && rankPrizeList.value.length > 1) {
      nextTick(() => {
        if (!swiperRankPrize.value) {
          swiperRankPrize.value = new Swiper('.swiper-rank-prize', {
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
            },
            loop: true,
            slidesPerView: 1,
            loopedSlides: 10,
            allowTouchMove: true,
          });
        }
      });

    }
    console.log(sharePrizeList.value, 'sharePrizeListsharePrizeList');
    // sharePrizeList.value = res.data
    if (pathParams.shareId) {
      await doTask();
    }
  } catch (e) {
    showToast(e.message);
  }
};
// 领取邀请奖品
const drawPrize = async (prizeData:any) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10006/sendCommonPrize', {
      prizeId: prizeData.prizeId,
    });
    // const data = {
    //   prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    //   prizeType: 12,
    //   prizeName: '123123123',
    //   result: {
    //     result: {
    //       planDesc: 'asdasdasd',
    //     },
    // cardDesc: '2222222',
    // cardNumber: '1111111111',
    // cardPassword: '23123123',
    // id: 122222222,
    // prizeName: '213123',
    // showImg: '//img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png',
    //   },
    //   activityPrizeId: 213123123123,
    //   userPrizeId: 23123123123,
    // };
    closeToast();
    await getPrizes();
    award.value = data;
    showAward.value = true;
  } catch (e) {
    showToast({
      message: e.message,
      duration: 2000,
      onClose: (() => {
        getPrizes();
      }),
    });
  }
};
// 排行榜领取奖品
const drawRankPrize = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10006/sendRankPrize');
    closeToast();
    await getPrizes();
    award.value = data;
    showAward.value = true;
  } catch (e) {
    showToast({
      message: e.message,
      duration: 2000,
      onClose: (() => {
        getPrizes();
      }),
    });
  }
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    await Promise.all([getPrizes()]);
  } catch (error) {
    console.log(error);
  }
};
init();

</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color:#f2f2f2
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
    color:#fff;
  }

  .header-btn-all{
    margin-top:2.68rem;
    .header-btn {
      width: 1.51rem;
      height:0.72rem;
      line-height: 0.72rem;
      margin-bottom: 0.1rem;
      font-size: 0.2rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-repeat: no-repeat;
      background-size: 100%;
      padding-bottom: 0.1rem;
    }
  }
}
.shareDivAll{
  .shareNumDiv{
    width: 6.95rem;
    height: 0.72rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/248709/9/789/2340/658bbe45Fd7042416/55806aac70ae6aea.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font-size: 0.24rem;
    display: flex;
    justify-content: center;
    color: rgb(255, 234, 204);
    margin: 0.3rem auto 0px;
    line-height: 0.72rem;
    .shareNum{
      margin-left: 0.12rem;
      font-size: 0.3rem;
      font-weight: 600;
      span{
        color: rgb(255, 241, 0);
      }
    }
  }
  .shareBtnDiv{
    width: 5.61rem;
    height: 1.11rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/249884/36/819/19912/658bbda4F1019d788/c740e01c21474768.png);
    background-repeat: no-repeat;
    background-size: 100%;
    margin: 0.36rem auto 0.1rem;
  }
}
.sharePrizeDivAll{
  .share-prize-top{
    width: 6.91rem;
    height: 0.83rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/226807/6/9977/12847/658aa149F8fdd8cbf/2e2225455da94624.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin:0 auto;
  }
  .share-prize-bottom{
    width: 6.91rem;
    height: 0.47rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/234030/29/9714/3587/658aa149F43be6c05/55b0f87718d57ea5.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin:0 auto;
  }
  .swiper-share-prize{
    width: 6.91rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/224688/21/9859/19206/658aa149F9fab1f4c/4c8615ef7af32a7d.png);
    background-size: 100%;
    background-repeat: repeat-y;
    margin:0 auto;
  }
  .show-prizes{
    z-index: 0 !important;
    overflow: hidden;
    margin:0 auto;
    .show-prize-box{
      display: flex;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/227863/1/1137/5073/653b6690Fbf24a275/249bf86aad58e5c9.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 6.65rem;
      height: 2.44rem;
      margin-left: calc(50% - 6.65rem / 2);
      position: relative;
      &-fle{
        //width: 90%;
        display:flex;
        .prize-box{
          width: 1.91rem;
          height: 1.92rem;
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/237897/35/9137/4026/658aa21fF1fcb39ec/ca2d74960991330e.png);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          border: none;
          background-color: transparent;
          justify-content: flex-start;
          position: relative;
          overflow: hidden;
          display: flex;
          align-items: center;
          flex-direction: column;
          margin: 0.3rem 0.22rem 0.23rem 0.22rem;
          .prizeImgDiV{
            margin:0.10rem auto 0 auto;
            height: 1.3rem;
          }
          .backDiv{
            width: 1.90rem;
            height: 0.35rem;
            position: absolute;
            text-align: center;
            padding-bottom: 0.32rem;
            font-size: 0.24rem;
            color: #FFFFFF;
            bottom: 2px;
            top: auto;
          }
        }
        .prize-info{
          font-size:0.2rem;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          //width: calc(100% - 3rem);
          box-sizing: border-box;
          height: 2rem;
          margin-top: 0.2rem;
          position: relative;
          .prizeNameDiv{
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            font-size: 0.3rem;
            text-align: left;
            font-weight: bold;
          }
          .shareNumDiv{
            font-size: 0.2rem;
            margin-top: 0.08rem;
            color: #000;
            text-align: left;
          }
          .restDiv{
            font-size: 0.2rem;
            color: #000;
            margin-left: 0.05rem;
            margin-top: 0.14rem;
          }
        }
      }
      .prizeBtn{
        position: absolute;
        bottom: 0.24rem;
        right: 0.24rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/197485/37/18150/7911/619cec62Ee06d907f/e5f5555bc7d0bfb3.png);
        background-size: 100%;
        background-repeat: no-repeat;
        width: 1.5rem;
        height: 0.5rem;
        font-size:0;
      }
    }
  }
}
.activity-guide{
  width: 6.9rem;
  height: 3.28rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/189217/21/30459/75478/6371d6b4Ee5a0ff0f/e6dc6e0d37ea0f87.png);
  background-size: 100%;
  background-repeat: no-repeat;
  margin: 0.3rem 0px 0.3rem calc(50% - 3.45rem);
}
.rankPrizeDivAll{
  .swiper-container-rank{
    width: 7.08rem;
    height: 3.68rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/249778/13/812/24959/658bc7f5Faa36ecdd/d720b31c757c412c.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
    padding-top: 0.9rem;
    overflow: hidden;
    display: flex;
    justify-content: center;
  }
  .show-rank-prizes{
    //z-index: 0 !important;
    //width: 6.34rem;
    //overflow: hidden;
    //margin:0 auto;

    width: 6.51rem;
    display: flex;
    justify-content: center;
    overflow: hidden;
    .show-prize-box{
      //width:100%;
      ////background: #FFFFFF;
      //margin: 0 auto;
      //border-radius: 0.1rem;
      //position: relative;
      //padding-bottom:0.5rem;
      height: 2.32rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/230964/10/10137/6074/658bc858Ff2c589ad/cbc37bb897f37230.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      width: 6.51rem !important;
      margin: 0 auto;
      &-fle{
        //width: 90%;
        display:flex;
        .prize-box{
          width: 1.91rem;
          height: 1.92rem;
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/237897/35/9137/4026/658aa21fF1fcb39ec/ca2d74960991330e.png);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          margin: 0.18rem 0.2rem 0.23rem;
          position: relative;
          overflow: hidden;
          .prizeImgDiV{
            margin:0.10rem auto 0 auto;
            height: 1.3rem;
            width: auto;
          }
          .backDiv{
            width: 1.92rem;
            height: 0.6rem;
            background-image: none;
            background-size: 100%;
            background-repeat: no-repeat;
            position: absolute;
            bottom: -2px;
            left: 0px;
            text-align: center;
            padding-top: 0.2rem;
            font-size: 0.24rem;
            color: #fff;
          }
        }
        .prize-info{
          font-size:0.2rem;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          //width: calc(100% - 3rem);
          box-sizing: border-box;
          height: 2rem;
          margin-top: 0.1rem;
          position: relative;
          .prizeNameDiv{
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            font-size: 0.3rem;
            color: #000;
            text-align: left;
          }
          .shareNumDiv{
            font-size: 0.2rem;
            color: #000;
            margin-top: 0.08rem;
            text-align: left;
          }
          .restDiv{
            font-size: 0.2rem;
            color: #000;
            margin-left: 0.05rem;
            margin-top: 0.14rem;
          }
        }
      }
    }
  }
}
.ranking-box{
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/787/31/24791/26057/66c40d91F58197eb8/143d5e1e7beeb139.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 7.06rem;
  height: 7.48rem;
  text-align: center;
  overflow: hidden;
  margin: 0.3rem auto 0px;
  position: relative;
  .ranking-box-tips{
    position: absolute;
    top: 1.3rem;
    left: 2.13rem;
    color: rgb(242, 39, 12);
    font-size: 0.28rem;
    text-align: left;
    span{
      font-size:0.2rem;
      color:#000;
    }
  }
  .ranking-box-tips1{
    position: absolute;
    top: 1.3rem;
    left: 2.53rem;
    color: rgb(242, 39, 12);
    font-size: 0.28rem;
    text-align: left;
    span{
      font-size:0.2rem;
      color:#000;
    }
  }
  .drawBtnDiv{
    width: 1.5rem;
    height: .5rem;
    position: absolute;
    right: 0.45rem;
    top: 1.22rem;
    font-size:0;
  }
  .rank-no-data{
    font-size: 0.24rem;
    color: #8c8c8c;
    height: calc(100% - 0.4rem);
    margin: 1.24rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 0.24rem;
    width: 100%;
    img{
      border: 0;
      max-width: 100%;
    }
  }
  &-list{
    width: 6.7rem;
    height: 4.52rem;
    overflow-y: auto;
    margin: 2.4rem 0.15rem 0.15rem;
    border-radius: 0.1rem;
    font-size: 0.28rem;
    box-sizing: border-box;
    &-item{
      width: 6.5rem;
      height: 0.9rem;
      padding: 0rem 0.21rem 0px 0.15rem;
      border-radius: 0.08rem;
      background: rgb(255, 255, 255);
      font-size: 0.28rem;
      box-sizing: border-box;
      margin-bottom: 0.03rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 0.12rem;
      .items{
        display: flex;
        align-items: center;
        &-num{
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 0.36rem;
          height: 0.56rem;
          margin-right: 0.19rem;
        }
        &-num2{
          width: 0.36rem;
          height: 0.56rem;
          margin-right: 0.19rem;
        }
        &-img{
          background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/214317/31/16906/3536/624f9d31E2e45d8cf/8363020f60f115ff.png);
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: 0.56rem;
          height: 0.56rem;
          margin-right: 0.19rem;
          border-radius: 100%;
          overflow: hidden;
          img{
            width: 0.56rem;
            height: 0.56rem;
          }
        }
        &-name{
          font-size: 0.23rem;
          margin-left: 0.19rem;
        }
      }
      &-amount{
        color: rgb(148, 148, 148);
        font-size: 0.22rem;
        span{
          color:red;
        }
      }
    }
  }
}
.goToshopDiv{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 7.5rem;
  height: 0.88rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/107718/1/35765/4396/658bd2d6F92f4be27/dfa8dd201bfac354.png);  background-size: 100%;
  z-index:100;
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
