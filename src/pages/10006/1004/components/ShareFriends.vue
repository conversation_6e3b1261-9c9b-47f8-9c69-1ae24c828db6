<template>
  <div class="rule-bk">
    <div class="title">
<!--      <img src="//img10.360buyimg.com/imgzone/jfs/t1/7614/30/26740/15653/66bdbf22Fba87a0e8/147475e23795af48.png" alt="" class="text" />-->
      <div class="close" @click="close"></div>
    </div>
    <div class="contentAll">
      <div class="formTitle"></div>
      <div class="formInfo" v-if="shareFriendList.length > 0">
        <div
            class="listAll"
            v-for="(item, index) in shareFriendList"
            :key="index"
        >
          <div class="infoLeft">
            <img v-if="item.shareImg" :src="item.shareImg" alt="" />
            <span>{{ item.nickName }}</span>
          </div>
          <div class="infoRight">
              <span>
               {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
              </span>
          </div>
        </div>

       <div class="more-btn-all">
         <div class="more-btn" v-if="!isPreview && pageNum < pagesAll" @click="loadMore">点我加载更多</div>
       </div>
      </div>
      <div class="formInfo1" v-if="shareFriendList.length == 0">
        暂无邀请记录哦~
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

interface ShreFriend {
  nickName: string;
  shareImg: string;
  createTime: number;
}
const shareFriendList = reactive([] as ShreFriend[]);

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10006/inviteList', {
      pageNum: pageNum.value,
      pageSize: 10,
    });
    // const data = {
    //   records: [
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //     {
    //       nickName: '12312312',
    //       createTime: dayjs().valueOf(),
    //     },
    //   ],
    //   total: 2,
    //   pages: 3,
    // };
    shareFriendList.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getUserPrizes();
};
if (!isPreview) {
  getUserPrizes();
}

</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/188460/2/37603/103521/6502c281F1b07da56/8f0fdeb8cfc4228d.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 0.86rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.6rem;
    }
  }

  .close {
    position: absolute;
    right: 0.32rem;
    width: 0.55rem;
    height: 0.55rem;
  }
  .contentAll{
    margin: 0.6rem 0.2rem 0 0.2rem;
    max-height: 6.8rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 0.24rem;
    //overflow-y: scroll;
    color: #333333;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    .formTitle {
      width: 6.4rem;
      height: 0.64rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/150051/8/20972/3370/61e50505E2849291a/68c6072a645af1cf.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
    .formInfo {
      width: 6.9rem;
      height: 5rem;
      overflow-y: scroll;
      padding-bottom:0.32rem;
      .listAll {
        width: 6.9rem;
        height: 0.7rem;
        //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/149468/31/21113/713/61e50764E68452264/93185c4bb006cda8.png);
        background-size: 100%;
        margin-top: 0.05rem;
        display: flex;

        .infoLeft {
          display: flex;
          align-items: center;
          height: 100%;
          width: 50%;
          font-size:0.24rem;
          padding-left: 0.24rem;
          img {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            margin-left: 0.2rem;
            margin-right: 0.2rem;
          }
        }
        .infoRight {
          display: flex;
          align-items: center;
          height: 100%;
          width: 50%;
          padding-left: 0.9rem;
          font-size:0.24rem;
        }
      }
    }
    .formInfo1{
      width: 100%;
      text-align: center;
      font-size: 0.24rem;
      color: #8c8c8c;
      line-height: 35vh;
    }
  }
  .more-btn-all{
    width:100%;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
}
</style>
