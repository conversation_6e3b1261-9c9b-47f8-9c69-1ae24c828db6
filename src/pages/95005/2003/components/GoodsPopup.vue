<template>
  <CommonDrawer title="活动商品" @close="emits('close')">
    <div class="content">
      <div class="h-[40vh] px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
        <div v-if="skuList.length || data.length">
          <div class="text-gray-400 text-sm flex justify-center pb-4">
            <div class="grid grid-cols-2 gap-2">
              <div v-for="(item, index) in skuList.length ? skuList : data" class="bg-white py-2 px-3.5" :key="index" @click="gotoSkuPage(item.skuId)">
                <div class="flex justify-center">
                  <img class="w-32 h-32" :src="item.skuMainPicture" alt="" />
                </div>
                <div class="text-xs mt-5 lz-multi-ellipsis--l2" v-text="item.skuName"></div>
                <div class="text-red-500 text-xs mt-3">¥ <span v-text="item.jdPrice"></span></div>
              </div>
            </div>
          </div>
          <div class="no-more" v-if="skuList.length > 4 || data.length > 4">—— 没有更多了 ——</div>
        </div>
        <div v-else class="no-data">
          活动商品为本店全部商品
          <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
        </div>
      </div>
    </div>
  </CommonDrawer>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps(['data']);
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const skuList = ref<any[]>([]);
// 获取曝光商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/95005/getExposureSku', { type: 1 });
    skuList.value = data as any[];
    closeToast();
  } catch (error) {
    closeToast();
  }
};
if (!isPreview) {
  getSkuList();
} else {
  watch(props.data, () => {
    skuList.value = props.data;
  });
}
</script>

<style scoped lang="scss">
.title {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
}
.no-data {
  text-align: center;
  padding-top: 3rem;
  font-size: 0.3rem;
  .btn {
    width: 2.4rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    color: white;
    font-size: 0.3rem;
    border-radius: 0.1rem;
    background-color: #ff9900;
    margin: 0.3rem auto;
  }
}
.no-more{
  margin: 0 auto;
  text-align: center;
  color: rgb(115, 114, 114);
}
.content {
  width: 7rem;
  margin: 0.3rem auto 0;
  overflow-y: scroll;
  font-size: 0.24rem;
  color: #333333;
  white-space: pre-wrap;
}
</style>
