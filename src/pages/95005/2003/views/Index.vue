<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img
        :src="furnish.actBg"
        alt=""
        class="kv-img" />
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
      <div class="rule-btn" @click="showRulePopup()">活动规则 ></div>

    </div>

    <div>
      <div class="prizeBox">
        <div class="box">
          <div class="swiper-container-prize" ref="swiperRef" >
            <div class="swiper-wrapper " >
              <div class="swiper-slide prizeItem" v-for="item in prizeList" :key="item">
                <div class="prizeItem" >
                  <img :src="item.prizeImg  " alt=""/>
                  <div class="prizeName">{{item.prizeName }}</div>
                  <template v-if="baseInfo.status ==2">
                    <div v-if="item.prizeStatus==1" class="sign-up-button" :style="furnishStyles.singnStyle.value" @click="receivePrize(item)" >领取奖品</div>
                    <div v-if="item.prizeStatus==2" class="sign-up-button gray" :style="furnishStyles.singnStyle.value" >已领取</div>
                    <div v-if="item.prizeStatus==3" class="sign-up-button gray " :style="furnishStyles.singnStyle.value" >已领完</div>
                    <!-- 不满足-->
                    <div v-if="item.prizeStatus==4" class="sign-up-button gray" :style="furnishStyles.singnStyle.value" >未达领取资格</div>
                    <div v-if="item.prizeStatus==5" class="sign-up-button gray " :style="furnishStyles.singnStyle.value">不可领取</div>
                  </template>
                </div>
              </div>
            </div>
            <div v-if="prizeList.length>2" class="swiper-button-prev" @click="prevSwiper"></div>
            <div v-if="prizeList.length>2" class="swiper-button-next" @click="nextSwiper"></div>
          </div>
        </div>
      </div>
<!--      <div class="draw-btn" v-if="!isCanRecive">-->
<!--        <CountDown-->
<!--          :endTime="endTime"-->
<!--          :startTime="startTime"-->
<!--        />-->
<!--      </div>-->
      <div class="winners" v-if="[2,3].includes(baseInfo.status)">
        <div class="header-box">
          <div class="header-btn" :style="furnishStyles.headerBtn.value"  v-for="(btn, index) in btnList" :key="index" @click="btn.event">
            {{ btn.name }}
          </div>
        </div>
        <div class="winners-content">
          <div class="winner-header">
            <div class="rank">排名</div>
            <div class="nickName">昵称</div>
            <div class="prizeName">奖品</div>
          </div>
          <div class="winner-list swiper-container-winner" ref="swiperRef" v-if="activityGiftRecords.length >0">
            <div class="swiper-wrapper" >
              <div class="winner swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
                  <span >NO.{{ item.ranking }}</span>
                  <span >{{ item.nickName }}</span>
                  <span >{{ item.prizeName }}</span>
              </div>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无记录</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 活动门槛 -->
    <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup v-if="showRule" :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
     <MyPrize v-if="showMyPrize"  @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress v-if="showSaveAddress" @close="saveAddressClose" :addressId="addressId" :userReceiveRecordId="userReceiveRecordId"></SaveAddress>
    </VanPopup>
    <!--我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup v-if="showOrderRecord" @close="showOrderRecord = false" ></OrderRecordPopup>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { inject, ref, reactive, nextTick, onMounted } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';

import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import useThreshold from '@/hooks/useThreshold';
import furnishStyles, { furnish } from '../ts/furnishStyles';

import RulePopup from '../components/RulePopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import Threshold2 from '@/components/Threshold2/index.vue';
import CountDown from '../components/CountDown.vue';
import MyPrize from '../components/MyPrize.vue';

import 'swiper/swiper.min.css';
import dayjs from 'dayjs';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';

Swiper.use([Autoplay]);
let mySwiper: Swiper;
const userReceiveRecordId = ref('');
const addressId = ref('');

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

// 状态定义
const isCanRecive = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const showLimit = ref(false);
const showOrderRecord = ref(false);
const showSaveAddress = ref(false);

// 数据定义
const prizeList = ref<any>([]);
const prizeObj = ref([] as any);
const activityGiftRecords = reactive([] as ActivityGiftRecord[]);
const ruleTest = ref('');

// 时间定义
const endTime = ref(0);
const startTime = ref(0);

// 函数定义
const prevSwiper = () => {
  mySwiper.slidePrev();
};
const nextSwiper = () => {
  mySwiper.slideNext();
};

// 初始化
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});
// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
// 按钮列表
const btnList = [{
  name: '您的订单 >',
  clickCode: 'wddd',
  event: () => {
    showOrderRecord.value = true;
  },
},
{
  name: '中奖记录 >',
  clickCode: 'zjjl',
  event: () => {
    showMyPrize.value = true;
  },
},
];

// 获取活动信息(
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/95005/activity');
    const nowTime = dayjs().valueOf();
    if (nowTime >= data.awardStartTime && nowTime <= data.awardEndTime) {
      isCanRecive.value = true;
    } else {
      isCanRecive.value = false;
    }
    // 报名开始时间
    startTime.value = data.awardStartTime;
    // 报名结束时间
    endTime.value = data.awardEndTime;

  } catch (error) {
    console.error(error);
  }
};
// 获取奖品信息(
const getPrizeList = async () => {
  try {
    const { data } = await httpRequest.post('/95005/prize');
    prizeList.value = data.prizeList;
    nextTick(() => {
      mySwiper = new Swiper('.swiper-container-prize', {
        allowTouchMove: prizeList.value.length > 2,
        loop: prizeList.value.length > 2,
        slidesPerView: 2 || 'auto',
        spaceBetween: prizeList.value.length > 2 ? 30 : 0,
        centeredSlides: [1, 3].includes(prizeList.value.length),
        navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
      });
    });
  } catch (error) {
    console.error(error);
  }
};
const getWinnerList = async () => {
  try {
    const res = await httpRequest.post('/95005/award');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container-winner', {
        autoplay: activityGiftRecords.length > 5 ? { delay: 1000, stopOnLastSlide: false, disableOnInteraction: false } : false,
        direction: 'vertical',
        loop: activityGiftRecords.length > 5,
        slidesPerView: 5,
        loopedSlides: 7,
        spaceBetween: 10,
      });
    });
  } catch (error) {
    console.error(error);
  }
};

// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityInfo(), getWinnerList(), getPrizeList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
// 领取接口
const receivePrize = async (item:any) => {
  lzReportClick('ljlq');
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    return;
  }
  if (!isCanRecive.value) {
    showToast('请在领奖时间内进行领奖');
    return;
  }
  try {
    const res = await httpRequest.post('/95005/receivePrize', {
      prizeId: item.prizeId,
    });
    if (res.code === 200) {
      showToast('领取成功');
      if (item.prizeType === 3) {
        showSaveAddress.value = true;
        userReceiveRecordId.value = res.data.userReceiveRecordId;
        addressId.value = res.data.addressId;
      } else {
        setTimeout(() => {
          init().then();
        }, 1000);
      }
    }
  } catch (error) {
    console.error(error);
  }
};

// 保存地址弹窗关闭
const saveAddressClose = (type: any) => {
  if (type) {
    showSaveAddress.value = false;
    init();
  } else {
    showSaveAddress.value = false;
  }
};

init();
</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }
}
.sign-up-button {
  margin-top: 0.3rem;
  width: 2.8rem;
  height:0.68rem;
  font-size: 0.26rem;
  background-size: 100%;
  display: flex;
  color: #6a4828;
  background: #e5c986;
  border-radius: .1rem;
  justify-content: center;
  align-items: center;
}
.gray {
  color: #6f6f6f !important;
  background: #ccc !important;
}
.sign-up-button-over {
  width: 7.08rem;
  height: 1.29rem;
  font-size: 0.36rem;
  line-height: 1rem;
  margin: 0 auto;
  text-align: center;
  background-repeat: no-repeat;
  background-size: 100%;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/238948/17/5293/13052/65e80561F9d3971d8/522a53e7b56b1ac0.png');
  color: #999999;
}

.bottom-div {
  margin: 0.8rem auto 0;
  padding-bottom: .5rem;
  font-size: 0.24rem;
  color: #fff;
  text-align: center;
}
.header-box {
  height: 1rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .header-btn {
    width: 3.19rem;
    height: 0.68rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-size: 100%;
  }
}
.rule-btn {
  font-size: 0.24rem;
  color: #fff;
  text-align: right;
  padding-right: 0.2rem;
  text-decoration: underline;
  text-underline-offset: .1rem;//下划线和文字间距
  margin-bottom: .5rem;
}
.prizeBox {
  width: 7.2rem;
  height: 6.38rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/24523/18/21111/9719/6698dbfbFc38ae5ae/92e25cbbae7a3dee.png") no-repeat;
  background-size: 100%;
  font-size: 0.3rem;
  margin: 0 auto;
  padding-top: 1.5rem;
  overflow: hidden;
  position: relative;
  .box {
    width: 80%;
    margin:0 auto;
    overflow: hidden;
  }
  .prizeItem {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 2.5rem;

    img {
      width: 2.5rem;
      background: #ccc;
      border-radius: .1rem;
    }
    .prizeName {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: center;
    }
  }
}
.draw-btn {
  width: 5rem;
  height: 1rem;
  line-height: 1rem;
  margin: 1rem auto 0 auto;
}
.swiper-button-prev {
  position: absolute;
  left: 0.2rem;
  top: 60%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/180230/35/37580/745/66755088F83e4bb06/fe2cefbfd6ece6c5.png') no-repeat;
  background-size: 100%;
  z-index: 1;
}
.swiper-button-next {
  z-index: 1;
  position: absolute;
  right: 0.2rem;
  top: 60%;
  transform: translateY(-50%);
  width: 0.28rem;
  height: 0.52rem;
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/233542/28/20707/755/66755089Fd2d6dab8/628521ff436b49fa.png') no-repeat;
  background-size: 100%;
}
.winners {
  width: 7.2rem;
  height: 7rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/19308/37/21666/10469/6698dbfbF76896232/0460865d17222682.png") no-repeat;
  background-size: 100%;
  margin: 0.5rem auto 0;
  padding-top: 1.5rem;
  .winners-content {
    width: 6.6rem;
    height: 4.34rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
    .winner-header {
      display: flex;
      justify-content: space-between;
      padding: 0.2rem 0.3rem;
      font-size: 0.24rem;
      color: #0f822f;
      font-weight: bold;
      div {
        width: 2.2rem;
        text-align: left;
      }
     .prizeName {
        width: 2.2rem;
        text-align: right;
      }
      .nickName {
        width: 2.2rem;
        text-align: center;
      }
    }
    .winner-list {
      width: 100%;
      height: 80%;
      overflow: hidden;
    }
  }
}
.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem;
  //margin-top: 0.1rem;
  // background: #ffffff;
  border-bottom:1px solid #ccc;

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.winner-null {
  text-align: center;
  line-height: 2.9rem;
  font-size: 0.24rem;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: flex;
  justify-content: space-around;
}
</style>
