import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init } from '@/utils';
import index from './App.vue';
import { InitRequest } from '@/types/InitRequest';
import EventTrackPlugin, { EventTrackParams } from '@/plugins/EventTracking';
import '@/style';
import './style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  backActRefresh: true,
};
const _baseInfo = {
  activityName: '6月消费满额有礼-2024-06-24',
  endTime: 1721784731000,
  followQualify: true,
  id: '1805051604564418561',
  jdActivityId: 4599051,
  jdActivityType: 39,
  levelQualify: true,
  memberLevel: 1,
  openCardLink: 'https://shopmember.m.jd.com/shopcard/?venderId=739130&shopId=734259&channel=80195005',
  payMember: true,
  shopLogo: 'http://img30.360buyimg.com/popshop/jfs/t12040/167/1277706166/88856/87939c85/5a1e9842N3d659b8f.jpg',
  shopName: '广博长泽专卖店',
  startTime: 1719192731000,
  supportLevels: '1,2,3,4,5,-9',
  thresholdLevelsStatus: 0,
  thresholdResponseList: [],
  activityId: '1805051604564418561',
  shopId: '734259',
  activityType: '95005',
  status: 1,
};
init(config).then(({ baseInfo, pathParams, decoData }) => {
  // 设置页面title
  console.log(baseInfo, '_baseInfo');
  document.title = baseInfo?.activityName || '消费满额有礼';
  app.provide('baseInfo', baseInfo);
  app.provide('decoData', decoData);
  app.provide('pathParams', pathParams);
  app.use(EventTrackPlugin, { c: { level: baseInfo?.memberLevel || 0 } });
  app.mount('#app');
});
