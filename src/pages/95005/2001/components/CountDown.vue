<template>
  <div class="count-down-time"  v-if="getTimeFun()>0">
    <span>{{ getCountdownTitle() }}</span>
    <van-count-down :time="getTimeFun()" format="DD:HH:mm:ss"  @finish="countDownFinish">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle" >{{ timeData.days }}</div>
          <span>天</span>
          <div class="acblockStyleStyle" >{{ timeData.hours }}</div>
          <span>时</span>
          <div class="acblockStyleStyle" >{{ timeData.minutes }}</div>
          <span>分</span>
          <div class="acblockStyleStyle" >{{ timeData.seconds }}</div>
          <span>秒</span>
        </div>
      </template>
    </van-count-down>
  </div>
<!--  <div class="count-down-finish" v-else>领奖已结束</div>-->
</template>

<script setup lang="ts">
import { BaseInfo } from '@/types/BaseInfo';
import { inject } from 'vue';

const props = defineProps({
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
});

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

// 倒计时文案
const getCountdownTitle = () => {
  if (props.startTime > new Date().getTime()) {
    return '距离领奖时间:';
  }
  if (props.endTime > new Date().getTime()) {
    return '距离领奖结束';
  }
  return '距离领奖时间:';
};

// 倒计时时间
const getTimeFun = () => {
  if (props.startTime > new Date().getTime()) {
    return props.startTime - new Date().getTime();
  }
  if (props.endTime > new Date().getTime()) {
    return props.endTime - new Date().getTime();
  }
  return 0;
};
const countDownFinish = () => {

  if (baseInfo.status === 3) {
    return;
  }
  if (props.endTime <= new Date().getTime()) {
    return;
  }
  console.log('倒计时结束');
  window.location.reload();
};
</script>

<style scoped lang="scss">
.count-down-time {
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: center;
  color: #fff;
  .contentSpan {
    display: flex;
    .acblockStyleStyle {
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
      color:#d0b16a;
    }
    span {
      width: 0.4rem;
      height: 0.44rem;
      color: #d0b16a;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
  }
}
.count-down-finish {
  font-size: 0.25rem;
  color: #fff;
  text-align: center;
}
</style>
