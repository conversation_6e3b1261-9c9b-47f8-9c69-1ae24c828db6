<template>
 <div class="bg">
   <div class="kvBgStyle">
     <img @click="getPrize()" v-if="giftImageUrl"  class="giftImageUrlDiv" :src="giftImageUrl" alt="">
     <div class="joinBtn" @click="getPrize()"></div>
     <div v-if="isShowClose" class="closeDiv" @click="toast"></div>
   </div>
 </div>
</template>
<script lang="ts" setup>
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { ref, inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { jumpGetCoupon, closeGiftPop } from '@/utils/shopGiftBag.js';

const decoData = inject('decoData') as DecoData;
const pathParams = inject('pathParams') as any;
console.log(pathParams, 'pathParams');
const isShowClose = ref(pathParams.isGift);
const giftImageUrl = ref(''); // 礼包图片
const linkUrl = ref(''); // 跳转链接
const userReceiveRecordId = ref(''); // 领取奖品的id
// 获取数据
const getAllData = async () => {
  try {
    const { data } = await httpRequest.post('/50007/query');
    // console.log(data, 'data===========');
    giftImageUrl.value = data.imgUrl;
    linkUrl.value = data.link;
    userReceiveRecordId.value = data.userReceiveRecordId;
  } catch (errMsg) {
    // console.log(errMsg, 'data===========errMsg');
    showToast(errMsg.message);
  }
};
const toast = () => {
  if (pathParams.isGift) {
    showToast('仅供预览');
  }
};
// 领取奖品
const getPrize = async () => {
  if (pathParams.isGift) {
    showToast('仅供预览');
    return;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/50007/receive', {
      userReceiveRecordId: userReceiveRecordId.value,
    });
    // showToast('收下礼包成功');
    closeToast();
    closeGiftPop(this, () => {
      console.log('关闭弹窗');
    });
    jumpGetCoupon(linkUrl.value, () => {
      console.log('跳转链接');
    });
  } catch (errMsg) {
    // console.log(errMsg, 'errMsg=======');
    showToast(errMsg.message);
  }
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getAllData()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();

</script>
<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg{
  width: 100%;
  min-height: 100vh;
  position: absolute;
  top: 0;
  background: rgba(0,0,0,0.7);
}
  .kvBgStyle{
    background-size: 100%;
    background-repeat: no-repeat;
    position: absolute;
    width: 6.30rem;
    height: 9rem;
    left: calc(50% - 6.30rem / 2);
    top: calc(50% - 9rem / 2);
  }
  .giftImageUrlDiv{
    width: 6.30rem;
    height: 9rem;
  }
.closeDiv{
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  top: -0.8rem;
  right: 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/240116/39/1181/7700/65a6402cF2d4309d7/f9f24e9fcb55e30c.png");
}
.joinBtn{
  position: absolute;
  top: 7.8rem;
  left: 1.9rem;
  width: 2.3rem;
  height:0.6rem;
}
</style>
