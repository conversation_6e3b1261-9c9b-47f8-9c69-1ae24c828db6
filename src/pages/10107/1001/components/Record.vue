<template>
  <div class="popup-bk" :style="{ backgroundImage: `url(${bkMap[actTab]})` }">
    <div class="title">
      <div class="tab" @click="actTab = 1"></div>
      <div class="tab" @click="actTab = 2"></div>
      <div class="close" @click="recordPopup = false"></div>
    </div>
    <div class="content" v-if="actTab === 1">
      <div class="list" v-for="item in recordData" :key="item.num">
        <div class="num-title" v-if="item.detailList.length">活动第{{ numToChinese(item.num) }}期</div>
        <div v-for="(it, ind) in item.detailList" :key="ind" class="num-list">
          <div class="prize-img">
            <img :src="it.giftImage" alt="" />
          </div>
          <div class="info">
            <div>赠品名称：{{ it.giftName }}</div>
            <div>领取时间：{{ dayjs(it.receiveTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div class="copy">
              <div>关联订单：{{ it.orderId }}</div>
              <div class="copy-btn" :copy-text="it.orderId">复制</div>
            </div>
          </div>
          <div class="cancel" v-if="it.orderStatus === 'CANCEL'">
            <div class="fs28">订单已取消</div>
            <div>订单取消时间：{{ dayjs(it.receiveTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
        </div>
      </div>
      <div v-if="recordNoData" class="no-data">暂无记录</div>
    </div>
    <div class="content-2" v-if="actTab === 2">
      <div class="info" v-if="prizeRecord.status === 0">
        <div>
          <div class="name">{{ prizeRecord.nickName }}</div>
          <div class="time">{{ dayjs(prizeRecord.joinTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
        </div>
        <div class="finish">已支付0.01锁权</div>
      </div>
      <div v-else class="no-data">暂无记录</div>
      <div class="content-2-desc">支付记录请至“我的-钱包-账单”中查看</div>
    </div>
    <div class="bottom-btn" @click="recordPopup = false"></div>
  </div>
</template>

<script lang="ts" setup>
import { recordPopup, promotionList } from '../ts/logic';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { inject, reactive, ref } from 'vue';
import Clipboard from 'clipboard';
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import dayjs from 'dayjs';

const isPreview = inject('isPreview') as boolean;
const baseInfo = inject('baseInfo') as BaseInfo;

const bkMap = {
  1: '//img10.360buyimg.com/imgzone/jfs/t1/236709/35/1503/161521/654375deF5cb0cd6c/af81210df9b7a4e7.png',
  2: '//img10.360buyimg.com/imgzone/jfs/t1/201968/33/38021/87425/654458e3F45ad4f19/e6fb5651f9282883.png',
};
const actTab = ref(1);

const userName = ref(window.sessionStorage.getItem(constant.LZ_JD_USER_NAME) as string);

interface List {
  giftImage: string;
  giftName: string;
  orderId: string;
  receiveTime: string;
  orderStatus: string;
}

interface Record {
  num: number;
  detailList: List[];
}

// 数字转中文数字，例如12转成十二
const numToChinese = (num: number): string => {
  const numCh = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
  return numCh[num];
};

const recordData = reactive([] as Record[]);
const recordNoData = ref(true);

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const pro = promotionList.find((item) => item.status === 1) || promotionList.find((item) => item.status === 0) || promotionList[promotionList.length - 1];
    const res = await httpRequest.post('/10107/receiveRecord', { num: pro?.num });
    closeToast();
    recordData.splice(0);
    recordData.push(...res.data);
    recordData.sort((a, b) => b.num - a.num);
    recordNoData.value = true;
    recordData.forEach((item) => {
      if (item.detailList.length) {
        recordNoData.value = false;
      }
    });
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

const prizeRecord = reactive({
  nickName: '',
  status: 1,
  joinTime: '',
});
const getPrizeRecord = async () => {
  try {
    const res = await httpRequest.post('/10107/prizeRecord');
    if (res.data) {
      prizeRecord.nickName = res.data.nickName;
      prizeRecord.status = res.data.status;
      prizeRecord.joinTime = res.data.joinTime;
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

if (!isPreview) {
  getRecord();
  getPrizeRecord();
}
</script>

<style scoped lang="scss">
.popup-bk {
  width: 6.32rem;
  height: 9rem;
  background-repeat: no-repeat;
  background-size: 100%;
  .title {
    width: 100%;
    height: 0.74rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.1rem 0 0.6rem;
    .tab {
      width: 2.5rem;
      height: 0.6rem;
    }
    img {
      height: 0.34rem;
    }
    .close {
      width: 0.4rem;
      height: 0.4rem;
    }
  }
  .content {
    width: 6.32rem;
    height: 7rem;
    margin: 0 auto;
    padding: 0.2rem;
    overflow-y: scroll;
    .list {
      .num-title {
        font-size: 0.24rem;
        color: #666666;
        margin-bottom: 0.1rem;
      }
      .num-list {
        position: relative;
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 0.2rem;
        overflow: hidden;
        padding: 0.1rem;
        margin-bottom: 0.1rem;
        .prize-img {
          width: 1.2rem;
          height: 1.2rem;
          background-color: #fff8f0;
          border-radius: 0.1rem;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            padding: 0.15rem;
          }
        }
        .info {
          padding-left: 0.17rem;
          flex: 1;
          font-size: 0.22rem;
          color: #262626;
          line-height: 0.4rem;
          div {
            // 文字最多2行展示
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
          .copy {
            display: flex;
            align-items: center;
            div {
              max-width: 3.75rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .copy-btn {
              color: #3399ff;
              width: max-content;
              padding: 0 0.1rem;
            }
          }
        }
        .cancel {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.8);
          padding: 0.3rem 0.25rem 0;
          color: #fff;
          font-size: 0.22rem;
          .fs28 {
            font-size: 0.28rem;
            margin-bottom: 0.05rem;
          }
        }
      }
    }
  }
  .content-2 {
    width: 6.32rem;
    height: 7rem;
    margin: 0 auto;
    padding: 0.7rem 0.45rem 0;
    position: relative;
    .info {
      height: 1.2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.24rem;
      border-bottom: solid 0.01rem #e6e6e6;
      .name {
        color: #262626;
        margin-bottom: 0.1rem;
      }
      .time {
        font-size: 0.2rem;
        color: #999999;
      }
      .finish {
        color: #ff3333;
      }
    }
    .content-2-desc {
      position: absolute;
      bottom: 0.2rem;
      left: 0;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0.24rem;
      color: #999999;
      scale: 0.7;
      white-space: nowrap;
    }
  }
  .no-data {
    width: 100%;
    height: 5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.24rem;
    color: #999999;
  }
  .bottom-btn {
    width: 4.5rem;
    height: 0.9rem;
    margin: 0.4rem auto 0;
  }
}
</style>
