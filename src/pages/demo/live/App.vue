<template>
  <div class="task-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/187427/8/38272/7424/65016dfdFc39aa757/76ae8a62aef72ccf.png" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="content">
      <div class="task">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/217700/37/34488/5043/65017d54F294b3701/7849a978453787cf.png" alt="" class="icon" />
        <div class="info">
          <div class="name">半屏开卡</div>
          <div class="rule">半屏开卡互动测试</div>
        </div>
        <div class="button" @click="openCard">点我开卡</div>
      </div>
      <div class="task">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/217700/37/34488/5043/65017d54F294b3701/7849a978453787cf.png" alt="" class="icon" />
        <div class="info">
          <div class="name">兰蔻直播间互动</div>
          <div class="rule">兰蔻直播间互动半屏互动测试</div>
        </div>
        <a class="button" href="https://lzkj-isv.isvjcloud.com/test/cc/cjwx/m/1000306683/99/2405100030668301/">跳转活动</a>
      </div>
      <div class="task">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/217700/37/34488/5043/65017d54F294b3701/7849a978453787cf.png" alt="" class="icon" />
        <div class="info">
          <div class="name">Sk2直播间互动</div>
          <div class="rule">Sk2直播间互动半屏互动测试</div>
        </div>
        <a class="button" href="https://lzkjdz-isv.isvjcloud.com/prod/cc/custom/1000009821/1788096771846885378/">跳转活动</a>
      </div>
      <div class="task">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/206717/25/33411/5110/65017d54Fa6e7c7a9/26e09681655cd6d5.png" alt="" class="icon" />
        <div class="info">
          <div class="name">获取用户身份</div>
          <div class="rule">获取用户身份</div>
        </div>
        <div class="button" @click="getUserInfo">获取用户身份</div>
      </div>
      <div class="task">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/221469/1/35735/4710/65017d54F01f5ef2e/c73926c238b9faac.png" alt="" class="icon" />
        <div class="info">
          <div class="name">跳转到店铺</div>
          <div class="rule">广博长泽专卖店</div>
        </div>
        <div class="button" @click="toShop">跳转</div>
      </div>
      <div class="task">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/185698/9/38659/4411/65017d54F2243b4bd/2a3893069b280bc3.png" alt="" class="icon" />
        <div class="info">
          <div class="name">跳转到商品</div>
          <div class="rule">A6手帐套装-酷企鹅</div>
        </div>
        <div class="button" @click="toSku">跳转</div>
      </div>
      <div class="task">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/189530/36/37768/4592/65017d54F28df3ceb/313afa6d3acd4f3f.png" alt="" class="icon" />
        <div class="info">
          <div class="name">跳转到会员中心</div>
          <div class="rule">开卡页面(SDK方式)</div>
        </div>
        <div class="button" @click="toMyJd">跳转</div>
      </div>
      <div class="task">
        <img src="//img10.360buyimg.com/imgzone/jfs/t1/189530/36/37768/4592/65017d54F28df3ceb/313afa6d3acd4f3f.png" alt="" class="icon" />
        <div class="info">
          <div class="name">跳转到会员中心开卡</div>
          <div class="rule">开卡页面(href方式)</div>
        </div>
        <a class="button" href="https://shopmember.m.jd.com/shopcard/?venderId=739130&shopId=734259&channel=801">跳转</a>
      </div>
      <div class="task2">
        <van-field v-model="jumpUrl" label="地址" label-width="30" placeholder="请输入页面地址" clearable />
        <div class="buttons">
          <a class="button" @click="toAny">SDK跳转</a>
          <a class="button" @click="toJump">浏览器跳转</a>
        </div>
      </div>
    </div>
  </div>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showPopup" closeable round position="bottom" :style="{ height: '30%', backgroundColor: '#FFF' }">
    <div class="popup-wrapper">这里是弹窗，我有300px高</div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import JD from '@/utils/platforms';
import { UserInfo } from '@/utils/products/types/UserInfo';
import { httpNoAuthRequest } from '@/utils/service';
import { CLIENT_TYPE, getClientType } from '@/utils/platforms/clientType';
import { showDialog, showToast } from 'vant';

const userInfo = ref<UserInfo>();
const showPopup = ref<boolean>(false);
const jumpUrl = ref<string>(localStorage.getItem('liveJumpUrl') || '');
const jumpWithSDK = ref<boolean>(false);
// eslint-disable-next-line func-names
window.testCallback = function testCallback(result: any) {
  console.log('半屏回调结果:', result);
};
const openCard = () => {

  const params = {
    url: encodeURIComponent('https://crmsam.jd.com/union/index.html?url=false&activityId=&token=07B0D930077284D50FE9135B4BAD0675#/'),
    targetName: window.location.href,
    targetType: 2,
    heightRateFromContainer: 0.7,
    layoutType: 1,
    backgroundColor: '0000009b',
    pageMultiTimes: 1,
    needCache: 0,
    needAutoClose: 1,
  };

  console.log(window.testCallback);
  window.XWebView.callNative(
    'JDXViewPlugin',
    'showXView2',
    JSON.stringify(params),
    'testCallback',
    '1',
  );
};

const toShop = () => {
  window.jmfe.toShop('734259');
};

const toSku = () => {
  window.jmfe.toSku('10097246839573');
};

const toMyJd = () => {
  window.jmfe.toAny('https://shopmember.m.jd.com/shopcard/?venderId=739130&shopId=734259');
};

const toChat = () => {
  window.jmfe.toMyCart({
    skuId: '10097246839573',
    skuNum: 1,
  });
};

const toAny = () => {
  if (jumpUrl.value) {
    localStorage.setItem('liveJumpUrl', jumpUrl.value);
    window.jmfe.toAny(jumpUrl.value);
  } else {
    showToast('请输入正确的地址');
  }
};
const toJump = () => {
  if (jumpUrl.value) {
    localStorage.setItem('liveJumpUrl', jumpUrl.value);
    window.location.href = jumpUrl.value;
  } else {
    showToast('请输入正确的地址');
  }
};

interface LoginRequest {
  activityType: string;
  templateCode: string;
  activityId: string;
  shopId: string;
}

const getUUID = async () => {
  if (getClientType() === CLIENT_TYPE.JDAPP) {
    try {
      const { status, data } = await window.jmfe.getDeviceInfo();
      if (status === '0') {
        // data就是获取到的设备信息
        return data?.uuid;
      }
    } catch (e) {
      console.error(e);
    }
  }
  return '';
};

const textXviewClose = () => {
  window.jmfe.xviewClose().then();
};
/**
 * 获取pin
 * @param loginRequest
 */
const getJdPin = async (loginRequest: LoginRequest): Promise<UserInfo> => {
  // 获取getPin所需参数
  const params = await JD.getToken();

  // 2. 获取UUID
  const uuid = await getUUID();

  const { data } = await httpNoAuthRequest.post('/user/login', {
    token: params.token,
    source: params.source,
    activityType: loginRequest.activityType,
    templateCode: loginRequest.templateCode,
    activityId: loginRequest.activityId,
    shopId: loginRequest.shopId,
    uuid,
    timestamp: new Date().getTime(),
  });
  return data;
};

const getUserInfo = async () => {
  showToast('获取用户信息中');
  const loginRequest: LoginRequest = {
    activityType: '1',
    templateCode: '1',
    activityId: '1',
    shopId: '734259',
  };
  userInfo.value = await getJdPin(loginRequest);
  await showDialog({
    className: 'dialog-white',
    title: '获取用户信息成功',
    message: JSON.stringify(userInfo.value),
  });
};

const close = () => {
  try {
    window.jmfe.closeWebview();
  } catch (e) {
    console.log(' window.jmfe.closeWebview error');
  }
  try {
    window.jmfe.xviewClose();
  } catch (e) {
    console.log(' window.jmfe.xviewClose error');
  }
};

</script>

<style lang="scss" scoped>
.dialog-white {
  background-color: white;
}

.task-bk {
  .title {
    position: relative;
    flex-shrink: 0;
    flex-grow: 0;
    height: 0.86rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;

    .text {
      height: 0.6rem;
    }

    .close {
      width: 0.55rem;
      height: 0.55rem;
    }
  }

  .content {
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    flex-shrink: 1;
    flex-grow: 1;

    .task {
      background-color: #fff;
      margin-bottom: 0.1rem;
      border-radius: 0.1rem;
      padding: 0.2rem 0.3rem;
      display: flex;
      align-items: center;

      .icon {
        width: 0.83rem;
        flex-shrink: 0;
        flex-grow: 0;
      }

      .info {
        padding: 0 0.39rem;
        flex-shrink: 1;
        flex-grow: 1;

        input {
          width: 100%;
        }
      }

      .name {
        font-size: 0.3rem;
        color: #000;
      }

      .rule {
        font-size: 0.24rem;
        color: rgba(0, 0, 0, 0.52);
      }
    }
  }
}

.button {
  flex-shrink: 0;
  flex-grow: 0;
  width: 1.86rem;
  height: 0.55rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/198040/4/39277/3952/65017bc7F0d560988/e67ffed4c850b15a.png) no-repeat;
  background-size: 100%;
  font-size: 0.28rem;
  color: rgb(255, 255, 255);
  border-radius: 0.23rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.task2 {
  background-color: #fff;
  margin-bottom: 0.1rem;
  border-radius: 0.1rem;
  padding: 0.2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  .buttons {
    display: flex;
    gap: 10px;
  }
}

.popup-wrapper {
  padding: 0.2rem;
}
</style>
