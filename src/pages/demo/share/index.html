<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <!--  设置页面编码-->
  <meta charset="utf-8" />
  <title></title>
  <!--  设置页面兼容模式-->
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <!--  禁止缓存html页面-->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <!--  禁止手机号码识别-->
  <meta name="format-detection" content="telephone=no">
  <!--  DNS 预读取-->
  <meta http-equiv="x-dns-prefetch-control" content="on">
  <!--  尝试在请求资源之前解析域名-->
  <link rel="dns-prefetch" href="//img10.360buyimg.com">
  <!--  移动端视口1:1-->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
  <!--  微信sdk-->
  <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
  <!--  京东sdk-->
  <script src="https://lzcdn.dianpusoft.cn/jdsdk/jssdk.4.7.4.js"></script>
  <!--  京东APP分享sdk-->
  <script src="https://lzcdn.dianpusoft.cn/jdshare/2.3.0/jm-jdshare.js"></script>
  <!--  京东小程序内嵌HTML5页面的时候使用的桥接js-->
  <script src="https://lzcdn.dianpusoft.cn/jdsdk/jssdk.mpBridge.js"></script>
  <!--  移动端日志打印-->
  <script src="https://lzcdn.dianpusoft.cn/vConsole/3.14.6/vconsole.min.js"></script>
  <!--  通用css动画库-->
  <link rel="stylesheet" href="https://lzcdn.dianpusoft.cn/animate/4.0.0/animate.compat.css" />
  <!--  jD 神盾-->
  <script src="https://lzcdn.dianpusoft.cn/jdsdk/shield-js-sdk_jos-js-sdk.1.0.0.min.js"></script>
  <!--  子午线埋点初始化配置-->
  <script type="text/javascript">
    var jap = {
      siteId: 'JA2019_2132395',
      autoLogPv: false,
    };
    jmfe.registerCode('b188f86e-75d7-4c2c-9663-af0815cf6341');

    const vc = new window.VConsole();
  </script>
  <!--  京东子午线埋点sdk-->
  <script src="https://wl.jd.com/unify.min.js"></script>
</head>

<body id="interact-c">
  <script src="https://lzcdn.dianpusoft.cn/vConsole/3.14.6/vconsole.min.js"></script>
  <div id="app">
    <img alt="" src="//img10.360buyimg.com/imgzone/jfs/t1/221552/16/39312/31264/662ba9c8F5516a0f6/62aa3cd1ed994d97.png"
      width="42" height="42" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%)" />
    <div
      style="text-align: center; font-size: 16px; color: rgba(0, 0, 0, 0.5); position: absolute; top: calc(50% + 35px); left: 50%; transform: translate(-50%, -50%)">加载中...</div>
</div>
<div id="threshold"></div>
    <!-- built files will be auto injected -->
</body>

</html>
