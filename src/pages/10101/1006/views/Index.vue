<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <!-- <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" /> -->
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div class="header-btn-all">
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
            <div>{{ btn.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="select-hover wheelClass">
      <div class="wheel">
        <lz-lucky-wheel ref="myLucky" width="80vw" height="80vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
      </div>
      <div class="figure">
        <div class="all-join-num" v-show="+furnish.showPersonNum === 1 || +furnish.personNum < joinNum" >
          已有<span>{{ joinNum }}</span
          >人参与
        </div>
        <div class="draws-num" :style="furnishStyles.drawsNum.value">
          当前剩余抽奖机会： <span style="color: #eb2b0d">{{ chanceNum }}</span> 次
        </div>
      </div>
    </div>
    <div class="shopMessageDiv" :style="furnishStyles.shopMessageBg.value">
      <div class="leftDiv">
        <div class="shopLogoImageDiv">
          <img :src="shopLogo" alt="" />
        </div>
        <div class="shopNameDiv" v-if="furnish.disableShopName === 1" :style="furnishStyles.shopNameColor.value">{{ shopName }}</div>
      </div>
      <div class="gonToShopDiv" @click="gotoShopPage(baseInfo.shopId)" :style="furnishStyles.shopMessageGoToShop.value"></div>
    </div>
    <div class="sku" v-if="skuList.length > 0">
      <div class="sku-list-box" :style="furnishStyles.winnersBg.value">
        <div class="sku-list">
          <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="gotoSkuPage(item.skuId)">
            <div class="sku-item-image"><img :src="item.skuMainPicture" alt="" /></div>
            <div class="sku-text">{{ item.skuName }}</div>
            <div class="sku-price">￥{{ item.jdPrice }}</div>
          </div>
          <div class="more-btn-all">
            <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="furnish.showWinnersBg === 1" class="winners" :style="furnishStyles.realWinnersBg.value">
      <div class="winners-content">
        <div class="winner-list swiper-container" ref="swiperRef">
          <div class="swiper-wrapper" v-if="activityGiftRecords.length != 0">
            <div class="winnerRecord swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
              <div>
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" v-if="!item.avatar" />
                <img v-else :src="item.avatar" alt="" />
                <span>{{ item.nickName }}</span>
              </div>
              <span>{{ item.prizeName }}</span>
            </div>
          </div>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
    <OrderRecordPopup v-if="showOrderRecord" @close="showOrderRecord = false" :orderRestrainStatus="orderRestrainStatus"></OrderRecordPopup>
  </VanPopup>
  <!--抽奖记录弹窗-->
  <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
    <DrawRecordPopup v-if="showDrawRecord" @close="showDrawRecord = false"></DrawRecordPopup>
  </VanPopup>
  <!-- 活动商品弹窗-->
  <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
    <GoodsPopup :data="[]" @close="showGoods = false" :orderSkuisExposure="orderSkuisExposure"></GoodsPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
</template>

<script setup lang="ts">
import { inject, nextTick, reactive, ref } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
// 0-全部商品 1-指定商品  2-排除
const orderSkuisExposure = ref(0);
const shopName = ref(baseInfo.shopName);
const shopLogo = ref(baseInfo.shopLogo);
const showDrawRecord = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

// 抽奖次数
const chanceNum = ref(0);
// 参与人数
const joinNum = ref(0);
// 订单状态
const orderRestrainStatus = ref(0);

const showMyPrize = ref(false);

const showGoods = ref(false);
const showOrderRecord = ref(false);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  skuId: number;
  jdPrice: string;
};

const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const pagesAll = ref(0);

const orderSkuList = ref<Sku[]>([]);
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      console.log('活动规则');
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    event: () => {
      console.log('我的奖品');
      showMyPrize.value = true;
    },
  },
  {
    name: '活动商品',
    event: () => {
      console.log('活动商品');
      showGoods.value = true;
    },
  },
  {
    name: '我的订单',
    event: () => {
      console.log('我的订单');
      showOrderRecord.value = true;
    },
  },
  {
    name: '抽奖记录',
    event: () => {
      showDrawRecord.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/10101/chanceNum');
    chanceNum.value = data.chanceNum;
    joinNum.value = data.joinNum;
    orderRestrainStatus.value = data.orderRestrainStatus;
    orderSkuisExposure.value = data.orderSkuisExposure;
  } catch (error) {
    console.error(error);
  }
};

// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/10101/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: activityGiftRecords.length > 5 ? { delay: 1000, stopOnLastSlide: false, disableOnInteraction: false } : false,
        direction: 'vertical',
        loop: activityGiftRecords.length > 5,
        slidesPerView: 5,
        loopedSlides: 7,
        spaceBetween: 10,
      });
    });
  } catch (error) {
    console.error(error);
  }
};

const myLucky = ref();
// 抽奖接口
const lotteryDraw = async () => {
  try {
    const res = await httpRequest.post('/10101/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      myLucky.value.stop(index);
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      myLucky.value.stop(index);
    }
  } catch (error) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    myLucky.value.stop(index);
    console.error(error);
  }
  getChanceNum();
  getWinners();
};
const startCallback = async () => {
  lzReportClick('kscj');
  if (chanceNum.value <= 0) {
    showToast('您的抽奖次数已用完');
    return;
  }
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  lotteryDraw();
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/10101/getPrizes');
    prizeInfo.splice(0);
    prizeInfo.push(...data);
  } catch (error) {
    console.error(error);
  }
};

// 获取曝光商品
const getSkuList = async () => {
  try {
    const res = await httpRequest.post('/10101/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    if (res.code === 200) {
      skuList.value.push(...res.data.records);
      pagesAll.value = res.data.pages;
    }
  } catch (error) {
    console.error(error);
  }
};
const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getChanceNum(), getPrizes(), getWinners(), getSkuList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
  if (baseInfo.status === 1) {
    const time = baseInfo.startTime - dayjs().valueOf();
    setTimeout(() => {
      window.location.reload();
    }, time);
  }
};
init();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
}
</style>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}
.shopMessageDiv {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/134603/30/40666/14045/65dd7598F83b1b120/5ff9cf869c3d5c5b.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 7.1rem;
  height: 1.38rem;
  margin-left: calc(50% - 7.1rem / 2);
  margin-top: 1.41rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.48rem;
  .leftDiv {
    display: flex;
    img {
      height: 0.64rem;
    }
    .shopNameDiv {
      margin-left: 0.18rem;
      color: #03581b;
      font-size: 0.23rem;
      display: flex;
      align-items: center;
    }
  }

  .gonToShopDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/173836/6/42083/8900/65dd7598Fc4304bcd/567f46b02589ae46.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 1.76rem;
    height: 0.53rem;
  }
}
.header-kv {
  position: relative;
  height: 11.7rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0rem;
    width: 100%;
    text-align: center;
    margin-top: 0.1rem;
  }
  .header-btn-all {
    position: absolute;
    top: 2.62rem;
    right: 0;
    z-index: 10;
    .header-btn {
      width: 1.06rem;
      background-size: 100%;
      background-repeat: no-repeat;
      height: 0.35rem;
      margin-bottom: 0.1rem;
      font-size: 0.18rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}

.wheelClass {
  position: relative;
  margin-top: -8.4rem;
  .wheel {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .wheel-img {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      object-fit: contain;
    }
  }
}

.sku {
  width: 7.5rem;
  margin: 0 auto;
  .more-btn-all {
    width: 6.55rem;
    display: flex;
    justify-content: center;
    margin-top: 0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-list-box {
    background-size: 100%;
    background-repeat: no-repeat;
    width: 6.89rem;
    height: 9.76rem;
    margin: 0.33rem auto 0;
    padding-top: 0.83rem;
    .sku-list {
      width: 6.3rem;
      height: 8.4rem;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      display: flex;
      margin: 0.2rem auto 0.1rem auto;
      overflow: hidden;
      overflow-y: scroll;
      .sku-item {
        width: 2.95rem;
        height: 3.87rem;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/103211/5/46257/14177/65dd4d7cFd4012ad9/6ef7ac8a07b2f4f2.png');
        background-repeat: no-repeat;
        background-size: 100%;
        margin-bottom: 0.2rem;
        overflow: hidden;
        padding-top: 0.3rem;
        .sku-item-image {
          display: flex;
          justify-content: center;
          width: 100%;
          height: 2.4rem;
          img {
            display: block;
            //width: 1.77rem;
            height: 2.13rem;
          }
        }
        .sku-text {
          width: 2.3rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          font-size: 0.2rem;
          color: #333333;
          padding: 0 0.38rem;
          margin: 0.1rem 0 0 0;
          box-sizing: border-box;
        }
        .sku-price {
          font-size: 0.3rem;
          color: #ff5f00;
          padding: 0 0.38rem;
        }
      }
    }
  }
}
.all-join-num {
  text-align: center;
  font-size: 0rem;
  margin-bottom: 0.2rem;
  color: #000;
  span {
    color: #fff;
  }
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
  .winnerRecord {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.3rem;
    background-color: #fff;
    margin-bottom: 0.05rem;
    border-radius: 0.04rem;
    font-size: 0.26rem;
    img {
      width: 0.6rem;
      height: 0.6rem;
      object-fit: cover;
      border-radius: 1.2rem;
      display: inline;
      vertical-align: middle;
      margin-right: 0.1rem;
    }

    span {
      vertical-align: middle;
      font-size: 0.28rem;
      color: #333333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .winner-null {
    text-align: center;
    line-height: 3.9rem;
    font-size: 0.24rem;
    color: #8c8c8c;
  }
}
.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1.1rem;

  .winners-content {
    width: 6.6rem;
    height: 4.7rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.draws-num {
  width: 5.94rem;
  height: 0.66rem;
  display: flex;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/238619/14/1222/6322/658a33f6Fb2734429/a7ed3989ea59702f.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0.46rem 0.8rem 0;
  align-items: center;
  justify-content: center;
  padding: 0.06rem 0;
  font-size: 0.24rem;
  span {
    background: #fffad8;
    border-radius: 0.06rem;
    color: #da2020;
    padding: 0.04rem 0.08rem;
    margin: 0 0.08rem;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.op0 {
  opacity: 0;
}
</style>
