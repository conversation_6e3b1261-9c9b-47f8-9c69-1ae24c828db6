<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/185594/32/39467/10390/65376dd9Fe543e617/8bf1b1620f4e1fbb.png" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="type">
          <span>{{ prizeType[item.prizeType] }}</span>
          <span>{{ item.userPrizeId ? item.userPrizeId : '' }}</span>
        </div>
        <div class="info">
          <img :src="item.prizeImg" alt="" class="show-img" />
          <div class="detail">
            <div class="name">{{ item.prizeName }}</div>
            <div class="time">获奖时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="status" v-if="item.prizeType === 3">
            <div class="black" v-if="!item.deliveryStatus && item.status !== 3">待发货</div>
            <div class="red" v-if="item.status === 3">已取消</div>
            <div class="green" v-if="item.deliveryStatus && item.status !== 3">已发货</div>
            <div class="blue" v-if="!item.deliveryStatus && item.status !== 3" @click="changAddress(item)">
              <span v-if="item.realName" class="blue1">查看地址</span>
              <span v-else class="purple">填写地址</span>
            </div>
            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 7">
            <div class="black" v-if="item.status === 1">待发放</div>
            <div class="black" v-else-if="item.status === 2">已发放</div>
            <div class="red" v-else-if="item.status === 3">已取消</div>
            <div class="blue" @click="showCardNum(item)" v-if="item.status === 2">如何兑换</div>
            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 9 || item.prizeType === 10">
            <div class="black" v-if="item.status === 1">待发放</div>
            <div class="black" v-else-if="item.status === 2">已发放</div>
            <div class="red" v-else-if="item.status === 3">已取消</div>
            <div class="blue" @click="exchangePlusOrAiqiyi" v-if="item.status === 2">立即兑换</div>
            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>
          </div>
          <div class="status" v-else-if="item.prizeType === 12">
            <div class="black" v-if="item.status === 1">待发放</div>
            <div class="black" v-else-if="item.status === 2">已发放</div>
            <div class="red" v-else-if="item.status === 3">已取消</div>
            <div class="blue" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive && item.status !== 3">填写信息</div>
            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>
          </div>
          <div class="status" v-else>
            <div class="black" v-if="item.status === 1">待发放</div>
            <div class="black" v-else-if="item.status === 2">已发放</div>
            <div class="red" v-else-if="item.status === 3">已取消</div>
            <div class="blue" @click="item.showOrder = !item.showOrder">{{ item.showOrder ? '收起订单' : '关联订单' }}</div>
          </div>
        </div>
        <div class="deliver" v-if="item.prizeType === 3 && item.deliveryStatus">
          <div>
            <div>快递公司：{{item.deliverName}}</div>
            <div>快递单号：{{item.deliverNo}}</div>
          </div>
          <div class="copy-btn" :copy-text="item.deliverNo">复制单号</div>
        </div>
        <div class="order-info" v-if="item.showOrder">
          <div class="order-info-item" v-for="(itemO,indexO) in item.orderInfo" :key="indexO">
          <div>订单号：{{ itemO.orderId }}</div>
          <div>订单状态：{{ itemO.orderStatus }}</div>
          <div>订单金额：{{ itemO.orderPrice }}</div>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import Clipboard from 'clipboard';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  status: number;
  showOrder: boolean;
  realName: string;
  orderInfo: {
    orderId: string;
    orderStatus: string;
    orderPrice: string;
  };
  deliverName: string;
  deliverNo: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10101/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
    prizes.forEach((item) => {
      item.showOrder = false;
    });
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  addressId.value = item.addressId;
  activityPrizeId.value = item.userPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userPrizeId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/222600/40/35224/219814/65016dfeF9234602d/d99de4f864849a24.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 0.86rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.6rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }

  .content {
    height: 8.45rem;
    width: 7rem;
    margin: 0.3rem auto 0;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .prize {
      width: 6.9rem;
      background-color: #ffffff;
      margin-bottom: 0.1rem;
      padding-bottom: 0.12rem;
      border-radius: 0.16rem;

      .flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.08rem 0.22rem;
      }

      .type {
        color: #999999;
        font-size: 0.2rem;
        text-align: left;
        border-bottom: #eee 0.02rem dashed;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding: 0.1rem 0;
      }
      .time {
        color: #999999;
        font-size: 0.2rem;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        //height: 0.9rem;
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        padding-top: 0.08rem;

        .show-img {
          width: 0.6rem;
          height: 0.6rem;
          border-radius: 50%;
        }

        .detail {
          flex: 1;
          padding-left: 0.28rem;

          .name {
            font-size: 0.28rem;
            color: #000000;
          }
        }

        .status {
          font-size: 0.24rem;
          text-align: right;
          .gray {
            color: #333333;
          }
          .green {
            color: #6bce98;
          }

          .orange {
            color: #ff9900;
          }
          .purple{
            color: #b24ff1;
          }

          .blue {
            color: #0083ff;
          }
          .blue1{
            color: #3458f5;
          }

          .red {
            color: #ff3333;
          }
          .black{
            color:#333333;
          }
        }
      }
      .deliver{
        margin-left: 1.1rem;
        margin-right: 0.22rem;
        color: #999999;
        font-size: 0.2rem;
        display: flex;
        justify-content: space-between;
        .copy-btn {
          color: #0083ff;
          margin-top:0.24rem;
          font-size: 0.24rem;
        }
      }
      .order-info {
        margin-left: 0.22rem;
        margin-right: 0.22rem;
        margin-top: 0.2rem;
        border-top: 0.02rem dashed #eee;
        padding-top: 0.2rem;
        font-size: 0.2rem;
        color: #999999;
        max-height: 3rem;
        overflow-y: scroll;
        .order-info-item{
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }

    .no-data {
      height: 100%;
      font-size: 0.24rem;
      color: #8c8c8c;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/102250/3/42352/11653/65016dfeF263c3d2b/250af9864bd1312f.png) no-repeat;
      background-size: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
