<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/87857/33/45147/10864/653741e8Fc1afb011/15cc8f28f23bfda2.png" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="content">
      <div v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/222600/40/35224/219814/65016dfeF9234602d/d99de4f864849a24.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 0.86rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.6rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }

  .content {
    height: 8.45rem;
    width: 7rem;
    margin: 0 auto;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/102250/3/42352/11653/65016dfeF263c3d2b/250af9864bd1312f.png) no-repeat;
    background-size: 100%;
    padding: 0.3rem 0.3rem 0 0.3rem;
    font-size: 0.24rem;
    color: #262626;
    white-space: pre-wrap;
    div {
      height: 100%;
      overflow-y: scroll;
    }
  }
}
</style>
