<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>填写信息 领取权益</div>
      <div class="rightLineDiv"></div>
      <img alt="" data-v-705393a4="" src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png" class="close" @click="close" />
    </div>
    <div class="content">
      <div class="form">
        <VanField v-model="phone" required label="电话：" maxlength="11" type="number"></VanField>
      </div>
      <p class="tip-title">重要提醒:</p>
      <div class="tip" v-html="planDesc"></div>
      <div class="submit" @click="checkForm">提交</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="confirmPopup">
    <div class="affiem-inner" @click.stop>
      <div class="affiem-text">{{ phone }}</div>
      <div class="affiem-hint">请再次确定号码填写无误</div>
      <div class="affiem-word">
        <div class="affiem-balck">重要提示：</div>
        <p>① 充值成功后无法退换；</p>
        <p>② 切勿写错手机号，如充错，责任自行承担；</p>
        <p>③ 点击【确认提交】后，权益领取手机号会无法修改，请确认无误后再点击确认。</p>
      </div>
      <div class="flex">
        <div class="affirm-cancel-btn m-r-37" @click="confirmPopup = false">返回修改</div>
        <div class="affiem-btn" @click="submit">确认提交</div>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, showLoadingToast, closeToast } from 'vant';
import { ref } from 'vue';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  userPrizeId: {
    type: String,
    required: true,
  },
  planDesc: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const phone = ref('');

// 二次确认弹窗
const confirmPopup = ref(false);

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/30009/fuLuReceive', {
      userPrizeId: props.userPrizeId,
      phone: phone.value,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!phone.value) {
    showToast('请输入电话');
  } else if (!checkPhone.test(phone.value)) {
    showToast('请输入正确的电话');
  } else {
    // submit();
    confirmPopup.value = true;
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .content {
    border: 0.3rem solid transparent;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .form {
      .van-cell {
        border-radius: 0.08rem;
        margin-bottom: 0.1rem;

        &::after {
          display: none;
        }
      }
    }
    .tip-title {
      margin-top: 0.25rem;
      font-size: 0.25rem;
      color: #262626;
    }
    .tip {
      height: 2rem;
      overflow-y: scroll;
      font-size: 0.2rem;
      color: #b3b3b3;
    }

    .submit {
      margin-top: 0.25rem;
      margin-bottom: 1rem;
      text-align: center;
      font-size: 0.24rem;
      line-height: 0.6rem;
      color: #fff;
      background-color: #e2231a;
      height: 0.6rem;
    }
  }
}

// 二次确认弹窗
.affirm-box {
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiem-inner {
  background-color: #fff;
  border-radius: 0.2rem;
  width: 6rem;
  min-height: 2rem;
  padding: 0.4rem 0.2rem;
}
.affiem-text {
  margin-top: 0.09rem;
  font-size: 0.3rem;
  text-align: center;
  line-height: 0.54rem;
  color: #262626;
}
.affiem-hint {
  color: #f2270c;
  font-weight: 400;
  font-size: 0.26rem;
  text-align: center;
  line-height: 0.54rem;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.22rem;
}
.affirm-cancel-btn {
  width: 2rem;
  border-radius: 0.3rem;
  height: 0.6rem;
  line-height: 0.6rem;
  background-color: #fff;
  border: 1px solid #f2270c;
  color: #f2270c;
  text-align: center;
  font-size: 0.24rem;
  box-sizing: border-box;
}
.affiem-btn {
  width: 2rem;
  border-radius: 0.3rem;
  height: 0.6rem;
  line-height: 0.6rem;
  color: #fff;
  text-align: center;
  font-size: 0.24rem;
  box-sizing: border-box;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/125769/5/21394/20372/6268eedeEa00c993d/dfd61890bb7e7fa9.png) no-repeat 100%;
  background-size: cover;
}
.m-r-37 {
  margin-right: 0.37rem;
}
.affiem-word {
  color: #8c8c8c;
  font-size: 0.2rem;
  margin-top: 0.4rem;
  padding: 0 0.2rem;
  line-height: 0.4rem;
}
.affiem-balck {
  color: #262626;
  line-height: 0.43rem;
}
</style>
