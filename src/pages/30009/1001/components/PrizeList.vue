<template>
  <div class="prizeListBox">
    <div class="content"  :style="furnishStyles.showPrizeBg.value" >
      <div class="swiper-container1 swiper-container-prize" ref="swiperRef" >
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="prize in prizeList"  :key="prize.id">
            <div class="item">
              <img :src="prize.prizeImg" alt="" />
              <div>
                <p class="prizeName">{{ prize.prizeName }}</p>
                <p class="tip">奖品数量有限，先到先得哦~</p>
                <p class="prizeSurplus" v-if="furnishStyles.isStockShow.value !== 0">奖品剩余：{{prize.surplusNum}}份</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, nextTick, onMounted, onUpdated, ref, watch } from 'vue';
import furnishStyles from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

Swiper.use([Autoplay]);
const props = defineProps(['prizeList']);
const prizeSwiper = ref(null);
const prizeList = computed(() => props.prizeList.filter((e) => e.prizeName !== '谢谢参与').map((e) => ({
  ...e,
  surplusNum: e.surplusNum || e.sendTotalCount || 0,
})));
watch(prizeList, (newValue, oldValue) => {
  // console.log(newValue, 'newValue========');
  // console.log(`count changed from ${oldValue} to ${newValue}`);
  // if (newValue.length > 0) {
  nextTick(() => {
    if (prizeSwiper.value) {
      prizeSwiper.value.destroy();
    }
    prizeSwiper.value = new Swiper('.swiper-container-prize', {
      autoplay: {
        delay: 2000,
        disableOnInteraction: false,
      },
      loop: true,
      slidesPerView: 1,
      loopedSlides: 10,
      allowTouchMove: true,
    });
  });
  // }

}, { immediate: true, deep: true });
</script>

<style scoped lang="scss">
.prizeListBox {
  width: 6.9rem;
  margin: 0.5rem auto 0;
  .content {
    background-size: 100%;
    background-repeat: no-repeat;
    padding:0.8rem 0 0;
    height: 3.4rem;
    overflow: hidden;
    .item {
      width: 6.5rem;
      height: 2.4rem;
      display: flex;
      background: #fff;
      border-radius: 0.2rem;
      padding: 0.3rem;
      margin: 0 auto;
      img {
        width: 1.8rem;
        height: 1.8rem;
        border: 2px solid red;
        border-radius: 0.1rem;
        margin-right: 0.2rem;
      }
      div {
        flex: 1;
        .prizeName {
          font-size: 0.3rem;
          color: #333;
          margin: 0.2rem 0;
        }
        .tip {
          font-size: 0.24rem;
          color: #999;
          margin: 0.2rem 0;
        }
        .prizeSurplus {
          font-size: 0.2rem;
          color: #999;
          text-align: right;
          margin-top: 0.5rem;
        }
      }
    }
  }
}
</style>
