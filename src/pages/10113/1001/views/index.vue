<template>
  <div class="bg" :style="furnishStyles.actBgColor.value">
    <img alt="暂无图片" :src="furnishStyles.kvImg.value.src" class="kv-img" />
    <div class="act-title" v-if="furnish.actTitle!==''">{{furnish.actTitle}}</div>
    <div class="shop-name-text" :style="furnishStyles.shopNameColor.value">
      <span v-if="furnish.disableShopName === 1">{{ shopName }}</span>
    </div>
    <div class="header-btn-box">
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="showRule = true">活动规则</div>
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="showMyPrize = true">我的奖品</div>
      <div class="header-btn" :style="furnishStyles.operateBtn.value" @click="showMyOrder = true">我的订单</div>
    </div>
    <div class="time-icon-box">
      <van-count-down :time="activityInfo.orderEndTime - new Date().getTime()" format="DD:HH:mm:ss" class="time-box">
        <template #default="timeData">
          <span class="time-text">{{ timeData.days }}</span
          ><span class="letter-space">天</span> <span class="time-text">{{ timeData.hours }}</span
          ><span class="letter-space">时</span> <span class="time-text">{{ timeData.minutes }}</span
          ><span class="letter-space">分</span> <span class="time-text">{{ timeData.seconds }}</span
          ><span class="letter-space">秒</span>
        </template>
      </van-count-down>
    </div>
    <div class="new-tips-box" v-if="activityInfo.tasks.length > 1">可以左右滑动查看不同阶梯门槛及奖品~</div>
    <van-swipe :loop="true" :height="420" :show-indicators="false" :touchable="true">
      <div v-for="(item, index) in activityInfo.tasks" :key="index">
        <van-swipe-item>
          <div class="goods-task-list-box" @dragstart.prevent>
            <div class="goods-task-list-title" v-if="item.rule || (activityInfo.configType === 0 && item.rule === 0)">
              满<span class="goods-task-list-limit">{{ item.rule }}</span
              >罐即送
            </div>
            <div class="goods-task-list-title" :style="{ width: '4.6rem' }" v-else>消费有礼 完成任务即可领</div>
            <div class="goods-task-goods">
              <div class="goods-task-goods-img-box">
                <img class="goods-task-goods-img" :src="item.prizeImg" alt="暂无图片" />
              </div>
              <div class="goods-task-goods-info">
                <div class="goods-task-goods-name">{{ item.prizeName }}</div>
                <div class="goods-task-goods-remain">奖品剩余：{{ item.residuePrize }}份</div>
                <div class="goods-task-goods-remain">兑换奖品所需罐数：{{ item.rule }}罐</div>
              </div>
            </div>
            <div class="task-progress-box">
              <div class="task-progress-num" :style="{ width: calculateProgress(activityInfo.canExchange, item.rule) + '%' }"></div>
<!--              <div class="task-progress-num" :style="{ width: `${Number((+activityInfo.canExchange / +item.rule) * 100).toFixed(1)}%` }"></div>-->
            </div>
            <div class="receive-btn" v-threshold-if :style="{ backgroundImage: `url(${item.isCan || +item.residuePrize ? btnBg[item.isCan] : btnBg[2]})` }" v-threshold-click="() => clickGetPrizeFn(item)" />
            <div class="receive-btn" v-threshold-else :style="{ backgroundImage: `url(//img10.360buyimg.com/imgzone/jfs/t1/210529/30/30134/17380/64080220F6efb69c4/b26abf4cc1d06a41.png)` }" v-threshold-click="() => clickGetPrizeFn(item)" />
          </div>
        </van-swipe-item>
      </div>
    </van-swipe>
    <div class="canExchange">您已兑换：{{activityInfo.hasExchange}}罐</div>
    <div class="canExchange">当前剩余罐数：{{activityInfo.canExchange}}罐</div>
    <div class="goods-list-box" v-if="skuList.length !== 0">
      <div class="goods-list-title"></div>
      <div class="content-box" v-if="skuList.length !== 0">
        <div v-for="(item, index) in skuList" :key="item.skuId + index" class="goods-box">
          <img :src="item.skuMainPicture" alt="暂无图片" class="goods-pic" />
          <div class="goods-info-box">
            <div class="goods-name">{{ item.skuName }}</div>
            <div class="goods-info">
              <div class="goods-price"><span class="price-unit">¥</span>{{ item.jdPrice }}</div>
              <div class="bug-now-btn" @click="gotoSkuPage(item.skuId)" />
            </div>
          </div>
        </div>
        <div class="more-btn-all">
          <div class="more-btn" v-if="skuList.length && skuList.length !== total" @click="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>
    <div class="award-list-box" :style="{ top: skuList.length !== 0 ? '1.5rem' : '0.5rem' }">
      <div class="award-content" v-if="activityInfo.activity10113SuccessResponse.length !== 0">
        <div class="award-box" v-for="(item, index) in activityInfo.activity10113SuccessResponse" :key="index">
          <div class="award-name-box">
            <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="暂无图片" class="user-img" />
            <span>{{ item.nickName.substr(0, 1) + '****' + item.nickName.substr(-1) }}</span>
          </div>
          <div>{{ item.prizeName }}</div>
        </div>
      </div>
      <div class="award-content-null" v-else>暂无相关获奖信息哦~</div>
    </div>
    <div class="go-to-shop" v-if="skuList.length === 0">
      <div class="get-btn" @click="gotoShopPage(baseInfo.shopId)" />
    </div>
    <!-- 获奖规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="activityRule" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
    </VanPopup>
    <!-- 我的订单弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyOrder" position="bottom">
      <MyOrder v-if="showMyOrder" @close="showMyOrder = false"></MyOrder>
    </VanPopup>
    <!-- 领取奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showReceivePrize">
      <ReceivePrize :isDelay="activityInfo.isDelay" :delayDays="activityInfo.delayDays" :shopId="baseInfo.shopId" :receivePrizeInfo="receivePrizeInfo" :currentPrizeId="currentPrizeId" :currentReceivePrizeInfo="currentReceivePrizeInfo" @close="showReceivePrize = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></ReceivePrize>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress v-if="showSaveAddress" :activityPrizeId="currentPrizeId" :addressId="addressId" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="copyCardPopup" :closeOnClickOverlay="false">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
    </VanPopup>
    <!-- 领取京元宝权益 -->
    <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom" :closeOnClickOverlay="false">
      <SavePhone v-if="savePhonePopup" :currentPrizeId="currentPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, inject, reactive } from 'vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import RulePopup from '../components/RulePopup.vue';
import _ from 'lodash';
import MyOrder from '../components/MyOrder.vue';
import MyPrize from '../components/MyPrize.vue';
import ReceivePrize from '../components/receivePrize.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import SavePhone from '../components/SavePhone.vue';

export interface CardType {
  cardDesc: string;
  cardNumber: string;
  cardPassword: string;
  id: number;
  prizeName: string;
  showImg: string;
}
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;

const activityInfo = ref({
  activity10113SuccessResponse: [
    {
      drawTime: '',
      nickName: '',
      prizeName: '',
    },
  ],
  orderEndTime: '',
  configType: 0,
  orderSkuisExposure: 0,
  tasks: [],
  totalAmount: '',
  totalOrder: '',
  totalSku: '',
  isDelay: false,
}); // 活动相关数据
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const cursorNum = ref(0);
const shopName = ref(baseInfo.shopName);
const receivePrizeInfo = ref({
  taskId: '',
  isCan: '',
  prizeImg: '',
  prizeName: '',
  residuePrize: '',
  prizeType: '',
  rule: '',
  sort: 0,
});
const showRule = ref(false);
const showMyPrize = ref(false);
const showMyOrder = ref(false);
const showReceivePrize = ref(false);
const showSaveAddress = ref(false);
const activityRule = ref('');
const activityTotal = ['totalAmount', 'totalSku', 'totalOrder'];
const currentReceivePrizeInfo = ref();
const btnBg = [
  '//img10.360buyimg.com/imgzone/jfs/t1/102493/3/38236/35157/6507c818F055b6c9c/29175e79d30fce9e.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/176133/10/41204/18390/6507cdc7Fafdc3ea0/28ca374ef530a056.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/188292/20/43189/7959/660297ceF261c2b69/2cf70404b069528e.png',
  '//img10.360buyimg.com/imgzone/jfs/t1/188292/20/43189/7959/660297ceF261c2b69/2cf70404b069528e.png',
];

const currentPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (v: any) => {
  addressId.value = v.addressId;
  currentPrizeId.value = v.activityPrizeId;
  showReceivePrize.value = false;
  showSaveAddress.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (v: any) => {
  currentPrizeId.value = v.userPrizeId;
  planDesc.value = v.planDesc || '';
  showReceivePrize.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};
// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showReceivePrize.value = false;
  copyCardPopup.value = true;
};
// 获取活动信息
const getActivityInfo = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10113/activity');
    closeToast();
    activityInfo.value = data;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

// 获取指定商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/10113/getExposureSkuPage', {
      pageNum: pageNum.value,
      pageSize: 10,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    closeToast();
  } catch (error) {
    closeToast();
  }
};

const loadMore = async () => {
  pageNum.value += 1;
  await getSkuList();
};
// 获取活动规则
const getActivityRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    activityRule.value = data;
  } catch (error) {
    console.error(error);
  }
};
// 领取奖品
const clickGetPrize = async (item:any) => {
  console.log('item', item);
  receivePrizeInfo.value = item;
  if (+receivePrizeInfo.value.isCan === 0 && +receivePrizeInfo.value.residuePrize) {
    // 可以兑换
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const { code, data } = await httpRequest.post('/10113/sendPrize', { taskId: receivePrizeInfo.value.taskId });
      if (code === 200) {
        showReceivePrize.value = true;
        currentReceivePrizeInfo.value = data;
        console.log(data, '兑换结果');
      }
      closeToast();
      await getActivityInfo();
    } catch (error) {
      showToast(error.message);
      console.error(error);
    }
  } else if (+receivePrizeInfo.value.isCan === 1) {
    showToast('您已领取过奖品');
  } else if (+receivePrizeInfo.value.isCan === 2) {
    showToast('您不满足领取条件');
  } else if (+receivePrizeInfo.value.isCan === 3) {
    showToast('奖品已领光，请下次再试');
  }
};
const clickGetPrizeFn = _.debounce(clickGetPrize, 500, {
  leading: true, // 延长开始后调用
  trailing: false, // 延长结束前调用
});

// 移除组件时，取消防抖
onUnmounted(() => {
  clickGetPrizeFn.cancel();
});
const calculateProgress = (canExchange: number, rule: number) => {
  if (canExchange >= rule) {
    return 100;
  }
  return Number((Number(canExchange / rule) * 100).toFixed(1));
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getActivityInfo();
    await getActivityRule();
    await getSkuList();
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.bg {
  position: relative;
  padding-bottom: 2rem;
  min-height: 100vh;
  max-width: 7.5rem;
  background-size: 100%;
  background-repeat: no-repeat;
  .kv-img {
    position: relative;
    width: 7.5rem;
  }
  .act-title{
    text-align: center;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    top: 1rem;
    font-size: .6rem;
    color: #f8d174;
    font-weight: 600;
  }
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .header-btn-box {
    position: absolute;
    top: 2.4rem;
    right: 0;
    .header-btn {
      margin-bottom: 0.2rem;
      padding-left: 0.22rem;
      width: 1.26rem;
      height: 0.54rem;
      font-size: 0.24rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 40px 0 0 40px;
    }
  }
  .time-icon-box {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    margin-top: 0.5rem;
    width: 7.1rem;
    height: 1.1rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/96590/9/44441/14124/65e98c01F2e0e5ca9/fe09409729f098c0.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    align-items: center;
    justify-content: space-between;
    .time-box {
      position: absolute;
      left: 3.5rem;
      font-family: PingFang-SC-Bold;
      font-size: 0.24rem;
      color: #ffffff;
      .time-text {
        color: #ffffff;
      }
      .letter-space {
        margin: 0 0.1rem;
      }
    }
  }
  .new-tips-box {
    color: ghostwhite;
    text-align: center;
    margin: 5px 0px -19px;
  }
  .task-box {
    overflow: hidden;
    height: 7.23rem;
    width: 7.13rem;
  }
  .goods-task-list-box {
    position: relative;
    left: 50%;
    top: 0.5rem;
    transform: translateX(-50%);
    padding: 1.95rem 0.8rem 0.6rem;
    height: 7.23rem;
    width: 7.13rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/241904/24/4919/15445/65e98f75F84943576/19586abc793c518c.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .goods-task-list-title {
      position: absolute;
      transform: translateX(-50%);
      top: 0.17rem;
      left: 50%;
      font-family: PangMenZhengDao;
      font-size: 0.372rem;
      font-style: italic;
      letter-spacing: 0.02rem;
      color: #ffffff;
      .goods-task-list-limit {
        color: #ffff00;
      }
    }
    .goods-task-goods {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 2.23rem;
      .goods-task-goods-img-box {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 2.15rem;
        height: 2.23rem;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/120838/32/44047/6264/65ea7b35F00dea9cb/ecc159b3645bae61.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 0.1rem;
        .goods-task-goods-img {
          width: 1.88rem;
          height: 1.6rem;
        }
      }
      .goods-task-goods-info {
        .goods-task-goods-name {
          margin-bottom: 0.2rem;
          width: 3rem;
          font-family: SourceHanSansCN-Medium;
          font-size: 0.332rem;
          font-style: italic;
          color: #302c2c;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .goods-task-goods-remain {
          white-space: nowrap;
          font-family: SourceHanSansCN-Medium;
          font-size: 0.288rem;
          font-style: italic;
          line-height: 0.13rem;
          color: #ff3030;
          height: .4rem;
        }
      }
    }
    .task-progress-box {
      margin-top: 0.15rem;
      padding: 0 0.72rem 0 0.1rem;
      display: flex;
      align-items: center;
      width: 5.21rem;
      height: 0.74rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/98454/9/47643/8826/65ea7a14F367fb5d6/ce35ed278b40a2ad.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .task-progress-num {
        //width: 0;
        height: 0.36rem;
        background-image: linear-gradient(0deg, #74c321 0%, #bbeb89 100%);
        box-shadow: 0 0.02rem 0.02rem 0 rgba(223, 111, 48, 0.2), inset 0.025rem 0.043rem 0 0 rgba(207, 244, 167, 0.5), inset -0.035rem -0.035rem 0.05rem 0rem rgba(108, 185, 29, 0.7);
        border-radius: 0.201rem;
      }
    }
    .task-progress-text {
      margin-top: 0.16rem;
      font-family: SourceHanSansCN-Medium;
      font-size: 0.288rem;
      font-style: italic;
      line-height: 0.13rem;
      color: #ff3030;
    }
    .receive-btn {
      margin-top: 0.28rem;
      margin-left: 0.6rem;
      width: 4.12rem;
      height: 1.04rem;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
  .canExchange{
    text-align:center;
    color: #f8d174;
  }
  .goods-list-box {
    position: relative;
    left: 50%;
    top: 1rem;
    transform: translateX(-50%);
    padding: 0.6rem 0 0.4rem 0.1rem;
    min-height: 5rem;
    width: 7.12rem;
    background-color: #fed560;
    border-radius: 0.465rem;
    .goods-list-title {
      position: absolute;
      top: -0.5rem;
      left: 1rem;
      width: 5.2rem;
      height: 0.91rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/227009/1/15135/27659/65eec662F02b246b6/1f9bd2a2887fdbf5.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .content-box {
      margin-left: 0.15rem;
      display: flex;
      flex-wrap: wrap;
      width: 7rem;
      max-height: 7.9rem;
      overflow: auto;
    }
    .more-btn-all {
      width: 6.6rem;
      display: flex;
      justify-content: center;
      .more-btn {
        width: 1.8rem;
        height: 0.5rem;
        font-size: 0.2rem;
        color: #fff;
        background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
        background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
        border-radius: 0.25rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .goods-box {
      margin: 0 0.22rem 0.3rem 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      width: 3.21rem;
      height: 3.83rem;
      background-image: linear-gradient(0deg, #fff7e2 0%, #ffffff 100%);
      box-shadow: 0.109rem 0.214rem 0.376rem 0.024rem rgba(221, 141, 27, 0.19);
      border-radius: 0.391rem;
      .goods-pic {
        margin-top: 0.1rem;
        width: 2.6rem;
        height: 2.2rem;
        border-radius: 0.1rem;
      }
    }
    .goods-info-box {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      height: 1.16rem;
      width: 3.13rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/106280/6/40786/3055/65eebc9aFea075eb3/0f8b9b9ccd9221f4.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .goods-name {
        margin-bottom: 0.1rem;
        width: 2.65rem;
        font-family: SourceHanSansCN-Regular;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        font-size: 0.26rem;
        font-style: italic;
        color: #ffffff;
      }
      .goods-info {
        width: 2.65rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .goods-price {
          font-family: SourceHanSansCN-Bold;
          font-size: 0.3rem;
          font-style: italic;
          color: #ffffff;
          .price-unit {
            font-size: 0.2rem;
          }
        }
        .bug-now-btn {
          width: 1.43rem;
          height: 0.46rem;
          background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/179633/40/43510/9063/65ea7c7bF74a1bf1c/fd5a580f24a912c5.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }
  .award-list-box {
    padding: 1.5rem 0.5rem 0.5rem;
    margin-bottom: 0.5rem;
    position: relative;
    left: 50%;
    top: 1.5rem;
    transform: translateX(-50%);
    width: 7.12rem;
    height: 6.61rem;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/229860/4/13963/14598/65ea78a3F966fbb6d/9e4f71f9f21b889a.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-align: center;
    overflow: hidden;
    .award-content {
      height: 4.6rem;
      overflow: hidden;
      overflow-y: scroll;
    }
    .award-content-null {
      height: 4.5rem;
      overflow-y: scroll;
      text-align: center;
      line-height: 3.9rem;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
    .award-box {
      margin-bottom: 0.1rem;
      display: flex;
      justify-content: space-between;
      height: 0.78rem;
      line-height: 0.78rem;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t20353/283/457089967/998/1cdf509/5b0e3f7cNa6ff600e.png) left bottom repeat-x;
      color: rgb(0, 0, 0);
      font-size: 0.28rem;
      .award-name-box {
        display: flex;
        .user-img {
          margin-right: 0.1rem;
          width: 0.72rem;
          height: 0.72rem;
          border-radius: 50%;
        }
      }
    }
  }
  .go-to-shop {
    position: fixed;
    height: 0.88rem;
    line-height: 1.5rem;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .get-btn {
      width: 7.5rem;
      height: 0.88rem;
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/192572/12/27705/4069/6303376fE584f4c53/c1196beb18066383.png');
      background-size: 100%;
      background-repeat: no-repeat;
      font-family: MiSans-Regular;
      text-align: center;
      font-size: 0.53rem;
      letter-spacing: 0.011rem;
      color: #2d53f3;
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
