<template>
  <div class="rule-bk">
    <div class="close" @click="close"/>
    <div class="content" v-html="rule"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/173069/22/40237/13829/64f6fc44F446c39ef/f2eed301c147134c.png);
  background-size: 100%;
  background-repeat: no-repeat;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;
  height: 9.48rem;
  .close {
    position: absolute;
    top: 0.32rem;
    right: 0.33rem;
    width: 0.55rem;
    height: 0.55rem;
    cursor: pointer;
  }

  .content {
    position: absolute;
    top: 1.3rem;
    left: 0.5rem;
    height: 8rem;
    width: 6.5rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    cursor: pointer;
  }
}
</style>
