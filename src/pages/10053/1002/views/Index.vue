<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/205844/30/38166/87161/64bf63c3Fba5e68cd/6825cdfc139cafff.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div class="header-btn-all">
          <div class="header-btn"  :style="furnishStyles.headerBtn.value" v-click-track="'hdgz'" @click="showRulePopup()"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-click-track="'wdjp'" @click="showMyPrize = true"><div>我的奖品</div></div>
        </div>
      </div>
    </div>
    <div class="allFollowDivAll" :style="{'backgroundImage':'url(' + furnish.followBg + ')'}">
      <div class="logoDiv1" v-if="isGetPrize === 1" :style="{'backgroundImage': 'url(' + prizeImg + ')'}"></div>
      <div class="logoDiv" v-else-if="isGetPrize === 2"></div>
      <div class="logoDiv2" v-else-if="isGetPrize === 3"></div>
      <div class="messageDiv" v-if="isGetPrize === 2">
        <div class="followAllDiv" v-if="followType === 1">关注{{followNum}}件商品</div>
        <div class="followAllDiv" v-else-if="followType === 2">关注全部商品</div>
        <div class="textDiv">有机会领取神秘奖品</div>
      </div>
      <div class="messageDiv" v-else-if="isGetPrize === 1">
        <div class="followAllDiv">恭喜获得</div>
        <div class="prizeName">{{prizeName}}</div>
      </div>
      <div class="messageDiv" v-else-if="isGetPrize === 3">
        <div class="followAllDiv1">领取失败</div>
<!--        <div>抱歉，奖品被领完了</div>-->
      </div>
      <div class="followBtnAll" v-if="followType === 2">
        <div class="followBtn1" v-if="isGetPrize === 2 && followOnWay !== 1" :style="{'backgroundImage':'url(' + furnish.allFollowBtn + ')'}" v-threshold-click="followAllClick"></div>
        <div class="followBtnGray" :style="{'backgroundImage':'url(' + furnish.allHasFollowBtn + ')'}" v-else></div>
      </div>
    </div>
    <div class="sku">
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="gotoSkuPage(item.skuId)">
          <div>
            <img :src="item.skuMainPicture" alt="" />
            <div class="sku-bottom">
              <div class="sku-text">{{ item.skuName }}</div>
              <div class="sku-btns">
                <div class="price">￥{{ item.jdPrice1 }}</div>
                <div class="stepFollowBtnDiv"  v-if="followType === 1">
                  <div class="stepFollowGrayBtn" :style="{'backgroundImage':'url(' + furnish.stepHasFollowBtn + ')'}" v-if="item.isFollow">已关注</div>
                  <div class="stepFollowBtn" :style="{'backgroundImage':'url(' + furnish.stepFollowBtn + ')'}" v-else v-threshold-click="() => followGoodClick(item)">未关注</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="more-btn-all">
        <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
      </div>
    </div>
    <div class="bottom-shop-share select-hover">
      <div class="to-shop" :style="{'backgroundImage':'url(' + furnish.btnToShop + ')'}" v-click-track="'jdgg'" @click="gotoShopPage(baseInfo.shopId)" />
      <div class="share-friends" :style="{'backgroundImage':'url(' + furnish.btnShare + ')'}" @click="shareAct" v-click-track="'fxhy'" />
    </div>

    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div>
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @savePhone="showSavePhone" @showCardNum="showCardNum"></MyPrize>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @savePhone="showSavePhone" @showCardNum="showCardNum"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="activityPrizeId" :userPrizeId="userPrizeId" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
    </VanPopup>
    <!-- 领取京元宝权益 -->
    <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
      <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
    </VanPopup>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo, PrizeTypeName } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import { defaultSkuList } from 'pages/10053/1001/ts/default';

const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const shopName = ref(baseInfo.shopName);
const prizeData = ref({
  prizeImg: '',
  prizeName: '',
  prizeNum: 0,
  prizeType: 0,
  status: 0,
});
const followOnWay = ref(2); // 是否一键关注 1是 2否
const isGetPrize = ref(1); // 是否已经领奖  1 已领奖中奖 2未领奖 3 已领奖的未中奖
const followType = ref(1); // 关注方式 1 逐件关注 2一键关注
const followNum = ref(1); // 逐件关注几件商品数量可以获得奖品
const prizeName = ref('');
const prizeImg = ref('');
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const showMyPrize = ref(false);

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string, userPrizeIds: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  userPrizeId.value = userPrizeIds;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

// 主接口
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/10053/activityInfo');
    closeToast();
    isGetPrize.value = data.status;
    followOnWay.value = data.followOnWay;

    prizeImg.value = data.prizeImg;
    prizeName.value = data.prizeName;
    console.log(data, '主接口数据=======');
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};
const getTaskData = async () => {
  try {
    const { data } = await httpRequest.post('/10053/getTask');
    closeToast();
    followType.value = data.optWay;
    followNum.value = data.perOperateCount;
    console.log(data, '任务数据=======');
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};
const getFollowSkuTaskSkuDataPage = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10053/getFollowSkuTaskSkuPage', {
      pageNum: pageNum.value,
      pageSize: 10,
      type: 0,
      x: 'exposure',
    });
    closeToast();
    if (res.data.records && res.data.records.length > 0) {
      res.data.records.forEach((item) => {
        item.jdPrice1 = (item.jdPrice / 1000).toFixed(2);
      });
      skuList.value.push(...res.data.records);
    }
    pagesAll.value = res.data.pages;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
// 曝光商品加载更多
const loadMore = async () => {
  pageNum.value++;
  await getFollowSkuTaskSkuDataPage();
};
// 一键关注
const followAllClick = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10053/followSku', {
      skuId: skuList.value[0].skuId,
    });
    if (data.prizeType && data.prizeType > 0) {
      award.value = data;
      showAward.value = true;
    } else if (data.status === 1) {
      // 发奖失败
      award.value = {
        prizeType: '',
        prizeName: '',
      };
      showAward.value = true;
    }
    closeToast();
    await getActivityInfo();
  } catch (error) {
    showToast({
      message: error.message,
      duration: 2000,
      onClose: (() => {
        pageNum.value = 1;
        total.value = 0;
        pagesAll.value = 0;
        getActivityInfo();
      }),
    });
    console.error(error);
  }
};
const isClickFollowDisable = ref(true); // 关注店铺是否可以点击
// 逐件关注商品
const followGoodClick = async (item:any) => {
  // 防止连点
  if (!isClickFollowDisable.value) {
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10053/followSku', {
      skuId: item.skuId,
    });
    closeToast();
    if (data.prizeType && data.prizeType > 0) {
      award.value = data;
      showAward.value = true;
    } else if (data.status === 1) {
      // 发奖失败
      award.value = {
        prizeType: '',
        prizeName: '',
      };
      showAward.value = true;
    }

    item.isFollow = true;
    skuList.value = [];
    pageNum.value = 1;
    total.value = 0;
    pagesAll.value = 0;
    await getActivityInfo();
    await getFollowSkuTaskSkuDataPage();
    isClickFollowDisable.value = true;
  } catch (error) {
    showToast({
      message: error.message,
      duration: 2000,
      onClose: (() => {
        skuList.value = [];
        pageNum.value = 1;
        total.value = 0;
        pagesAll.value = 0;
        getFollowSkuTaskSkuDataPage();
        isClickFollowDisable.value = true;
      }),
    });
    console.error(error);
  }
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivityInfo(), getTaskData(), getFollowSkuTaskSkuDataPage()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color:#f2f2f2
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }
  .header-btn-all{
    margin-top:2.47rem;
    .header-btn {
      width: 1.28rem;
      height:0.49rem;
      margin-bottom: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      text-align: center;
      border-radius: 0.22rem;
      border: 0.01rem;
      border-style: solid;
      font-size: 0.2rem;
    }
  }
}
.allFollowDivAll{
  width: 6.92rem;
  height: 1.4rem;
  margin: 0.3rem auto 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  text-align: center;
  font-size: 0.2rem;
  align-items: center;
  display: flex;
  position: relative;
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/213315/39/35287/4561/64bf6428F69a509ae/8b92844c0decd2e0.png);
  .logoDiv{
    width: 1rem;
    height: 1rem;
    margin-left: 0.3rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193273/20/35104/12692/64bf687bFedd99e0f/75e5312371683b04.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .logoDiv1{
    width: 1rem;
    height: 1rem;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: 0.3rem;
  }
  .logoDiv2{
    width: 1.36rem;
    height: 0.9rem;
    margin-left: 0.3rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/168757/2/25782/15232/61a1feeeE7b368485/8159eb771a65ed74.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .messageDiv{
    width: 2.5rem;
    height: 0.7rem;
    margin-left: 0.2rem;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    flex-direction: column;
    .followAllDiv{
      color: #f2270c;
      font-size: 0.3rem;
      font-weight: bold;
    }
    .textDiv{
      color: #000000;
    }
    .prizeName{
      font-size: 0.2rem;
      color: #f2270c;
      font-weight: bold;
    }
    .followAllDiv1{
      font-size: 0.3rem;
      font-weight: bold;
    }
  }
  .followBtnAll{
    position: absolute;
    right: 0.3rem;
    .followBtn1{
      z-index: 12;
      width: 1.9rem;
      height: 0.69rem;
      background-size: 100%;
      background-repeat: no-repeat;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/204079/11/31908/4424/64bf644aF4cd38819/2fb2d95cf3a1c2c9.png);
    }
    .followBtnGray{
      width: 1.9rem;
      height: 0.69rem;
      background-size: 100%;
      background-repeat: no-repeat;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/91673/13/30798/2970/63073bb5E29f5639e/af3be7b128dc85b6.png);
    }
  }
}
.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0 0.2rem;
  .more-btn-all {
    width:6.9rem;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-bottom{
        width: 3.4rem;
        //height: 2.19rem;
        //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/213491/17/5876/55015/61a1c90aE3f24163e/9d701f534aba4d52.png);
        //background-size: 100% 100%;
        //background-repeat: no-repeat;
        padding-top:0.5rem;
        .sku-text {
          width: 3.4rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          color: #333333;
          font-size: 0.26rem;
          padding: 0 0.2rem;
          margin: 0 0 0.2rem 0;
          box-sizing: border-box;
        }
        .sku-btns {
          width: 3rem;
          height: 0.6rem;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
          //background-size: 100%;
          margin: 0 auto 0.2rem;
          position: relative;
          .price {
            width: 2.05rem;
            height: 0.6rem;
            //line-height: 0.6rem;
            font-size: 0.3rem;
            color: #333333;
            text-align: left;
            box-sizing: border-box;
          }
          .stepFollowBtnDiv{
            position: absolute;
            bottom: 0.14rem;
            right: 0.1rem;
            font-size:0;
            .stepFollowBtn{
              width: 1.5rem;
              height: 0.5rem;
              background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/207847/28/38765/3903/64bf6c8bFa6022496/9819401f53d31d46.png);
              background-size: 100%;
              background-repeat: no-repeat;
            }
            .stepFollowGrayBtn{
              width: 1.5rem;
              height: 0.5rem;
              background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/134625/1/35452/2566/64bf6c8bF8a587f8e/97f6c9c854e834af.png);
              background-size: 100%;
              background-repeat: no-repeat;
            }
          }
        }
      }
    }
  }
}
.bottom-shop-share {
  display: flex;
  position: fixed;
  bottom: 0;
  justify-content: space-evenly;
  width: 100%;
  z-index: 100;
  background: linear-gradient(rgb(255, 198, 219), rgb(255, 156, 191));
  padding-top: 0.18rem;
  .to-shop {
    height: 1.02rem;
    width: 3.15rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .share-friends {
    height: 1.02rem;
    width: 3.15rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

</style>
