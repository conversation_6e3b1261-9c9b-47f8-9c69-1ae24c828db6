<template>
  <div class="box">
    <div class='dialog' :style="furnishStyles.myPrizeBg.value">
       <div class="main">
         <div class="prize_list" >
           <div class="prize_item prize_til">
             <div class="item">抽奖时间</div>
             <div class="item">奖品名称</div>
             <div class="item">状态</div>
             <div class="item">地址物流</div>
           </div>
           <div class="content" v-if="prizes?.length > 0">
             <div class="prize_item" v-for="item in prizes" :key="item.giftId" >
               <div class="item">{{dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
               <div class="item">{{ item.prizeName }}</div>
               <div class="item" >
                 <div v-if="item.status==1">已中奖</div>
                 <div class="tipMessage" v-else>已重复中奖<br/>参与其他活动</div>
               </div>
               <div class="item" >
                 <div v-if="item.status === 2">无</div>
                 <div v-else-if="item.status === 1 && !item.realName" @click="editAddress(item)">填写地址</div>
                 <div class="tip" @click="showLogisticsInfo(item)" v-else>点击查看</div>
               </div>
             </div>
           </div>
           <div class="no_data" v-else>暂无数据</div>
         </div>
       </div>
      <div class="close-btn" @click="close"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import furnishStyles from '../ts/furnishStyles';

import LogisticsDetails from './LogisticsDetails.vue';

const isPreview = inject('isPreview') as boolean;

const addressId = ref('');
const showSaveAddress = ref(false);

const emits = defineEmits(['close', 'toSaveAddress']);

const close = () => {
  emits('close');
};

const prizes = reactive([]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99003/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
const showLogisticsInfo = (item: any) => {
  emits('toLogisticsDetails', item);
};
const editAddress = (item: any) => {
  emits('toSaveAddress', item);
};
if (!isPreview) {
  getUserPrizes();
}

</script>

<style scoped lang="scss">
.box {
  .dialog {
    width: 6.85rem;
    background-repeat: no-repeat;
    height:9rem;
    background-size: contain;
    box-sizing: border-box;
    position: relative;
    .main {
      width: 6rem;
      height:100%;
      margin:0 auto;
      padding-top: 1.7rem;
    }
    .prize_list {
      font-size: 0.27rem;
      margin-top: .13rem;
      .prize_til {
        font-size: 0.3rem;
        font-weight: 600;
      }
      .prize_item {
        width: 100%;
        display: flex;
        line-height: 0.31rem;
        margin-bottom: 0.07rem;
        text-align: center;
        align-items: center;

        div.item {
          flex: 1;
          text-align: center;
          padding-bottom: 0.07rem;
          padding-top: 0.07rem;
          .tip {
            color: #7c0004;
            cursor: pointer;
            text-decoration: underline;
            text-underline-offset: .1rem;//下划线和文字间距
          }
          .tipMessage {
            font-size: 0.24rem;
          }
        }
      }
    }
    .content {
      height: 3.7rem;
      overflow: scroll;
      .prize_item {
        width: 100%;
        display: flex;
        line-height: 0.31rem;
        margin-bottom: 0.07rem;
        text-align: center;
        align-items: center;

        div.item {
          flex: 1;
          text-align: center;
          padding-bottom: 0.07rem;
          padding-top: 0.07rem;
          .tip {
            color: #7c0004;
            cursor: pointer;
            text-decoration: underline;
            text-underline-offset: .1rem;//下划线和文字间距
          }
          .tipMessage {
            font-size: 0.24rem;
          }
        }
      }
    }
    .no_data {
      height: 3.5rem;
      line-height:3.5rem;
      text-align: center;
      font-size: 0.32rem;
    }
    .close-btn {
      position: absolute;
      bottom: 0.4rem;
      left: 1.8rem;
      width: 3.2rem;
      height: 0.8rem;
    }
  }
}

</style>
