<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/99442/3/44964/116321/650016f2F7a76acd6/e669105a838ca892.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div class="header-btn-all">
          <div class="header-btn"  :style="furnishStyles.headerBtn.value" v-click-track="'hdgz'" @click="showRulePopup()"><div>活动规则</div></div>
        </div>
      </div>
    </div>
    <div v-if="!isAcEnd">
      <CountDown :endTime="endTime" :startTime="startTime" :isStart="isStart"></CountDown>
    </div>
    <div class="teamDivAll">
      <div class="unTeamDivAll" :style="{'backgroundImage':'url(' + furnish.teamBg + ')'}">
        <div class="teamMemberDivAll">
          <div class="teamMemberDiv" v-for="(item,index) in teamMemberList" :key="index">
            <div :class="[index === 3 ? 'teamMemberDiv3' : index === 4 ? 'teamMemberDiv4' : '']">
              <div class="avtarDiv">
                <img :src="item.avatar" alt="">
              </div>
              <div class="nickName" :style="{'color':furnish.teamColor}">{{item.nickName ? encryptStr(item.nickName) : '待加入'}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="btnDivAll">
        <div v-if="captainCreate === 1" :style="furnishStyles.teamBtnBg.value" class="becomeLeaderDiv" v-threshold-click="doTeamClick">成为队长</div>
        <div v-else-if="captainCreate === 0 && isTeamIng" :style="furnishStyles.teamBtnBg.value" class="becomeLeaderDiv" v-threshold-click="shareActClick">邀请好友加入战队</div>
        <div v-else :style="furnishStyles.teamBtnBg.value" class="becomeLeaderDivGray">成为队长</div>
      </div>
    </div>
    <div class="myTeamDivAll" v-if="resultResponses.length > 0">
      <div class="myTeamTitle" :style="{'backgroundImage':'url(' + furnish.myTeamBg + ')'}"></div>
      <div class="myTeamMemberDivAll" :style="{'backgroundImage':'url(' + furnish.teamBg + ')'}" v-for="(item,index) in resultResponses" :key="index">
        <div class="teamMemberDiv" v-for="(item1,index1) in item.responses" :key="index1">
          <div class="avtarDiv">
            <img :src="item1.avatar" alt="">
          </div>
          <div class="nickName" :style="{'color':furnish.teamColor}">{{item1.nickName ? encryptStr(item1.nickName) : '等待加入'}}</div>
          <div class="prizeDivAll">
            <div>
              <img v-if="prizeType === 4" src="//img10.360buyimg.com/imgzone/jfs/t1/8033/11/14049/865/5c4fb95dEa8b1e3fa/05b2218f104fb4fc.png" alt="">
              <img v-else src="https://img10.360buyimg.com/imgzone/jfs/t1/7534/8/13941/783/5c4fb95eEa71d323f/30dee458e8747468.png" alt="">
            </div>
            <div class="prizeNumDiv" :style="{'color':furnish.teamColor}">
              <span v-if="index1 === 0">{{captainNum}}+{{memberNum}}</span>
              <span v-else>{{memberNum}}</span>
            </div>
          </div>
        </div>
        <div class="successLogoDiv" :style="{'backgroundImage':'url(' + furnish.teamStatusBg + ')'}"></div>
      </div>
    </div>
    <div class="myCaptainDivAll" v-if="pathParams.shareId && pathParams.groupId">
      <div class="captainImg">
        <img :src="(captainInfo && captainInfo.avatar) ? captainInfo.avatar : '//img10.360buyimg.com/imgzone/jfs/t1/231229/10/23993/3226/66b1d00aFaba38b8b/9fc146818525fcc6.png'" alt="" />
      </div>
      <div class="myCaptainDiv" :style="furnishStyles.myCaptainBg.value">
        <div class="nickNameDiv">{{captainInfo ? encryptStr(captainInfo.nickName) : ''}}</div>
        <div class="textDiv">等待瓜分积分</div>
      </div>
    </div>
    <div class="sku" v-if="skuList.length > 0" :src="furnish.winnersBg">
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" v-click-track="'ljgm'"  @click="gotoSkuPage(item.skuId)">
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-text">{{ item.skuName }}</div>
          <div class="sku-btns">
            <div class="textDiv1">活动价：</div>
            <div class="price">￥{{ item.jdPrice }}</div>
          </div>
        </div>
      </div>
      <div class="more-btn-all">
        <div class="more-btn" v-if="pageNum < pagesAll" @click="loadMore">点我加载更多</div>
      </div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div>
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
<!--    :close-on-click-overlay="false"-->
    <VanPopup teleport="body" v-model:show="isShowShareJoinTeam" position="center" >
      <ShareJoinTeam :captainInfo="captainInfo" :groupId="pathParams.groupId" @shareCallBack="shareJoinCallBack" @close="isShowShareJoinTeam = false"></ShareJoinTeam>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import CountDown from '../components/CountDown.vue';
import dayjs from 'dayjs';
import { gotoSkuPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';
import { defaultTeamMemberList } from '../ts/default';
import ShareJoinTeam from '../components/ShareJoinTeam.vue';
import { Handler } from '@/utils/handle';

const teamMemberList = ref<[]>(defaultTeamMemberList); // 正在组队的信息
const endTime = ref(0);
const startTime = ref(0);
const isStart = ref(false);
const isAcEnd = ref(false); // 活动是否已经结束
const pathParams = inject('pathParams') as any;
console.log(pathParams, 'pathParamspathParams');
const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const userInfo = inject('userInfo') as any;
const isShowShareJoinTeam = ref(false); // 是否显示被邀请者弹窗
const shareUrlOld = ref('');
const captainNum = ref(0); // 队长奖励
const memberNum = ref(0); // 队员奖励
const shopName = ref(baseInfo.shopName);
const prizeType = ref(0);
const prizeData = ref({
  prizeImg: '',
  prizeName: '',
  prizeNum: 0,
  prizeType: 0,
  status: 0,
});

// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);

const resultResponses = ref([]); // 已经完成的组队信息
// 活动倒计时
const getTime = () => {
  startTime.value = dayjs(baseInfo.startTime).valueOf();
  endTime.value = dayjs(baseInfo.endTime).valueOf();
  const now = dayjs().valueOf();
  if (now > endTime.value) {
    // 活动已结束
    isAcEnd.value = true;
    isStart.value = true;
  } else if (now > startTime.value && now <= endTime.value) {
    // 活动进行中
    isAcEnd.value = false;
    isStart.value = true;
  } else if (now < startTime.value) {
    // 活动未开始
    isStart.value = false;
  }
};

const showRule = ref(false);
const handler = Handler.getInstance();
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const captainCreate = ref(1); // 是否可以创建新的队伍 0 不能创建 1 能创建
const groupId = ref(0);
const captainInfo = ref(null); // 邀请者信息
const isTeamIng = ref(true); // 是否有正在进行中的队伍 true 有 false  没有
// 主接口
const getPrizesData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10033/activity', {
      shareId: pathParams.shareId,
    });
    closeToast();
    captainCreate.value = data.captainCreate;
    captainInfo.value = data.captainInfo;
    captainNum.value = data.captainNum;
    memberNum.value = data.memberNum;
    prizeType.value = data.prizeType;
    if (data.responses && data.responses.length === 0) {
      // data.responses = data.responses.concat(userInfo);
      teamMemberList.value[0].nickName = userInfo.nickname;
      teamMemberList.value[0].avatar = userInfo.avatar;
      isTeamIng.value = false;
    } else {
      isTeamIng.value = true;
      groupId.value = data.responses[0].groupId;
      teamMemberList.value.forEach((item, index) => {
        data.responses[0].responses.forEach((item1, index1) => {
          if (index === index1) {
            teamMemberList.value[index] = data.responses[0].responses[index1];
          }
        });
      });
    }
    resultResponses.value = data.resultResponses;
    console.log(data, '主接口数据=======');
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};
// 做队长
const doTeamClick = async () => {
  console.log('做队长');
  // isCaptain.value = true;
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    closeToast();
    const res = await httpRequest.post('/10033/createTeam');
    if (pathParams.shareId && pathParams.groupId) {
      const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
      const urlObj = new URL(window.location.href);
      const searchParams = new URLSearchParams(urlObj.search);
      // 移除shareId参数
      searchParams.delete('shareId');
      searchParams.delete('groupId');
      // 构造新的URL，不包含shareId参数
      const newUrl = `${urlObj.origin + urlObj.pathname}?${searchParams.toString()}`;
      shareUrlOld.value = `${newUrl}&shareId=${shareConfig.shareId}`;
      console.log(shareUrlOld.value, '被邀请者分享活动链接2');
    }
    await getPrizesData();
  } catch (e) {
    showToast(e.message);
  }
};
const getExposureData = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10033/getExposureSku', {
      pageNum: pageNum.value,
      pageSize: 10,
      type: 0,
      x: 'exposure',
    });
    closeToast();
    if (res.data.records && res.data.records.length > 0) {
      skuList.value.push(...res.data.records);
    }
    pagesAll.value = res.data.pages;
  } catch (error) {
    closeToast();
    console.error(error);
  }
};
// 曝光商品加载更多
const loadMore = async () => {
  pageNum.value++;
  await getExposureData();
};
const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  console.log(`${shareUrlOld.value}&groupId=${groupId.value}`, '分享链接');
  callShare({
    shareUrl: `${shareUrlOld.value}&groupId=${groupId.value}`,
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
// 好友帮忙助力
const doTask = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/10033/doTask/doTask', {
      shareId: pathParams.shareId,
      groupId: pathParams.groupId,
    });
    showToast('加入TA的战队成功');
    const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
    const urlObj = new URL(window.location.href);
    const searchParams = new URLSearchParams(urlObj.search);
    // 移除shareId参数
    searchParams.delete('shareId');
    searchParams.delete('groupId');
    // 构造新的URL，不包含shareId参数
    const newUrl = `${urlObj.origin + urlObj.pathname}?${searchParams.toString()}`;
    shareUrlOld.value = `${newUrl}&shareId=${shareConfig.shareId}`;
    console.log(shareUrlOld.value, '被邀请者分享活动链接1');
  } catch (e) {
    showToast(e.message);
  }
};
const shareActClick = async () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  shareUrlOld.value = `${window.location.href}&shareId=${shareConfig.shareId}`;
  console.log(shareUrlOld.value, '被邀请者分享活动链接3');
  await shareAct();
};
// 被邀请者点击回调
const shareJoinCallBack = async (data) => {
  console.log(data, '被邀请者点击回调');
  const thresholdLength = await handler.trigger('onThresholdOpen');
  if (!thresholdLength) {
    if (data === 'doCaptain') {
      console.log('做队长');
      await doTeamClick();
    } else if (data === 'joinTeam') {
      await doTask();
    }
    isShowShareJoinTeam.value = false;
  }

};
const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    await Promise.all([getPrizesData(), getExposureData()]);
    const thresholdLength = await handler.trigger('onThresholdIf');
    if (!thresholdLength) {
      if (pathParams.shareId && pathParams.groupId) {
        isShowShareJoinTeam.value = true;
      }
    }
  } catch (error) {
    closeToast();
  }
};
const encryptStr = (pin: string): string => {
  const first = pin.slice(0, 1);
  const last = pin.slice(-1);
  return `${first}****${last}`;
};
init();
</script>

<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color:#f2f2f2
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }
  .header-btn-all{
    .header-btn {
      width: 1.28rem;
      height:0.49rem;
      margin-bottom: 0.1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      text-align: center;
      border-radius: 0.22rem;
      border: 0.01rem;
      border-style: solid;
      font-size: 0.2rem;
    }
  }
}
.teamDivAll{
  position: relative;
  .unTeamDivAll{
    position: relative;
    width: 7.14rem;
    height: 3.53rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/121824/25/39106/11390/64ffc864F93751acb/6d8de151c2df73a7.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0 auto 0.64rem;
    display: flex;
    flex-direction: column;
    padding:0.3rem 0.22rem 0 0.22rem;
    //justify-content: center;
    .textDiv{
      text-align: center;
      color:#f6cf8c;
      font-size:0.26rem;
    }
    .teamMemberDivAll{
      justify-content: space-around;
      flex-wrap: wrap;
      display: flex;

    }
    .teamMemberDiv3{
      margin-left:0.8rem;
    }
    .teamMemberDiv4{
      margin-right:0.8rem;
    }
    .teamMemberDiv{
      //width: 1.3rem;
      width:33%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;
      position: relative;
      margin-bottom:0.1rem;
      .avtarDiv{
        width: 0.8rem;
        height: 0.8rem;
        color: rgb(131, 131, 131);
        font-size: 0.18rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 700;
        img{
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }
      }
      .nickName{
        font-size: 0.22rem;
        font-weight: normal;
        color: rgb(255, 255, 255);
        margin-top: 0.12rem;
      }
    }
  }
  .btnDivAll{
    position: absolute;
    bottom: -0.2rem;
    left:50%;
    transform: translate(-50%);
    .becomeLeaderDiv{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/166379/12/38607/4559/64ffc9a8F5e00e419/85c8f2891e4777c1.png);
      margin: 0 auto;
      width: 4.54rem;
      height: 0.77rem;
      background-size: 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: "[FZZDHJW]";
      text-align: center;
      line-height:0.77rem;
      font-size: 0.29rem;
      color: #f4c97d;
    }
    .becomeLeaderDivGray{
      filter: grayscale(1);
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/42260/38/7436/12029/5d109844E16ea0fb1/1c4ed43f5e045cb6.png);
      margin: 0 auto;
      width: 5.38rem;
      height: 1.15rem;
      background-size: 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: "[FZZDHJW]";
      text-align: center;
      line-height:1.15rem;
      font-size: 0.29rem;
      color: #f4c97d;
    }
  }
}
.myTeamDivAll{
  .myTeamTitle{
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/41483/9/7499/65248/5d108f89E533ee1f0/a424d4a15f65aec0.png) 0% 0% / 100% no-repeat;
    width: 7.21rem;
    height: 1.37rem;
    margin-top: 0.3rem;
    margin-left: 50%;
    transform: translate(-50%);
  }
  .myTeamMemberDivAll{
    position: relative;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/121824/25/39106/11390/64ffc864F93751acb/6d8de151c2df73a7.png) 0% 0% / 100% no-repeat;
    box-sizing: border-box;
    width: 7.14rem;
    height: 3.53rem;
    display:flex;
    padding-top:0.5rem;
    justify-content: center;
    margin-top:0.2rem;
    margin-left: 50%;
    transform: translate(-50%);
    .teamMemberDiv{
      width: 1.3rem;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;
      position: relative;
      .avtarDiv{
        width: 0.8rem;
        height: 0.8rem;
        color: rgb(131, 131, 131);
        font-size: 0.18rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 700;
        img{
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
        }
      }
      .nickName{
        font-size: 0.22rem;
        font-weight: normal;
        color: rgb(255, 255, 255);
        margin-top:0.2rem;
        //margin-top: 0.08rem;
      }
    }
    .prizeDivAll{
      margin-top:0.1rem;
      display:flex;
      color:#fff;
      img{
        margin-right:0.04rem;
        width: 0.3rem;
      }
      .prizeNumDiv{
        font-size:0.24rem;
      }
    }
    .successLogoDiv{
      position: absolute;
      right: 0.15rem;
      bottom: 0.45rem;
      width: 1.07rem;
      height: 0.46rem;
      background-image:url('//img10.360buyimg.com/imgzone/jfs/t1/76295/34/2820/3726/5d11ca6dEbbb2ac9e/8f66e72533c44b48.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
}
.myCaptainDivAll{
  //margin-top: 1.5rem;
  position: relative;
  .captainImg{
    width:1.7rem;
    height:1.7rem;
    border-radius: 50%;
    position: relative;
    z-index: 10;
    margin-left: 50%;
    transform: translate(-50%);
    img{
      //margin-top: -0.7rem;
      width:100%;
      height:100%;
      border-radius: 50%;
    }
  }
  .myCaptainDiv{
    position: relative;
    width: 7.21rem;
    height: 3rem;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/69059/7/2771/32919/5d108f8aE65dac15f/583437223034341a.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-left: 50%;
    transform: translate(-50%);
    margin-top: -0.9rem;
    padding-top:1rem;
    text-align: center;
    color:#fff;
    font-size:0.3rem;

  }
}
.sku {
  width: 7.14rem;
  height: 11.42rem;
  margin: 0 auto;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/184441/19/37488/22487/6500076fFb5369c42/3e501ff2dbacd426.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 1.2rem 0.3rem 0px;
  .more-btn-all {
    width:6.9rem;
    margin-top:0.24rem;
    .more-btn {
      width: 1.8rem;
      height: 0.5rem;
      font-size: 0.2rem;
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
      border-radius: 0.25rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }
  }
  .sku-list {
    height: 9rem;
    overflow-y: scroll;
    display: flex;
    place-content: flex-start space-between;
    flex-wrap: wrap;
    .sku-item {
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/105227/29/28830/7353/64ff0bf8F2862c1c2/f870ac7d29452a2b.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 3.22rem;
      height: 5.2rem;
      border-radius: 0.2rem;
      overflow: hidden;
      img {
        display: block;
        width: 3.2rem;
        //height: 3.2rem;
      }
      .sku-text {
        width: 3.2rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 0.27rem;
        color: #262626;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.2rem 0;
        box-sizing: border-box;
      }
      .sku-btns {
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/193129/28/37715/3825/650012feF2d045e9b/157a786a87367d82.png);
        background-size: 100%;
        margin: 0 auto 0.2rem;
        padding-left: 0.2rem;
        .textDiv1{
          line-height:0.6rem;
          color:#fff;
          font-size: 0.2rem;
        }
        .price {
          //width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          //padding-left: 0.2rem;
          box-sizing: border-box;
        }
      }
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

</style>
