<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
      <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
    </div>
    <div class="header-btn-box">
      <div class="header-btn" :style="furnishStyles.operateBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
        {{ btn.name }}
      </div>
    </div>
    <div class="openBoxBtn" :style="furnishStyles.joinBtnBg.value" @click="openBoxFn"/>
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import { showToast } from 'vant';

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const shopName = ref('xxx旗舰店');
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
const setDataInfo = (data:any) => { // 数据赋值
  if (data.rules) {
    ruleTest.value = data.rules;
  }
};
const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '我的奖品',
    event: () => {
      showMyPrize.value = true;
    },
  },
];

const openBoxFn = () => {
  showToast('活动预览，仅供查看');
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    setDataInfo(data);
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
// 截图监听
registerHandler('screen', () => {
  createImg();
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  if (activityData) {
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
img[src=""]
,img:not([src])
{
  opacity:0;
}
.bg {
  position: relative;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .header-btn-box {
    position: absolute;
    top: 2.1rem;
    right: 0;
    .header-btn {
      width: 1.41rem;
      height: 0.4rem;
      margin-bottom: 0.1rem;
      font-size: 0.22rem;
      text-align: center;
      background-repeat: no-repeat;
      background-size: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
  .openBoxBtn{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 13rem;
    width: 2.54rem;
    height: 0.68rem;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
