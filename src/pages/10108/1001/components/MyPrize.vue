<template>
  <!-- 领取奖品弹窗 -->
  <div class="box">
    <div class="prize-view" v-if="prizes.length >0">
      <div class="prize-item" v-for="(item,index) in prizes" :key="index">
        <div class="prize-img-view">
          <img style="width: 100%" :src="item?.prizeImg" alt="">
        </div>
        <div class="prize-name one-line-omit">{{ item?.prizeName }}</div>
        <div class="handle-view">
          <img @click="toExchangePage()" v-if="item.prizeType===9 || item.prizeType===10" src="../assets/img/exchange-btn.png"
               style="width: 100%" alt="">
        </div>
      </div>
    </div>

    <div class="prize-view" v-else style="top: 6rem">暂无奖品数据</div>
    <!-- 关闭按钮 -->
    <div class="close-btn" @click="closeDialog()"></div>
  </div>
</template>

<script lang='ts' setup>

import { httpRequest } from '@/utils/service';

import { inject, reactive } from 'vue';
import { showLoadingToast, closeToast } from 'vant';

const isPreview = inject('isPreview') as boolean;
const emits = defineEmits(['close']);

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/10108/myPrice');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const closeDialog = () => {
  emits('close');
};
const toExchangePage = () => {
  window.location.href = 'https://asset-m.jd.com/';
};

</script>

<style lang='scss' scoped>
.box {
  width: 6.5rem;
  height: 10.5rem;
  position: relative;
  padding: 0 .5rem;
  background: {
    image: url("../assets/img/myPrize-dialog.png");
    repeat: no-repeat;
    size: contain;
  };

  .close-btn {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: .7rem;
    height: .7rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/110430/17/39957/1262/6459a770Fd709c84b/d608c15c4f784ed7.png");
      repeat: no-repeat;
      size: contain;
    };
  }

  .prize-view {
    width: 5.5rem;
    height: 3.5rem;
    padding: 0 .1rem;
    text-align: center;
    position: absolute;
    top: 5rem;
    overflow-y: auto;

    .prize-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 1rem;

      .prize-img-view {
        width: 1.5rem;
      }

      .prize-name {
        width: 1.9rem;
        font-size: .3rem;
        color: #4f4f4f;
      }

      .handle-view {
        width: 1.5rem;
      }
    }

  }
}
</style>
