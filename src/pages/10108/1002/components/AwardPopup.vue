<template>
  <!-- 领取奖品弹窗 -->
  <div class="box">
    <div class="prize-view">
      <img class="prize-img" :src="prizeInfo?.prizeImg" alt="">
      <div class="prize-name" v-if="prizeInfo.prizeType===8">请至京东APP-我的-我的钱包-礼品卡中查收</div>
      <div class="prize-name" v-else-if="prizeInfo.prizeType===6">请至京东APP-我的-我的钱包-红包中查收</div>
      <div v-else>
        <img class="toExchangePageBtnDiv" @click="toExchangePage()" src="../assets/img/exchange-btn.png" alt="">
      </div>
    </div>
    <!-- 关闭按钮 -->
    <div class="close-btn" @click="closeDialog()"></div>
  </div>
</template>

<script lang='ts' setup>
import { defineEmits, defineProps, PropType, ref } from 'vue';
import { closeDialog } from '../ts/dialog';
import { IPrizeData } from '../ts/type';

interface PrizeData {
  activityPrizeId: number;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  status: number;
}

// const props = defineProps({ prizeInfo: Object as PropType<PrizeData> });
const props = defineProps({
  prizeInfo: {
    type: Object as PropType<PrizeData>,
    required: true,
  },
});

const toExchangePage = () => {
  window.location.href = 'https://asset-m.jd.com/';
};
</script>

<style lang='scss' scoped>
.box {
  width: 6.5rem;
  height: 10.5rem;
  position: relative;
  background: {
    image: url("../assets/img/success-dialog.png");
    repeat: no-repeat;
    size: contain;
  };
.toExchangePageBtnDiv{
  width: 2rem;
  margin-left: calc(50% - 2rem / 2);
  margin-top: 0.2rem;
}
  .close-btn {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: .7rem;
    height: .7rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/110430/17/39957/1262/6459a770Fd709c84b/d608c15c4f784ed7.png");
      repeat: no-repeat;
      size: contain;
    };
  }

  .prize-view {
    width: 100%;
    height: 3.4rem;
    text-align: center;
    position: absolute;
    top: 5.4rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .prize-img {
      width: 2.5rem;
    }

    .prize-name {
      font-size: .28rem;
      color: #4f4f4f;
      margin-top: .4rem;
    }
  }
}
</style>
