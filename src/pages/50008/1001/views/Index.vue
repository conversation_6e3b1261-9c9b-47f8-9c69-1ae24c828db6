<template>
  <div class="bg">
    <div class="kvBgStyle" v-if="giftImageUrl">
      <img class="giftImageUrlDiv" :src="giftImageUrl" @click="getPrize()" alt="" />
      <!--     <div @click="getPrize()">-->
      <!--       &lt;!&ndash;兜底和非会员&ndash;&gt;-->
      <!--       <div v-if="position === 1 || position === 2" class="knowBtn"></div>-->
      <!--&lt;!&ndash;       &lt;!&ndash;99会员生日月  跳付费会员页面&ndash;&gt;&ndash;&gt;-->
      <!--&lt;!&ndash;       <div v-if="position === 3" class="getBtn"></div>&ndash;&gt;-->
      <!--&lt;!&ndash;       &lt;!&ndash;付费会员  跳付费99会员开卡&ndash;&gt;&ndash;&gt;-->
      <!--&lt;!&ndash;       <div v-if=" position === 4 || position === 5" class="joinBtn"></div>&ndash;&gt;-->
      <!--       &lt;!&ndash; 会员  跳付费会员页面&ndash;&gt;-->
      <!--       <div v-if="position === 6 || position === 7 || position === 8" class="knowBtn"></div>-->
      <!--     </div>-->

      <!-- <div v-if="isShowClose" class="closeDiv" @click="toast"></div> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { ref, inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { jumpGetCoupon, closeGiftPop } from '@/utils/shopGiftBag.js';

const decoData = inject('decoData') as DecoData;
const pathParams = inject('pathParams') as any;
console.log(pathParams, 'pathParams');
const isShowClose = ref(pathParams.isGift);
const giftImageUrl = ref(''); // 礼包图片
const linkUrl = ref(''); // 跳转链接
const userReceiveRecordId = ref(''); // 领取奖品的id

// 人群类型
const position = ref(2);
// 获取数据
const getAllData = async () => {
  try {
    const { data } = await httpRequest.post('/50008/query');
    // console.log(data, 'data===========');
    giftImageUrl.value = data.imgUrl;
    linkUrl.value = data.link;
    userReceiveRecordId.value = data.userReceiveRecordId;
  } catch (errMsg) {
    // console.log(errMsg, 'data===========errMsg');
    showToast(errMsg.message);
  }
};
const toast = () => {
  if (pathParams.isGift) {
    showToast('仅供预览');
  }
};
// 领取奖品
const getPrize = async () => {
  if (pathParams.isGift) {
    showToast('仅供预览');
    return;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/50008/receive', {
      userReceiveRecordId: userReceiveRecordId.value,
    });
    // showToast('收下礼包成功');
    closeToast();
    closeGiftPop(this, () => {
      console.log('关闭弹窗');
    });
    jumpGetCoupon(linkUrl.value, () => {
      console.log('跳转链接');
    });
  } catch (errMsg) {
    // console.log(errMsg, 'errMsg=======');
    showToast(errMsg.message);
  }
};
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getAllData()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>
<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  width: 100%;
  min-height: 100vh;
  position: absolute;
  top: 0;
  background: rgba(0, 0, 0, 0.7);
}
.kvBgStyle {
  background-size: 100%;
  background-repeat: no-repeat;
  position: absolute;
  width: 6.3rem;
  height: 9rem;
  left: calc(50% - 6.3rem / 2);
  top: calc(50% - 9rem / 2);
}
.giftImageUrlDiv {
  width: 6.5rem;
  height: 7.7rem;
}
/*立即参与*/
.joinBtn {
  width: 5.8rem;
  height: 0.8rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/237026/12/14475/6911/662a02e2F8535405f/564af7925bf525a7.png') no-repeat;
  background-size: 100%;
  position: absolute;
  top: 6.6rem;
  left: calc(50% - 5.7rem / 2);
}
/*立即领取*/
.getBtn {
  width: 5.8rem;
  height: 0.8rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/241282/20/8228/7303/662b209eF40371bda/fe5a8824a83c923b.png') no-repeat;
  background-size: 100%;
  position: absolute;
  top: 6.6rem;
  left: calc(50% - 5.7rem / 2);
}
/*立即了解*/
.knowBtn {
  width: 5.8rem;
  height: 0.8rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/216461/34/37230/6577/662a02e1F6c6b2f88/244cf23a50e9201c.png') no-repeat;
  background-size: 100%;
  position: absolute;
  top: 6.6rem;
  left: calc(50% - 5.7rem / 2);
}
.closeDiv {
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  top: -0.8rem;
  right: 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/240116/39/1181/7700/65a6402cF2d4309d7/f9f24e9fcb55e30c.png');
}
</style>
