import type { DirectiveBinding } from 'vue';
import { App } from 'vue';
import { getBeaconBehaviors, reportDefaultValue } from '@/utils/trackEvent/lzReport';
import constant from '@/utils/constant';

// 定义点击事件的扩展参数类型
export interface EventTrackParams {
  venderId?: string,
  activityId?: string,
  c?: any,
}

/**
 * 指令名称
 */
const clickDirectiveName = 'click-track';

/**
 * 指令名称
 */
const popDirectiveName = 'pop-track';

/**
 * 点击事件
 * @param event Event
 */
const onClickHandler = (event: Event) => {
  const target = event.target as Element;
  const trackValue = target.getAttribute('data-click-track-value') || '{}';
  const postData = JSON.parse(trackValue);
  getBeaconBehaviors(postData);
  console.debug('点击埋点', postData);
};

// /**
//  * 注册事件
//  * @param el
//  * @param binding
//  * @param options
//  */
// const attachClickEvent = (el: HTMLElement, binding: DirectiveBinding, options: EventTrackParams) => {
//   el.removeEventListener('click', onClickHandler);
//   el.addEventListener('click', onClickHandler);
//   const defaultEventTrackParams = {
//     e: 'click',
//   };
//   const value = { ...options, ...defaultEventTrackParams };
//   let { c } = value;
//   if (typeof binding.value === 'string') {
//     c = Object.assign(value.c, { code: binding.value });
//   } else if (typeof binding.value === 'object') {
//     c = Object.assign(value.c, binding.value);
//   }
//   Object.assign(value, { c });
//   el.setAttribute('data-click-track-value', JSON.stringify(value));
// };

/**
 * 注册事件
 * @param el
 * @param binding
 * @param options
 */
const attachClickEvent = (el: HTMLElement, binding: DirectiveBinding, options: EventTrackParams) => {
  const manageClickEvent = (action: 'add' | 'remove') => {
    el[`${action}EventListener`]('click', onClickHandler);
  };

  manageClickEvent('remove');
  manageClickEvent('add');

  const defaultEventTrackParams = { e: 'click' };
  const value = { ...options, ...defaultEventTrackParams };

  const c = typeof binding.value === 'string'
    ? { ...value.c, code: binding.value }
    : { ...value.c, ...binding.value };

  el.setAttribute('data-click-track-value', JSON.stringify({ ...value, c }));
};

/**
 * 解绑事件
 * @param el Element
 *
 */
const removeClickEvent = (el: Element) => {
  el.removeEventListener('click', onClickHandler);
};

const ObserverSet = new Map();

// 观察器对象
const getObserver = (el: Element) => {

  const rootMargin = '0px';
  const threshold = 0.1;

  const observer = new IntersectionObserver((entries: IntersectionObserverEntry[]) => {
    entries.forEach((ioe: IntersectionObserverEntry) => {
      /**
       * isIntersecting：
       * 返回一个布尔值，如果目标元素与交叉区域观察者对象 (intersection observer) 的根相交，则返回 true .
       * 如果返回 true, 则 IntersectionObserverEntry 描述了变换到交叉时的状态;
       * 如果返回 false, 那么可以由此判断，变换是从交叉状态到非交叉状态。
       *
       * intersectionRatio：
       * 返回intersectionRect 与 boundingClientRect 的比例值
       *
       * target:
       * 与根出现相交区域改变的元素
       */
      const {
        target,
        isIntersecting,
      } = ioe;

      if (isIntersecting) {

        // 元素的唯一标记
        const trackValue = target.getAttribute('data-pop-track-value') as string;

        console.debug(`[${new Date().getTime()}]上报埋点数据:`, trackValue);

        const postData = JSON.parse(trackValue);

        getBeaconBehaviors(postData);
      }
    });
  }, {
    // 表示重叠面积占被观察者的比例，从0-1取值，1表示完全被包含, 这里设置为1的时候不生效，所以需要减去一个偏移量
    threshold,
    // 如果元素有边距，那么监听不到最边缘，需要减去边距
    rootMargin,
  });

  observer.observe(el);

  return observer;
};

// /**
//  * 注册事件
//  * @param el
//  * @param binding
//  * @param options
//  */
// const attachPopEvent = (el: HTMLElement, binding: DirectiveBinding, options: EventTrackParams) => {
//   const defaultEventTrackParams = {
//     e: 'pop',
//   };
//   const value = { ...options, ...defaultEventTrackParams };
//   if (typeof binding.value === 'string') {
//     const c = JSON.stringify({ code: binding.value });
//     Object.assign(value, { c });
//   } else if (typeof binding.value === 'object') {
//     Object.assign(value, binding.value);
//   }
//
//   el.setAttribute('data-pop-track-value', JSON.stringify(value));
//   if (!el.id) {
//     el.id = Math.random()
//       .toString(36)
//       .slice(-6);
//   }
//   const server = getObserver(el);
//   ObserverSet.set(el.id, server);
// };

/**
 * 注册事件
 * @param el
 * @param binding
 * @param options
 */
const attachPopEvent = (el: HTMLElement, binding: DirectiveBinding, options: EventTrackParams) => {
  const defaultEventTrackParams = { e: 'pop' };
  const value = { ...options, ...defaultEventTrackParams };

  const c = typeof binding.value === 'string'
    ? { code: binding.value }
    : binding.value;

  Object.assign(value, { c });

  el.setAttribute('data-pop-track-value', JSON.stringify(value));

  if (!el.id) {
    el.id = Math.random().toString(36).slice(-6);
  }

  const server = getObserver(el);
  ObserverSet.set(el.id, server);
};

/**
 * 解绑事件
 * @param el Element
 *
 */
const removePopEvent = (el: Element) => {
  const server = ObserverSet.get(el.id);
  server && server.unobserve(el);
};

export default {
  install: (app: App, options: EventTrackParams): void => {
    // 通用参数
    const defaultValue = {
      ...reportDefaultValue(),
      sid: sessionStorage.getItem(constant.LZ_SHOP_ID),
      opid: sessionStorage.getItem(constant.LZ_ACTIVITY_ID),
      at: sessionStorage.getItem(constant.LZ_ACTIVITY_TYPE),
      uid: window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN),
      ...options,
    };
    app.directive(clickDirectiveName, {
      mounted(el, binding) {
        removeClickEvent(el);
        attachClickEvent(el, binding, defaultValue);
      },
      // 卸载绑定元素的父组件时调用
      unmounted(el: Element) {
        removeClickEvent(el);
      },
      deep: true,
    });
    app.directive(popDirectiveName, {
      mounted(el, binding) {
        removePopEvent(el);
        attachPopEvent(el, binding, defaultValue);
      },
      // 卸载绑定元素的父组件时调用
      unmounted(el: Element) {
        removePopEvent(el);
      },
      deep: true,
    });
  },
};
