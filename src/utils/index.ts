import constant from '@/utils/constant';
import { reportPV } from '@/utils/trackEvent/jdReport';
import { loadScript, parsePathByPattern, removeTokenFromUrl } from '@/utils/platforms/client';
import { BaseInfo } from '@/types/BaseInfo';
import { ActivityStatus } from '@/types/ActivityStatus';
import { InitRequest } from '@/types/InitRequest';
import { InitResponse } from '@/types/InitResponse';
import { InitPreviewResponse } from '@/types/InitPreviewResponse';

import CLIENT_TYPE, { getClientType, isPC } from '@/utils/platforms/clientType';
import CrmUtil from './products/crm';
import DOMAIN, { exchangeSubStr } from '@/utils/platforms/domain';
import { ActivityBaseInfo } from '@/types/ActivityBaseInfo';
import { UserInfo } from './products/types/UserInfo';
import { isMiniProgram } from '@/utils/platforms/wx';
import { DecoData } from '@/types/DecoData';
import { getBeaconBehaviors, lzReportClick, reportDefaultValue } from './trackEvent/lzReport';
import { setThreshold } from '@/utils/setThreshold';
import { httpRequest } from '@/utils/service';
import { gotoErrorPage } from '@/utils/errorHandler';
import { Handler } from '@/utils/handle';
import { goLinkCard } from '@/utils/openCard';

const isProd = process.env.NODE_ENV === 'production';

const handler = Handler.getInstance();

// 显示控制台
const enableVConsole = async (): Promise<void> => {
  await loadScript('https://lzcdn.dianpusoft.cn/vConsole/3.14.6/vconsole.min.js');
  if (window.VConsole) {
    const vc = new window.VConsole();
  }
};

// 检查调试状态
const checkDebug = (pathParams: { [propName: string]: string }) => {
  if ('debug' in pathParams && pathParams.debug) {
    // 调试模式
    window.debug = true;
    enableVConsole().then();
  }
};

// pv埋点
const pv = (config: InitRequest, baseInfo: BaseInfo, pathParams: { [propName: string]: string }) => {
  if (isProd) {
    try {
      const { jdActivityId, jdActivityType, shopId } = baseInfo;

      window.sessionStorage.setItem(constant.LZ_JD_ACTIVITY_ID, jdActivityId);

      // 子午线pv
      reportPV({
        pin: window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) ?? '',
        shopId,
        jdActivityId,
        jdActivityType,
        shareUserId: pathParams.shareUserId,
      }).then();
      // 陆泽埋点
      getBeaconBehaviors({
        ...reportDefaultValue(),
        sid: sessionStorage.getItem(constant.LZ_SHOP_ID),
        opid: sessionStorage.getItem(constant.LZ_ACTIVITY_ID),
        at: sessionStorage.getItem(constant.LZ_ACTIVITY_TYPE),
        uid: window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) ?? '',
        e: 'enter',
      });
    } catch (e) {
      console.error(e);
    }
  }
};

// 新增会员买点
const newCustomTrackingEvent = async () => {
  try {
    await httpRequest.post('/common/add/member');
  } catch (e) {
    console.log(e);
  }
};
// 半屏开卡数据埋点≈新增会员埋点
const halfScreenOpenCard = async (baseInfo: BaseInfo) => {
  try {
    await httpRequest.post('add/member/half');
  } catch (e) {
    console.log(e);
  }
};

// 前置检查器-更换域名
const beforeInit = () => {
  const clientType = getClientType();
  if (clientType === CLIENT_TYPE.JDAPP) return;
  if (isPC()) return;
  const url = exchangeSubStr(window.location.href, DOMAIN.COMMON, DOMAIN.PROTECT);
  if (isProd) {
    // 如果是微信并且不是微信小程序||QQ||PC 的时候，切换域名
    if (window.location.href !== url && ((clientType === CLIENT_TYPE.WECHAT && !isMiniProgram()) || clientType === CLIENT_TYPE.QQ || isPC())) {
      window.location.href = url;
      throw new Error('current domain is not support');
    }
  }
};

const checkParams = (params: { shopId: string; activityId: string }) => {
  if (!params.shopId) {
    throw new Error('店铺ID参数错误');
  }
  if (!params.activityId) {
    throw new Error('活动ID参数错误');
  }
};

const getVariables = (config: InitRequest, pathParams: { [p: string]: string }) => {
  // 活动ID
  const activityId: string = config.activityId ?? pathParams.activityId;

  // 活动类型
  const activityType: string = config.activityType ?? pathParams.activityType ?? '99';

  // venderId
  const shopId: string = config.shopId ?? pathParams.shopId;

  // 模板code
  const templateCode: string = config.templateCode ?? pathParams.templateCode;
  return {
    activityId,
    activityType,
    shopId,
    templateCode,
  };
};
interface Info {
  activityType: string;
  templateCode: string;
}

const isCPBActivity = ({ activityType, templateCode }: Info) => {
  const cppActivityPath = ['10015/2001', '10049/2001', '10109/2001', '10110/2001', '21003/1001', '21003/2001', '21004/1001', '30003/2001', '39001/2001', '90001/1001', '90001/2001', '90004/1001', '90006/1001', '90006/1002', '900010/1001', '92002/1001', '92002/1002'];
  return cppActivityPath.includes(`${activityType}/${templateCode}`);
};
const isBosidengActivity = ({ activityType }: Info) => activityType.toString().startsWith('8');

const checkThreshold = async (info: Info) => {
  const { data }: any = await httpRequest.post('/common/getThreshold');
  data.thresholdResponseList = data.thresholdResponses;
  data.isCpb = isCPBActivity(info);
  data.isBosiden = isBosidengActivity(info);
  if (info.activityType.toString() === '30006') {
    data.className = 'common-message-30006';
  }
  setThreshold({ ...data, ...info });
  if (data.showThreshold) {
    handler.trigger('onThresholdOpen').then();
  }
};

/**
 * 初始化页面
 * @param config 页面初始化配置
 * @returns InitResponse
 */
export const init = async (config: InitRequest): Promise<InitResponse> => {
  console.group('init');
  console.log(config, 'init初始化==========');
  const { urlPattern } = config;

  sessionStorage.setItem('duration_time', new Date().getTime().toString());

  try {
    // 前置检查
    await beforeInit();

    const pathParams: { [propName: string]: string } = parsePathByPattern(urlPattern);

    const { activityId, activityType, shopId, templateCode } = getVariables(config, pathParams);

    // 检查调试状态
    checkDebug(pathParams);

    // 检查参数,参数不正确会throw异常，中断代码
    await checkParams({
      activityId,
      shopId,
    });

    // 店铺id
    sessionStorage.setItem(constant.LZ_SHOP_ID, shopId);
    // 活动id
    sessionStorage.setItem(constant.LZ_ACTIVITY_ID, activityId);
    // 活动模板
    sessionStorage.setItem(constant.LZ_TEMPLATE_CODE, templateCode);
    // 活动类型
    sessionStorage.setItem(constant.LZ_ACTIVITY_TYPE, activityType);

    // 获取用户pin
    let userInfo: UserInfo;
    sessionStorage.removeItem('mockCode');
    // 91005 中奖查询
    if (pathParams.mockCode) {
      sessionStorage.setItem('mockCode', pathParams.mockCode);
      userInfo = await CrmUtil.mockLogin({
        activityId,
        activityType,
        templateCode,
        shopId,
        mockCode: pathParams.mockCode,
      });
    } else {
      userInfo = await CrmUtil.getPin({
        activityId,
        activityType,
        templateCode,
        shopId,
        loginType: config.loginType,
        prd: pathParams.prd ? pathParams.prd : '',
      });
    }

    if (!activityType.startsWith('9')) {
      checkThreshold({ activityType, templateCode }).then();
    }
    await CrmUtil.initShare(config, pathParams);

    // 获取活动的基础信息
    const activityBaseInfo: ActivityBaseInfo = await CrmUtil.getActivityBaseInfo();

    // 获取活动装修数据
    const decoData: DecoData = await CrmUtil.getActivityConfig();

    // 设置活动标题
    document.title = activityBaseInfo?.activityName;

    // 活动状态
    const status: ActivityStatus = CrmUtil.getActivityStatus({ ...activityBaseInfo });

    // 获取活动基础信息
    const baseInfo: BaseInfo = {
      ...activityBaseInfo,
      activityId,
      shopId,
      activityType,
      status,
    };

    // const urlParams = new URLSearchParams(window.location.search);
    // const isJoin = urlParams.get('isJoin');
    if (pathParams?.isJoin) {
      newCustomTrackingEvent().then();
    }

    // 获取大促公告
    await CrmUtil.getNotice();

    // 大促icon动态配置
    await CrmUtil.setIcon();

    // 互动城icon配置
    CrmUtil.setInteractCityIcon(activityBaseInfo?.assembleStatus);

    // 特定活动类型 不设置底部
    const activityTypeNotSetFooter = ['95008', '39001', '99201', '99203', '92002'];
    // 5为活动类型为店铺礼包，不设置底部
    if (!activityType.toString().startsWith('5') && !activityTypeNotSetFooter.includes(activityType)) {
      CrmUtil.setFooter();
    }

    // 记录pv数据上报（异步）
    pv(config, baseInfo, pathParams);

    // 从上一页返回时，重新获取数据
    if (config.backActRefresh) {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          window.location.reload();
          console.log('页面再次被展示');
        }
      });
    }

    window.addEventListener('beforeunload', () => {
      console.log('页面离开');
      const durationTime = (new Date().getTime() - Number(sessionStorage.getItem('duration_time') ?? '0')) / 1000;
      lzReportClick({
        c: JSON.stringify({
          code: 'v_duration',
          value: durationTime,
        }),
        e: 'enter',
      });
    });

    // 半屏开卡回调处理
    window.openMemberCallback = async (result: any) => {
      try {
        let resultObj: any;
        if (typeof result === 'string') {
          resultObj = JSON.parse(result);
        } else {
          resultObj = result;
        }
        if (resultObj.status === 0 || resultObj.status === '0') {
          let typeData = resultObj.data;
          if (typeof typeData === 'string') {
            try {
              typeData = JSON.parse(typeData);
            } catch (error) {
              console.error('解析typeData失败:', error);
              throw new Error('解析typeData失败000');
            }
          }
          console.log('resultObj', typeData);
          if (typeData.type === 'layerClose') {
            httpRequest.post('/common/add/member/half');
            // 获取活动的基础信息
            const { memberLevel, openCardLink }: ActivityBaseInfo = await CrmUtil.getActivityBaseInfo();
            const last5Chars = openCardLink.slice(-5);
            if (last5Chars === '10115') {
              window.location.reload(); // 强制刷新页面
            } else {
              // 会员等级不为空，则上报自定义事件并刷新页面
              // eslint-disable-next-line no-lonely-if
              if (memberLevel) {
                // 刷新当前页面
                window.location.reload();
              }
            }
          }
        } else if (resultObj.status === -1 || resultObj.status === '-1') {
          console.error('半屏开卡错误:', resultObj.msg);
          throw new Error('半屏开卡错误:');
        } else if (resultObj.status === -2 || resultObj.status === '-2') {
          console.error('半屏开卡错误，方法不存在');
          throw new Error('半屏开卡错误，方法不存在');
        } else {
          throw new Error('半屏开卡错误，兜底错误');
        }
      } catch (error) {
        goLinkCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      }

    };

    return {
      userInfo,
      baseInfo,
      pathParams,
      decoData,
    };
  } catch (e: any) {
    console.error(e.message);
    if (e.message !== 'toLogin') {
      getBeaconBehaviors({
        c: JSON.stringify(e),
        e: 'error',
      });
      gotoErrorPage(0);
    }
    throw e;
  } finally {
    removeTokenFromUrl();
    console.groupEnd();
  }
};

/**
 * 初始化页面
 * @param config 预览页面初始化配置
 * @returns InitResponse
 */
export const initPreview = async (config: InitRequest): Promise<InitPreviewResponse> => {
  const { urlPattern } = config;
  try {
    console.group('initPreview');
    const pathParams: { [propName: string]: string } = parsePathByPattern(urlPattern);
    // 检查调试状态
    checkDebug(pathParams);
    const res = await CrmUtil.getConfig({
      id: pathParams.id,
      type: pathParams.type,
    });
    const activityData = res.activityData ? JSON.parse(res.activityData) : '';
    const decoData = res.decoData ? JSON.parse(res.decoData) : '';
    console.log('activityData', activityData);
    console.log('decoData', decoData);
    return {
      pathParams,
      activityData,
      decoData,
    };
  } finally {
    console.groupEnd();
  }
};

/**
 * 初始化页面只登陆
 * @param config 页面初始化配置
 * @returns InitResponse
 */
export const initOnlyLogin = async (config: InitRequest) => {
  console.group('init');
  const { urlPattern } = config;

  sessionStorage.setItem('duration_time', new Date().getTime().toString());

  try {
    // 前置检查
    await beforeInit();

    const pathParams: { [propName: string]: string } = parsePathByPattern(urlPattern);

    const { activityId, activityType, shopId, templateCode } = getVariables(config, pathParams);

    // 检查调试状态
    checkDebug(pathParams);

    // 检查参数,参数不正确会throw异常，中断代码
    await checkParams({
      activityId,
      shopId,
    });

    // 店铺id
    sessionStorage.setItem(constant.LZ_SHOP_ID, shopId);
    // 活动id
    sessionStorage.setItem(constant.LZ_ACTIVITY_ID, activityId);
    // 活动模板
    sessionStorage.setItem(constant.LZ_TEMPLATE_CODE, templateCode);
    // 活动类型
    sessionStorage.setItem(constant.LZ_ACTIVITY_TYPE, activityType);

    // 获取用户pin
    let userInfo: UserInfo;

    if (pathParams.mockCode) {
      userInfo = await CrmUtil.mockLogin({
        activityId,
        activityType,
        templateCode,
        shopId,
        mockCode: pathParams.mockCode,
      });
    } else {
      userInfo = await CrmUtil.getPin({
        activityId,
        activityType,
        templateCode,
        shopId,
      });
    }

    // CrmUtil.set618Icon();

    // 5为活动类型为店铺礼包，不设置底部
    if (!activityType.toString().startsWith('5')) {
      CrmUtil.setFooter();
    }

    // 记录pv数据上报（异步）
    // 陆泽埋点
    getBeaconBehaviors({
      ...reportDefaultValue(),
      sid: sessionStorage.getItem(constant.LZ_SHOP_ID),
      opid: sessionStorage.getItem(constant.LZ_ACTIVITY_ID),
      at: sessionStorage.getItem(constant.LZ_ACTIVITY_TYPE),
      uid: window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) ?? '',
      e: 'enter',
    });

    // 从上一页返回时，重新获取数据
    if (config.backActRefresh) {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          window.location.reload();
          console.log('页面再次被展示');
        }
      });
    }

    window.addEventListener('beforeunload', () => {
      console.log('页面离开');
      const durationTime = (new Date().getTime() - Number(sessionStorage.getItem('duration_time') ?? '0')) / 1000;
      lzReportClick({
        c: JSON.stringify({
          code: 'v_duration',
          value: durationTime,
        }),
        e: 'enter',
      });
    });

    return {
      userInfo,
      pathParams,
    };
  } catch (e: any) {
    console.error(e.message);
    if (e.message !== 'toLogin') {
      getBeaconBehaviors({
        c: JSON.stringify(e),
        e: 'error',
      });
      gotoErrorPage(0);
    }
    throw e;
  } finally {
    removeTokenFromUrl();
    console.groupEnd();
  }
};

export const isPreview = window.location.pathname.includes('preview');

export default {
  // 初始化
  init,
  // 预览初始化
  initPreview,
  // 初始化只登录
  initOnlyLogin,
  // 获取客户端类型
  getClientType,
};
