import { reportViewShopEvent, reportViewSkuEvent } from '../trackEvent/jdReport';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

/**
 * 跳转到店铺首页
 * @param shopId
 */
export async function gotoShopPage(shopId: string | number): Promise<void> {
  if (shopId) {
    lzReportClick('v_shop');
    reportViewShopEvent(shopId);
    window.jmfe.toShop(`${shopId}`);
  }
}

/**
 * 跳转到商品详情页面
 * @param skuId
 */
export async function gotoSkuPage(skuId: string | number): Promise<void> {
  if (skuId) {
    reportViewSkuEvent(skuId);
    lzReportClick({
      c: JSON.stringify({
        code: 'v_sku',
        value: skuId,
      }),
    });
    window.jmfe.toSku(`${skuId}`);
  }
}

/**
 * 跳转到商品详情页面
 */
export function exchangePlusOrAiqiyi() {
  window.jmfe.toAny('http://asset-m.jd.com/');
}

/**
 * 跳转到购物车并加购商品
 * @param skuId
 */
export function addSkuToCart(skuId: string | number | number[] | string[]): void {
  let skuList: string[] = [];
  if (typeof skuId === 'number' || typeof skuId === 'string') {
    skuList = [String(skuId)];
  } else if (Array.isArray(skuId)) {
    const skuIds: any[] = skuId;
    skuList = skuIds.map((id: any) => String(id));
  }
  window.jmfe.toMyCart({
    extraOpenAppParams: {
      skuList,
    },
  });
}
