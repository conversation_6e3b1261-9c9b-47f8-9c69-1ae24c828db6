const appVersion = (window as any).jmfe.getAppVersion('jd');
interface PopOpenStyleParams {
  targetType?: number;
  heightRateFromContainer?: number;
  layoutType?: number;
  backgroundColor?: string;
  pageMultiTimes?: number;
  needCache?: number;
  needAutoClose?: number;
  hideNativeCloseBtn?: number;
}

const parseUrl = (url: string): { domain: string; params: { [key: string]: string } } => {
  const urlObj = new URL(url);
  const domain = urlObj.origin;
  const params = Object.fromEntries(urlObj.searchParams.entries());

  return { domain, params };
};
const objToStr = (obj: any) => Object.entries(obj)
  .map(([key, value]) => `${key}=${value}`)
  .join('&');

export const goLinkCard = (url: any, returnUrl = null) => {
  const { domain, params } = parseUrl(url);
  const str = objToStr(params);
  window.location.href = `https://shopmember.m.jd.com/shopcard/?${str}`;
};

// 构建开卡样式
const popOpenStyle = (urlParams: string, { targetType, heightRateFromContainer, layoutType, backgroundColor, pageMultiTimes, needCache, needAutoClose, hideNativeCloseBtn }: PopOpenStyleParams) => JSON.stringify({
  url: encodeURIComponent(`https://pages.jd.com/member/card?${urlParams}&memberSceneType=xview&closeBtn=0`),
  // targetName: window.location.href,
  targetType: targetType || 2,
  heightRateFromContainer: heightRateFromContainer || 0.7,
  layoutType: layoutType || 1,
  backgroundColor: backgroundColor || '0000009b',
  pageMultiTimes: pageMultiTimes || 1,
  needCache: needCache || 0,
  needAutoClose: needAutoClose || 1,
  hideNativeCloseBtn: hideNativeCloseBtn || 0,
});
const popOpenCard = (url: string, styles: { targetType: any; heightRateFromContainer: any; layoutType: any; backgroundColor: any; pageMultiTimes: any; needCache: any; needAutoClose: any; hideNativeCloseBtn: any }) => {
  if ((window as any).XWebView) {
    const { domain, params } = parseUrl(url);
    if (params?.venderId) {
      if (params.returnUrl) {
        delete params.returnUrl;
      }
      const urlParams = objToStr(params);
      (window as any).XWebView.callNative('JDXViewPlugin', 'showXView2', popOpenStyle(urlParams, styles), 'openMemberCallback', '1');
    } else {
      goLinkCard(url);
    }
  } else {
    goLinkCard(url);
  }
};

export const openCard = (url: string, styles: any = {}, activityType: any = '') => {
  if (appVersion > '13.6.3') {
    // 半屏开卡
    popOpenCard(url, styles);
  } else if (activityType === '10115') {
    window.location.href = url;
  } else {
    goLinkCard(url);
  }
};

export default openCard;
