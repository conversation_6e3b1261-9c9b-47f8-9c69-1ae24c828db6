<template>
  <VanPopup v-model:show="limitPopup" :closeOnClickOverlay="false" position="bottom" z-index="9999">
    <div class="common-limit">
      <div class="common-limit-close-btn" @click="close">
        <!-- <img class="w-3" src="//img10.360buyimg.com/imgzone/jfs/t1/144148/22/31691/1246/63906006Ea0b5df70/2d6a63424d902b10.png" alt="" /> -->
      </div>
      <div class="common-limit-title"></div>
      <div class="common-limit-content">
        <div v-for="(item, index) in data" :key="index" class="common-limit-item">
          <img :src="iconMap[item.icon]" alt="" class="icon" />
          <div class="common-limit-item-text">{{ item.thresholdContent }}</div>
          <div v-if="item.btnContent && item.thresholdStatus === 0" class="common-limit-item-btn">
            <div @click="eventClick(item.type)" class="common-limit-btn">{{ item.btnContent }}</div>
            <div v-if="item.type === 3 && item.btnContent.indexOf('去下单') > -1" class="common-search-btn" @click="searchOrderList">订单查询</div>
          </div>
        </div>
      </div>
    </div>
  </VanPopup>
  <VanPopup v-model:show="searchOrderPopup" :closeOnClickOverlay="false" position="bottom" z-index="10000">
    <div class="order-list">
      <div class="order-list-close-btn" @click="closeOrderSearch"></div>
      <div class="order-list-title">活动订单查询</div>
      <div class="order-input">
        <div class="orderInput-div-all">
          <!-- <div class="order-div">订单号：</div> -->
          <div class="input-div">
            <input maxLength="20" placeholder="请输入订单号" v-model="order" @input="validateInput"/>
          </div>
        </div>
        <div class="no-order-content" v-show="noOrder">
          <div>
            <div>未查到当前订单记录，请稍后查询</div>
          </div>
        </div>
      </div>
      <div class="order-list-btn">
        <div class="order-help">可在【我的】-【全部订单】-【订单信息】查询您的订单号</div>
        <div class="order-desc">部分订单状态可存在延迟，订单信息以京东APP信息为准</div>
        <div class="bottom-search-btn">
          <div class="btn" @click="orderClick">订单查询</div>
        </div>
      </div>
    </div>
  </VanPopup>

</template>
<script setup lang="ts">
import { defineEmits, defineProps, inject, ref, watch, onMounted } from 'vue';
// import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { Handler } from '@/utils/handle';
import useThreshold from '@/hooks/useThreshold';
import openCard from '@/utils/openCard';

const baseInfo: any = inject('baseInfo') as BaseInfo;
const handler = Handler.getInstance();

const props = defineProps(['data', 'show']);
const emits = defineEmits(['update:show', 'openShowGoShop']);

const data = baseInfo.thresholdResponseList;

const limitPopup = ref(props.show);
const close = () => {
  limitPopup.value = false;
  emits('update:show', false);
};

watch(
  () => props.show,
  (val) => {
    limitPopup.value = val;
  },
);

// 图标map
const iconMap = {
  1: '//img10.360buyimg.com/imgzone/jfs/t1/230783/34/7525/5292/6577c1d3F23e4fe6c/22d5b4b4e51c0760.png', // 活动未开始
  2: '//img10.360buyimg.com/imgzone/jfs/t1/230783/34/7525/5292/6577c1d3F23e4fe6c/22d5b4b4e51c0760.png', // 活动已结束
  3: '//img10.360buyimg.com/imgzone/jfs/t1/237664/17/7124/4981/6577c1d3F5ca630dd/a5aaeabd68457951.png', // 人群包
  4: '//img10.360buyimg.com/imgzone/jfs/t1/233389/38/7373/7483/6577c1d3Fc2458af9/e8980302d51cf663.png', // 入会
  5: '//img10.360buyimg.com/imgzone/jfs/t1/230783/34/7525/5292/6577c1d3F23e4fe6c/22d5b4b4e51c0760.png', // 关注店铺
  6: '//img10.360buyimg.com/imgzone/jfs/t1/237664/17/7124/4981/6577c1d3F5ca630dd/a5aaeabd68457951.png', // 付费会员
  7: '//img10.360buyimg.com/imgzone/jfs/t1/226847/23/6535/6200/6577c1d2Fc48a7813/99c95dda33f5969a.png', // 会员等级
  8: '//img10.360buyimg.com/imgzone/jfs/t1/237866/5/7268/7989/6577c1d3F7c6d9e1e/daf440c21d664974.png', // 关注并开卡
  9: '//img10.360buyimg.com/imgzone/jfs/t1/233389/38/7373/7483/6577c1d3Fc2458af9/e8980302d51cf663.png', // plus会员
  101: '//img10.360buyimg.com/imgzone/jfs/t1/233872/38/7045/5017/6577c1d3F35da2ef1/ad10ad4af3f3f15e.png', // 订单
};

const fellowShop = async () => {
  lzReportClick('fellowShop');
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  try {
    await httpRequest.post('/common/followShop');
    showToast({
      message: '关注成功',
      forbidClick: true,
    });
    return true;
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
    return false;
  }
};
const eventClick = async (type: number) => {
  switch (type) {
    case 1:
      // 去开卡
      lzReportClick('join');
      openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      // window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;`;
      break;
    case 2:
      // 关注店铺
      await fellowShop();
      setTimeout(() => {
        window.location.reload();
      }, 1500);
      break;
    case 3:
      // 进店铺
      await handler.trigger('onGoShopOpen');
      break;
    case 4:
      // 关注店铺并立即入会
      await fellowShop();
      lzReportClick('join');
      openCard(`${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`);
      // window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;`;
      break;
    default:
      console.log('~');
  }
};

const openThreshold = () => {
  limitPopup.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
  });
  return baseInfo.thresholdResponseList.length;
};

const searchOrderPopup = ref<boolean>(false);
const order = ref(''); // 需要校验的订单号
const noOrder = ref<boolean>(false);
const searchOrderList = () => {
  searchOrderPopup.value = true;
  limitPopup.value = false;
};
const reloadPage = () => {
  noOrder.value = false;
  searchOrderPopup.value = false;
  order.value = '';
  limitPopup.value = false;

  setTimeout(() => {
    window.location.reload();
  }, 500);
};
const validateInput = (event) => {
  const { value } = event.target;
  order.value = value.replace(/[^a-zA-Z0-9]/g, '');
};
// 校验订单号进行查询
const orderClick = async () => {
  if (!order.value.trim()) {
    showToast('请输入订单号');
    return;
  }
  const regex = /^[a-zA-Z0-9]*$/;
  if (!regex.test(order.value)) {
    showToast('订单号不符合规则，请重新输入');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.get('/common/getOrderByIdV1', { params: { orderId: order.value } });

    if (data?.length) {
      showToast('查询成功');
      reloadPage();
    } else {
      closeToast();
      noOrder.value = true;
    }
  } catch (e) {
    showToast(e.message);
    noOrder.value = true;
  }
};
const closeOrderSearch = () => {
  reloadPage();
};

onMounted(() => {
  handler.on('onThresholdOpen', () => openThreshold());
  handler.on('onThresholdIf', () => baseInfo.thresholdResponseList.length);
});
</script>
<style lang="scss" scoped>
.common-limit {
  position: relative;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/194898/24/47042/309499/6673c73dFfab59c75/7e3e3fce130ff090.png) no-repeat;
  background-size: 100%;
  padding: 0.3rem;
  display: flex;
  align-items: center;
  flex-direction: column;
  .common-limit-close-btn {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 0.4rem;
    height: 0.4rem;
  }
  .common-limit-title {
    height: 1.3rem;
  }
  .common-limit-content {
    width: 100%;
    height: 5.76rem;
    overflow: auto;
    .common-limit-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 0.3rem;
      background-color: #fff;
      border-radius: 0.2rem;
      &:not(:last-child) {
        margin-bottom: 0.1rem;
      }

      .icon {
        width: 0.8rem;
        height: 0.8rem;
        object-fit: contain;
        margin-right: 0.2rem;
      }
      .common-limit-item-text {
        flex: 1;
        font-size: 0.24rem;
      }
      .common-limit-item-btn {
        text-align: center;
        .common-search-btn {
          font-size: 0.24rem;
          color: #f86987;
          text-decoration: underline;
        }
      }
      .common-limit-btn {
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/221972/13/37018/6534/6577c1d1F7c1ee4bc/7c921f92ee61e724.png) no-repeat;
        background-size: 100%;
        width: 1.98rem;
        height: 0.92rem;
        text-align: center;
        line-height: 0.9rem;
        color: #fff;
        font-size: 0.24rem;
      }
    }
  }
}
.order-list {
  position: relative;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/266058/22/12624/68099/6788d3f5F4756503d/41169cc8cc339743.png) no-repeat;
  background-size: 100%;
  padding: 0.3rem;
  display: flex;
  align-items: center;
  flex-direction: column;
  border-radius: 0.5rem 0.5rem 0 0;

  .order-list-close-btn {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 0.4rem;
    height: 0.4rem;
  }
  .order-list-title {
    font-size: 0.4rem;
    color: #ffffff;
  }

  .order-input {
    width: 100%;
    background-color: #ffffff;
    padding: 0.3rem;
    margin: 0.6rem 0;
    border-radius: 0.3rem;
    height: 2rem;

    .orderInput-div-all {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0.4rem;
      .order-div {
        color:#000000;
        font-size: 0.27rem;
      }
      .input-div {
        margin-left: 0.1rem;
        input {
          background:#ffffff;
          // border: 0.01rem solid #000000;
          border-radius: 0.2rem;
          font-size: 0.27rem;
          padding: 0 0.44rem;
          height: 0.6rem;
          width: 4rem;
          line-height: 0.6rem;
        }
      }
    }
    .no-order-content {
      color: #5a5959;
      font-size: 0.24rem;
      // margin-top: 0.3rem;
      text-align: center;
    }
  }

  .order-list-btn {
    font-size: 0.24rem;
    color: #ffffff;
    .order-help {
      // color: #ffffff;
    }
    .order-desc {
      line-height: 0.5rem;
    }
    .bottom-search-btn {
      // color: #ffffff;
      display: flex;
      justify-content: center;
      .btn {
        // width: 2rem;
        // background-color: #169bd5;
        // padding: 0.1rem 0;
        // border-radius: 0.2rem;
        // text-align: center;
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/221972/13/37018/6534/6577c1d1F7c1ee4bc/7c921f92ee61e724.png) no-repeat;
        background-size: 100%;
        width: 1.98rem;
        height: 0.92rem;
        text-align: center;
        line-height: 0.9rem;
        color: #fff;
        font-size: 0.24rem;
      }
    }
  }
}
</style>
