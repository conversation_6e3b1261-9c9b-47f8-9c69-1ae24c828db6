/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023/11/14 14:27
 * Description: 通用弹框
 */
import { showDialog } from 'vant';

interface Props {
  title: string;
  message: string;
  className?: string
}
const useMessage = (props: Props) => {
  showDialog({
    title: props.title,
    message: props.message,
    className: props.className || 'common-message',
  }).then((): void => {
    console.log('close');
  });
};
export default useMessage;
