const { execSync } = require('child_process');
let fs = require('fs');
let path = require('path');

// 定义需要打包的特定路径
let specificPaths = [
  // '90001/1001',
  // '90001/2001',
  // '90003/1001',
  // '90003/2001',
  // '90003/2002',
  // '90004/1001',
  // '90005/1001',
  // '90006/1001',
  // '90006/1002',
  // '90007/1001',
  // '90007/2001',
  // '90007/2002',
  // '90007/2003',
  // '90007/2004',
  // '90007/2005',
  // '90008/2001',
  // '90009/1001',
  // '90010/1001',
  // '90012/2001',
  // '90013/2001',
  // '90031/1001',
  // '90077/1001',
  // '90077/1002',
  // '90077/1003',
  // '90077/1004',
  // '90077/1005',
  // '90090/1001',
  // '90090/1002',
  // '90090/1003',
  // '90090/1004',
  // '90090/1005',
  // '90101/2001',
  // '91001/1001',
  // '91001/1002',
  // '91002/1001',
  // '94001/1001',
  // '94002/1001',
  // '94003/1001',
  // '94004/1001',
  // '94005/1001',
  // '95001/1001',
  // '95001/2001',
  // '95001/2002',
  // '95002/1001',
  // '95002/2001',
  // '95002/2002',
  // '95002/2003',
  // '95003/1001',
  // '95003/2001',
  // '95003/2002',
  // '95003/2003',
  // '95003/2004',
  // '95003/2005',
  // '95004/1001',
  // '95004/2001',
  // '95004/2002',
  // '95004/2003',
  // '95005/1001',
  // '95005/2001',
  // '95005/2002',
  // '95005/2003',
  // '95006/1001',
  // '95006/2001',
  // '95006/2002',
  // '95007/1001',
  // '95007/2001',
  // '95007/2002',
  // '95008/1001',
  // '95008/2001',
  // '99001/1001',
  // '99001/1002',
  // '99002/1001',
  // '99003/2001',
  // '99201/1001',
  // '99202/1001',
  // '99203/1001',
  '90077/1001',
  '90077/1002',
  '90077/1003',
  '90077/1004',
  '90077/1005',
  '90077/1006',
  '90090/1001',
  '90090/1002',
  '90090/1003',
  '90090/1004',
  '90090/1005',
  '90077/1001/preview',
  '90077/1002/preview',
  '90077/1003/preview',
  '90077/1004/preview',
  '90077/1005/preview',
  '90077/1006/preview',
  '90090/1001/preview',
  '90090/1002/preview',
  '90090/1003/preview',
  '90090/1004/preview',
  '90090/1005/preview',
];

// 基础文件夹路径
let basePath = 'src/pages';

function startBuild() {
  console.log('开始');
  console.log('process.argv', process.argv[3]);

  // 验证每个特定路径是否存在index.ts文件，并收集有效路径
  let dirList = specificPaths.filter((entry) => {
    let fullPath = path.join(basePath, entry, 'index.ts');
    return fs.existsSync(fullPath);
  });

  console.log(`共 ${dirList.length} 个文件`);
  const cmd = dirList.map((entry) => `npm run build:${process.argv[3]} ${entry}`)
    .join(' && ');
  console.log('cmd', cmd);
  try {
    execSync(` ${cmd}`, { stdio: [0, 1, 2] }, (error) => {
      if (error) {
        console.error(error);
      }
    });
  } catch (e) {
    console.log('遇到错误', e);
    process.exit(1);
  }
}

startBuild();
